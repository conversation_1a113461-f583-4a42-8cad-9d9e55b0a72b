#!/usr/bin/env python3
"""
Setup script for LLM Modeling Metrics package.
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read version from __init__.py
def get_version():
    import re
    with open("llm_modeling_metrics/__init__.py", "r") as fp:
        content = fp.read()
        version_match = re.search(r'^__version__\s*=\s*[\'"]([^\'"]*)[\'"]', content, re.MULTILINE)
        if version_match:
            return version_match.group(1)
        raise RuntimeError("Unable to find version string.")

# Base requirements
install_requires = [
    "transformers>=4.30.0",
    "huggingface-hub>=0.15.1",
    "python-dotenv>=0.19.0",
    "tabulate>=0.9.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.2",
    "numpy>=1.21.0",
    "requests>=2.25.0",
    "watchdog>=3.0.0",
    "pyyaml>=6.0.0",
]

# Optional dependencies for different use cases
extras_require = {
    "web": [
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.20.0",
        "pydantic>=2.0.0",
        "jinja2>=3.1.0",
        "python-multipart>=0.0.6",
    ],
    "dev": [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0",
        "pytest-asyncio>=0.21.0",
        "black>=23.0.0",
        "flake8>=6.0.0",
        "mypy>=1.0.0",
        "pre-commit>=3.0.0",
        "jupyter>=1.0.0",
        "notebook>=6.5.0",
    ],
    "visualization": [
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "plotly>=5.0.0",
    ],
}

# All extras combined
extras_require["all"] = list(set(sum(extras_require.values(), [])))

setup(
    name="llm-modeling-metrics",
    version=get_version(),
    author="LLM Modeling Metrics Team",
    author_email="<EMAIL>",
    description="A comprehensive Python library for analyzing computational requirements and performance characteristics of Large Language Models",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/llm-modeling-metrics",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/llm-modeling-metrics/issues",
        "Documentation": "https://llm-modeling-metrics.readthedocs.io/",
        "Source Code": "https://github.com/your-org/llm-modeling-metrics",
    },
    packages=find_packages(exclude=["tests*", "docs*", "examples*"]),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=install_requires,
    extras_require=extras_require,
    include_package_data=True,
    package_data={
        "llm_modeling_metrics": [
            "web/static/**/*",
            "web/templates/**/*",
        ],
    },
    entry_points={
        "console_scripts": [
            "llm-metrics=llm_modeling_metrics.cli:main",
        ],
    },
    keywords=[
        "llm",
        "large language models",
        "machine learning",
        "deep learning",
        "model analysis",
        "performance metrics",
        "flops",
        "memory analysis",
        "mixture of experts",
        "moe",
        "transformer",
    ],
    zip_safe=False,
)