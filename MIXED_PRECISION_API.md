# Mixed Precision API Endpoints

This document describes the enhanced API endpoints that support mixed precision configurations for LLM memory analysis.

## Overview

The mixed precision support allows different model components to use different data types independently, enabling fine-grained control over memory usage and computational efficiency.

## Enhanced Endpoints

### 1. Enhanced Model Analysis (`/api/analyze`)

**Method:** `POST`

**New Parameters:**
- `expert_parameter_dtype`: Data type for expert parameters in MoE models
- `attention_parameter_dtype`: Data type for attention parameters

**Example Request:**
```json
{
  "model_names": ["meta-llama/Meta-Llama-3-8B-Instruct"],
  "sequence_length": 2048,
  "batch_size": 1,
  "precision": "fp16",
  "weight_dtype": "bf16",
  "activation_dtype": "bf16", 
  "kv_cache_dtype": "fp8",
  "expert_parameter_dtype": null,
  "attention_parameter_dtype": "bf16",
  "training": false
}
```

### 2. Enhanced Memory Analysis (`/api/memory/analyze`)

**Method:** `POST`

**New Parameters:**
- `expert_parameter_dtype`: Data type for expert parameters in MoE models
- `attention_parameter_dtype`: Data type for attention parameters

**Example Request:**
```json
{
  "model_names": ["deepseek-ai/DeepSeek-V3"],
  "sequence_length": 2048,
  "batch_size": 1,
  "dtype": "fp16",
  "weight_dtype": "bf16",
  "activation_dtype": "bf16",
  "kv_cache_dtype": "fp8",
  "expert_parameter_dtype": "fp4",
  "attention_parameter_dtype": "bf16",
  "training": false
}
```

### 3. Mixed Precision Memory Analysis (`/api/memory/mixed-precision`)

**Method:** `POST`

**Description:** Provides detailed mixed precision memory analysis with component and precision breakdowns.

**Request Parameters:**
- All parameters from memory analysis endpoint
- Enhanced response with precision-specific breakdowns

**Example Request:**
```json
{
  "model_names": ["meta-llama/Meta-Llama-3-8B-Instruct"],
  "sequence_length": 2048,
  "batch_size": 1,
  "dtype": "fp16",
  "weight_dtype": "bf16",
  "activation_dtype": "bf16",
  "kv_cache_dtype": "fp8"
}
```

**Example Response:**
```json
{
  "model_results": {
    "meta-llama/Meta-Llama-3-8B-Instruct": {
      "total": 16000000000,
      "by_component": {
        "parameters": 14000000000,
        "activations": 1500000000,
        "kv_cache": 500000000
      },
      "by_precision": {
        "bf16": 15500000000,
        "fp8": 500000000
      },
      "precision_config": {
        "weight_dtype": "bf16",
        "activation_dtype": "bf16",
        "kv_cache_dtype": "fp8"
      },
      "efficiency_metrics": {
        "memory_savings_percent": 12.5,
        "compression_ratio": 0.875,
        "precision_diversity": 2
      },
      "attention_mechanism": "GQA"
    }
  },
  "execution_time": 0.45,
  "timestamp": "2025-01-25T10:30:00Z",
  "request_id": "mixed_precision_1737891000_12345",
  "analysis_type": "mixed_precision_memory"
}
```

### 4. Enhanced Supported Data Types (`/api/memory/dtypes`)

**Method:** `GET`

**Description:** Returns comprehensive information about supported data types including mixed precision defaults.

**Example Response:**
```json
{
  "supported_dtypes": ["fp32", "fp16", "bf16", "int8", "fp8", "fp4"],
  "default_dtype": "fp16",
  "dtype_info": {
    "fp32": {
      "bytes_per_element": 4,
      "description": "32-bit floating point",
      "use_case": "Highest precision, largest memory usage",
      "components": ["weights", "activations", "gradients", "optimizer_states", "kv_cache"],
      "memory_multiplier": 2.0
    },
    "fp8": {
      "bytes_per_element": 1,
      "description": "8-bit floating point", 
      "use_case": "Advanced quantization with floating point representation",
      "components": ["weights", "kv_cache", "expert_parameters"],
      "memory_multiplier": 0.5
    }
  },
  "mixed_precision_defaults": {
    "weight_dtype": "bf16",
    "activation_dtype": "bf16",
    "kv_cache_dtype": "bf16", 
    "expert_parameter_dtype": "fp8",
    "attention_parameter_dtype": "bf16",
    "grad_dtype": "fp16",
    "optimizer_dtype": "fp32"
  }
}
```

### 5. Precision Recommendations (`/api/memory/precision-recommendations`)

**Method:** `GET`

**Description:** Provides optimized precision configurations for different use cases.

**Parameters:**
- `model_name`: Name of the model
- `use_case`: Target use case (inference, training, memory_optimized, quality_optimized)

**Example Request:**
```
GET /api/memory/precision-recommendations?model_name=deepseek-ai/DeepSeek-V3&use_case=memory_optimized
```

**Example Response:**
```json
{
  "model_info": {
    "model_name": "deepseek-ai/DeepSeek-V3",
    "architecture": "deepseek",
    "is_moe": true,
    "estimated_params": 671000000000
  },
  "use_case": "memory_optimized",
  "recommended_config": {
    "weight_dtype": "int8",
    "activation_dtype": "bf16",
    "kv_cache_dtype": "fp8",
    "expert_parameter_dtype": "fp4",
    "attention_parameter_dtype": "fp8",
    "description": "Aggressive quantization for maximum memory savings"
  },
  "estimated_memory_savings_percent": 40.0,
  "alternative_configs": {
    "inference": {...},
    "training": {...},
    "quality_optimized": {...}
  }
}
```

## Supported Data Types

| Type | Bytes | Description | Use Case | Components |
|------|-------|-------------|----------|------------|
| fp32 | 4 | 32-bit floating point | Highest precision | All |
| fp16 | 2 | 16-bit floating point | Balanced precision/memory | All |
| bf16 | 2 | 16-bit brain floating point | Better stability than fp16 | All |
| int8 | 1 | 8-bit integer | Quantized models | Weights, KV cache |
| fp8 | 1 | 8-bit floating point | Advanced quantization | Weights, KV cache, Expert params |
| fp4 | 0.5 | 4-bit floating point | Extreme quantization | Weights, Expert params |

## Backward Compatibility

All existing API calls continue to work unchanged. The new mixed precision parameters are optional and default to the legacy `dtype` or `precision` parameter when not specified.

## Error Handling

The API validates precision types and provides clear error messages for:
- Unsupported data types
- Invalid precision combinations
- Model configuration errors

## Testing

Use the provided `test_mixed_precision_api.py` script to test all mixed precision endpoints:

```bash
python test_mixed_precision_api.py
```