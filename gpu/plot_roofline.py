#!/usr/bin/env python3
"""
Roofline plotting module - consolidated plotting functionality
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from hardware import (
    load_gpu_specs, create_gpu_from_spec, create_npu_from_spec, Dtype,
    compare_gpus
)

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def plot_roofline(gpus, data_types=None, gpu_dtype_pairs=None, save_path=None, xlim=None, ylim=None):
    """
    Plot roofline model for given GPUs and data types
    
    Args:
        gpus: List of GPU instances (used when data_types is provided)
        data_types: List of Dtype enums to plot for all GPUs (default: common types)
        gpu_dtype_pairs: List of (GPU, Dtype) tuples for specific combinations
        save_path: Path to save the plot (optional)
        xlim: X-axis limits tuple (min, max)
        ylim: Y-axis limits tuple (min, max)
    
    Note: Use either (gpus + data_types) OR gpu_dtype_pairs, not both
    """
    plt.figure(figsize=(12, 8))
    
    # Operational intensity range (FLOP/Byte)
    oi_range = np.logspace(0, 3.3, 2000)
    
    # Determine what to plot
    if gpu_dtype_pairs:
        # Plot specific GPU-dtype combinations
        combinations = gpu_dtype_pairs
        colors = plt.cm.tab10(np.linspace(0, 1, len(combinations)))
        color_iter = iter(colors)
    else:
        # Plot all combinations of gpus and data_types
        if data_types is None:
            data_types = [Dtype.bf16, Dtype.fp8]
        combinations = [(gpu, dtype) for gpu in gpus for dtype in data_types]
        colors = plt.cm.tab10(np.linspace(0, 1, len(gpus)))
        gpu_colors = {gpu: color for gpu, color in zip(gpus, colors)}
    
    for gpu, dtype in combinations:
        # Get peak performance and memory bandwidth
        peak_perf = max(gpu.tensor_ops(dtype), gpu.vector_ops(dtype))
        if peak_perf == 0:
            continue
            
        # Skip if memory bandwidth is not available
        if gpu.memory_bandwidth_gbps is None or gpu.memory_bandwidth_gbps == 0:
            continue
            
        mem_bw = gpu.memory_bandwidth_gbps * 1e9  # Convert to Bytes/s
        
        # Calculate roofline
        # Performance = min(peak_perf, mem_bw * operational_intensity)
        roofline_perf = np.minimum(peak_perf, mem_bw * oi_range)
        
        # Convert to TFLOPS for plotting
        roofline_perf_tflops = roofline_perf / 1e12
        
        label = f"{gpu.name} {dtype.name.upper()}"
        
        # Choose color and linestyle
        if gpu_dtype_pairs:
            color = next(color_iter)
        else:
            color = gpu_colors[gpu]
        
        linestyle = '-' if 'tensor' in str(dtype.name) or dtype in [Dtype.fp8, Dtype.bf16] else '--'
        
        plt.plot(oi_range, roofline_perf_tflops, 
                 label=label, color=color, linestyle=linestyle)
    
    if xlim:
        plt.xlim(xlim)
    if ylim:
        plt.ylim(ylim)

    plt.xlabel('Operational Intensity (FLOP/Byte)')
    plt.ylabel('Performance (TFLOPS)')
    plt.title('GPU Roofline Model Comparison')
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def compare_specific_combinations(gpu_dtype_specs, save_path=None, xlim=None, ylim=None):
    """
    Compare specific GPU-dtype combinations
    
    Args:
        gpu_dtype_specs: List of tuples like [("nvidia_h20", "fp8"), ("huawei_ascend_910b", "int8")]
        save_path: Path to save the plot (optional)
        xlim: X-axis limits tuple (min, max)
        ylim: Y-axis limits tuple (min, max)
    
    Example:
        compare_specific_combinations([
            ("nvidia_h20", "fp8"),
            ("huawei_ascend_910b", "int8"),
            ("nvidia_b200", "fp8")
        ])
    """
    # Load GPU data
    gpu_data = load_gpu_specs()
    
    # Convert specs to GPU-dtype pairs
    gpu_dtype_pairs = []
    for gpu_key, dtype_str in gpu_dtype_specs:
        if gpu_key in gpu_data['gpus']:
            gpu = create_gpu_from_spec(gpu_key, gpu_data)
        elif gpu_key in gpu_data['npus']:
            gpu = create_npu_from_spec(gpu_key, gpu_data)
        else:
            print(f"Warning: GPU/NPU '{gpu_key}' not found in data")
            continue
            
        # Convert string to Dtype enum
        try:
            dtype = Dtype[dtype_str.lower()]
        except KeyError:
            print(f"Warning: Data type '{dtype_str}' not supported")
            continue
            
        gpu_dtype_pairs.append((gpu, dtype))
    
    if not gpu_dtype_pairs:
        print("No valid GPU-dtype combinations found")
        return
    
    # Plot using the updated function
    plot_roofline(gpus=None, gpu_dtype_pairs=gpu_dtype_pairs, 
                  save_path=save_path, xlim=xlim, ylim=ylim)


def calculate_knee_point(peak_perf, mem_bw):
    """
    计算roofline的拐点 (knee point)
    拐点是内存带宽限制线和计算限制线的交点
    """
    # 拐点的运算强度 = 峰值性能 / 内存带宽
    knee_oi = peak_perf / mem_bw
    # 拐点的性能就是峰值性能
    knee_perf = peak_perf
    return knee_oi, knee_perf


def plot_roofline_with_knee_points(accelerators_specs, save_path=None, xlim=None, ylim=None):
    """
    绘制roofline图并标记拐点值和加速器名称
    
    Args:
        accelerators_specs: List of tuples like [("nvidia_h100_sxm5", "fp8"), ("huawei_ascend_910b", "int8")]
        save_path: 保存路径
        xlim: X轴范围
        ylim: Y轴范围
    """
    plt.figure(figsize=(14, 10))
    
    # 运算强度范围 (FLOP/Byte)
    oi_range = np.logspace(-1, 3, 2000)
    
    # 加载GPU数据
    gpu_data = load_gpu_specs()
    
    # 颜色和标记样式
    colors = plt.cm.Set1(np.linspace(0, 1, len(accelerators_specs)))
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
    
    knee_points = []  # 存储拐点信息
    
    for i, (acc_key, dtype_str) in enumerate(accelerators_specs):
        # 创建加速器实例
        if acc_key in gpu_data['gpus']:
            accelerator = create_gpu_from_spec(acc_key, gpu_data)
        elif acc_key in gpu_data['npus']:
            accelerator = create_npu_from_spec(acc_key, gpu_data)
        else:
            print(f"警告: 找不到加速器 '{acc_key}'")
            continue
        
        # 转换数据类型
        try:
            dtype = Dtype[dtype_str.lower()]
        except KeyError:
            print(f"警告: 不支持的数据类型 '{dtype_str}'")
            continue
        
        # 获取峰值性能和内存带宽
        tensor_perf = accelerator.tensor_ops(dtype)
        vector_perf = accelerator.vector_ops(dtype)
        peak_perf = max(tensor_perf, vector_perf)
        
        if peak_perf == 0:
            print(f"警告: {accelerator.name} 的 {dtype_str} 性能为0")
            continue
        
        mem_bw = accelerator.memory_bandwidth_gbps * 1e9  # 转换为 Bytes/s
        if mem_bw == 0:
            print(f"警告: {accelerator.name} 的内存带宽为0")
            continue
        
        # 计算roofline
        roofline_perf = np.minimum(peak_perf, mem_bw * oi_range)
        roofline_perf_tflops = roofline_perf / 1e12  # 转换为TFLOPS
        
        # 计算拐点
        knee_oi, knee_perf = calculate_knee_point(peak_perf, mem_bw)
        knee_perf_tflops = knee_perf / 1e12
        
        # 存储拐点信息
        knee_points.append({
            'name': accelerator.name,
            'dtype': dtype_str.upper(),
            'oi': knee_oi,
            'perf': knee_perf_tflops,
            'color': colors[i],
            'marker': markers[i % len(markers)]
        })
        
        # 绘制roofline
        color = colors[i]
        label = f"{accelerator.name} {dtype_str.upper()}"
        
        plt.plot(oi_range, roofline_perf_tflops, 
                 color=color, linewidth=2.5, label=label)
        
        # 标记拐点
        marker = markers[i % len(markers)]
        plt.plot(knee_oi, knee_perf_tflops, 
                 marker=marker, markersize=10, color=color, 
                 markeredgecolor='black', markeredgewidth=1.5)
        
        # 添加拐点数值标注
        offset_x = knee_oi * 0.15  # X方向偏移
        offset_y = knee_perf_tflops * 0.1  # Y方向偏移
        
        plt.annotate(f'{knee_oi:.1f} FLOP/Byte\n{knee_perf_tflops:.0f} TFLOPS', 
                    xy=(knee_oi, knee_perf_tflops),
                    xytext=(knee_oi + offset_x, knee_perf_tflops + offset_y),
                    fontsize=9, ha='left', va='bottom',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                    arrowprops=dict(arrowstyle='->', color='black', lw=1))
    
    # 设置坐标轴
    # plt.xscale('log')
    # plt.yscale('log')
    
    if xlim:
        plt.xlim(xlim)
    else:
        # plt.xlim(0.1, 1000)
        pass
    
    if ylim:
        plt.ylim(ylim)
    else:
        # 自动设置Y轴范围
        max_perf = max([kp['perf'] for kp in knee_points])
        plt.ylim(1, max_perf * 1.1)
    
    # 设置标签和标题
    plt.xlabel('运算强度 (FLOP/Byte)', fontsize=14)
    plt.ylabel('性能 (TFLOPS)', fontsize=14)
    plt.title('Roofline模型对比 - 拐点标记', fontsize=16, fontweight='bold')
    
    # 网格和图例
    plt.grid(True, alpha=0.3, which='both')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    # 添加拐点信息表格
    print("\n=== 拐点信息汇总 ===")
    print(f"{'加速器':<20} {'数据类型':<8} {'拐点运算强度':<15} {'峰值性能':<12}")
    print("-" * 65)
    for kp in knee_points:
        print(f"{kp['name']:<20} {kp['dtype']:<8} {kp['oi']:<15.1f} {kp['perf']:<12.0f}")
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"\n图片已保存到: {save_path}")
    
    plt.show()
    
    return knee_points


def demo_ai_training_comparison():
    """Demo: AI训练加速器对比"""
    print("场景1: AI训练加速器对比")
    ai_specs = [
        ("nvidia_h100_sxm5", "bf16"),
        ("nvidia_h20", "bf16"), 
        ("huawei_ascend_910b", "fp16")
    ]
    
    return plot_roofline_with_knee_points(
        ai_specs, 
        save_path="fig/roofline_ai_knee_points.png",
        # xlim=(0.1, 500),
        # ylim=(10, 2000)
    )


def demo_inference_comparison():
    """Demo: 超低精度推理对比"""
    print("场景2: 超低精度推理对比")
    inference_specs = [
        ("nvidia_h100_sxm5", "fp8"),
        ("nvidia_h20", "fp8"),
        ("huawei_ascend_910b", "int8")
    ]
    
    return plot_roofline_with_knee_points(
        inference_specs,
        save_path="fig/roofline_inference_knee_points.png", 
        # xlim=(0.1, 1000),
        # ylim=(100, 5000)
    )


def demo_comprehensive_comparison():
    """Demo: 全面性能对比"""
    print("场景3: 全面性能对比")
    comprehensive_specs = [
        ("nvidia_h100_sxm5", "fp8"),
        ("nvidia_b200", "fp8"),
        ("nvidia_h20", "fp8"),
        ("huawei_ascend_910b", "int8")
    ]
    
    return plot_roofline_with_knee_points(
        comprehensive_specs,
        save_path="fig/roofline_comprehensive_knee_points.png",
        # xlim=(0.1, 2000), 
        # ylim=(100, 10000)
    )

def example_basic_usage():
    """Example of basic roofline plotting"""
    # Load all accelerator specifications
    print("Loading accelerator specifications...")
    spec_data = load_gpu_specs()
    
    # Create specific accelerator instances
    h100 = create_gpu_from_spec("nvidia_h100_sxm5", spec_data)
    h20 = create_gpu_from_spec("nvidia_h20", spec_data)
    ascend = create_npu_from_spec("huawei_ascend_910b", spec_data)
    
    # Plot specific combinations
    plot_roofline(
        gpus=None,
        gpu_dtype_pairs=[
            (h100, Dtype.fp8),
            # (h100, Dtype.fp16),
            (h20, Dtype.fp8),
            (ascend, Dtype.int8)
        ],
        save_path="fig/example_basic_roofline.png"
    )

def main():
    """主函数 - 展示不同的对比场景"""
    
    print("=== Roofline拐点分析 ===\n")

    example_basic_usage()
    
    # 场景1: AI训练对比 - H100 vs H20 vs Ascend 910B
    demo_ai_training_comparison()
    
    # 场景2: 超低精度推理对比
    print("\n" + "="*50)
    demo_inference_comparison()
    
    # 场景3: 全面对比 - 包含Blackwell
    # print("\n" + "="*50)
    # demo_comprehensive_comparison()




if __name__ == "__main__":
    main()
