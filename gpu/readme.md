# GPU Roofline Model Analysis

This project provides tools for plotting and analyzing roofline models for different GPUs and NPUs.

## Project Structure

- `hardware.py` - Core hardware abstraction classes (GPU, NPU, Accelerator)
- `plot_roofline.py` - **Dedicated plotting module** with all roofline visualization functions
- `data/gpu_specs.yaml` - Hardware specifications database
- `roofline_examples.py` - Usage examples
- `example_specific_comparison.py` - Specific comparison examples

## Key Features

### Plotting Functions (in `plot_roofline.py`)

1. **`plot_roofline()`** - Basic roofline plotting
2. **`plot_roofline_with_knee_points()`** - Advanced plotting with knee point annotations
3. **`compare_specific_combinations()`** - Compare specific GPU-dtype pairs
4. **Demo functions** - Pre-configured comparison scenarios

### Hardware Classes (in `hardware.py`)

- `Accelerator` (abstract base class)
- `GPU` - NVIDIA GPU implementations
- `NPU` - Neural Processing Unit implementations
- `Dtype` - Data type enumeration (fp64, fp32, fp16, bf16, fp8, etc.)

## Usage Examples

### Basic Usage
```python
from plot_roofline import plot_roofline, compare_specific_combinations
from hardware import create_gpu_from_spec, load_gpu_specs, Dtype

# Compare specific combinations
compare_specific_combinations([
    ("nvidia_h100_sxm5", "fp8"),
    ("nvidia_h20", "fp8"),
    ("huawei_ascend_910b", "int8")
])
```

### Advanced Usage with Knee Points
```python
from plot_roofline import plot_roofline_with_knee_points

# Plot with knee point annotations
plot_roofline_with_knee_points([
    ("nvidia_h100_sxm5", "bf16"),
    ("nvidia_h20", "bf16"), 
    ("huawei_ascend_910b", "fp16")
], save_path="ai_training_comparison.png")
```

### Manual GPU Instance Creation
```python
from hardware import load_gpu_specs, create_gpu_from_spec, Dtype
from plot_roofline import plot_roofline

specs = load_gpu_specs()
h100 = create_gpu_from_spec("nvidia_h100_sxm5", specs)
h20 = create_gpu_from_spec("nvidia_h20", specs)

plot_roofline(
    gpus=None,
    gpu_dtype_pairs=[(h100, Dtype.fp8), (h20, Dtype.fp8)]
)
```

## Running Examples

```bash
# Run all demo scenarios
python plot_roofline.py

# Run specific examples
python roofline_examples.py
python example_specific_comparison.py
```

## Data Format

Hardware specifications are stored in `data/gpu_specs.yaml` with the following structure:

```yaml
gpus:
  nvidia_h100_sxm5:
    name: "NVIDIA H100 SXM5"
    memory_size_gb: 80
    memory_bandwidth_gbps: 3350
    tensor_performance:
      fp8_tensor: 1979
      bf16_tensor: 989
    vector_performance:
      fp32: 67
      fp16: 134
```