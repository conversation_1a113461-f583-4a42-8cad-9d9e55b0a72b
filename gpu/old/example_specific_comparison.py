#!/usr/bin/env python3
"""
Example script showing how to compare specific GPU-dtype combinations
"""

from plot_roofline import compare_specific_combinations

if __name__ == "__main__":
    # Example 1: Compare H20 with FP8 vs Ascend 910B with INT8
    print("Comparing specific GPU-dtype combinations...")
    
    compare_specific_combinations([
        ("nvidia_h20", "fp8"),
        ("huawei_ascend_910b", "int8"),
        ("nvidia_b200", "fp8"),
        ("nvidia_h100_sxm5", "bf16")
    ], save_path="roofline_specific_comparison.png")
    
    print("Plot saved as 'roofline_specific_comparison.png'")