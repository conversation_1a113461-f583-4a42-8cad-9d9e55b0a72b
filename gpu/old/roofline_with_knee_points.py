#!/usr/bin/env python3
"""
绘制roofline图并标记拐点值和加速器名称
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from hardware import (
    load_gpu_specs, create_gpu_from_spec, create_npu_from_spec, Dtype
)

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_knee_point(peak_perf, mem_bw):
    """
    计算roofline的拐点 (knee point)
    拐点是内存带宽限制线和计算限制线的交点
    """
    # 拐点的运算强度 = 峰值性能 / 内存带宽
    knee_oi = peak_perf / mem_bw
    # 拐点的性能就是峰值性能
    knee_perf = peak_perf
    return knee_oi, knee_perf

def plot_roofline_with_knee_points(accelerators_specs, save_path=None, xlim=None, ylim=None):
    """
    绘制roofline图并标记拐点值和加速器名称
    
    Args:
        accelerators_specs: List of tuples like [("nvidia_h100_sxm5", "fp8"), ("huawei_ascend_910b", "int8")]
        save_path: 保存路径
        xlim: X轴范围
        ylim: Y轴范围
    """
    plt.figure(figsize=(14, 10))
    
    # 运算强度范围 (FLOP/Byte)
    oi_range = np.logspace(-1, 3, 2000)
    
    # 加载GPU数据
    gpu_data = load_gpu_specs()
    
    # 颜色和标记样式
    colors = plt.cm.Set1(np.linspace(0, 1, len(accelerators_specs)))
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
    
    knee_points = []  # 存储拐点信息
    
    for i, (acc_key, dtype_str) in enumerate(accelerators_specs):
        # 创建加速器实例
        if acc_key in gpu_data['gpus']:
            accelerator = create_gpu_from_spec(acc_key, gpu_data)
        elif acc_key in gpu_data['npus']:
            accelerator = create_npu_from_spec(acc_key, gpu_data)
        else:
            print(f"警告: 找不到加速器 '{acc_key}'")
            continue
        
        # 转换数据类型
        try:
            dtype = Dtype[dtype_str.lower()]
        except KeyError:
            print(f"警告: 不支持的数据类型 '{dtype_str}'")
            continue
        
        # 获取峰值性能和内存带宽
        tensor_perf = accelerator.tensor_ops(dtype)
        vector_perf = accelerator.vector_ops(dtype)
        peak_perf = max(tensor_perf, vector_perf)
        
        if peak_perf == 0:
            print(f"警告: {accelerator.name} 的 {dtype_str} 性能为0")
            continue
        
        mem_bw = accelerator.memory_bandwidth_gbps * 1e9  # 转换为 Bytes/s
        if mem_bw == 0:
            print(f"警告: {accelerator.name} 的内存带宽为0")
            continue
        
        # 计算roofline
        roofline_perf = np.minimum(peak_perf, mem_bw * oi_range)
        roofline_perf_tflops = roofline_perf / 1e12  # 转换为TFLOPS
        
        # 计算拐点
        knee_oi, knee_perf = calculate_knee_point(peak_perf, mem_bw)
        knee_perf_tflops = knee_perf / 1e12
        
        # 存储拐点信息
        knee_points.append({
            'name': accelerator.name,
            'dtype': dtype_str.upper(),
            'oi': knee_oi,
            'perf': knee_perf_tflops,
            'color': colors[i],
            'marker': markers[i % len(markers)]
        })
        
        # 绘制roofline
        color = colors[i]
        label = f"{accelerator.name} {dtype_str.upper()}"
        
        plt.plot(oi_range, roofline_perf_tflops, 
                 color=color, linewidth=2.5, label=label)
        
        # 标记拐点
        marker = markers[i % len(markers)]
        plt.plot(knee_oi, knee_perf_tflops, 
                 marker=marker, markersize=10, color=color, 
                 markeredgecolor='black', markeredgewidth=1.5)
        
        # 添加拐点数值标注
        offset_x = knee_oi * 0.15  # X方向偏移
        offset_y = knee_perf_tflops * 0.1  # Y方向偏移
        
        plt.annotate(f'{knee_oi:.1f} FLOP/Byte\n{knee_perf_tflops:.0f} TFLOPS', 
                    xy=(knee_oi, knee_perf_tflops),
                    xytext=(knee_oi + offset_x, knee_perf_tflops + offset_y),
                    fontsize=9, ha='left', va='bottom',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                    arrowprops=dict(arrowstyle='->', color='black', lw=1))
    
    # 设置坐标轴
    plt.xscale('log')
    plt.yscale('log')
    
    if xlim:
        plt.xlim(xlim)
    else:
        plt.xlim(0.1, 1000)
    
    if ylim:
        plt.ylim(ylim)
    else:
        # 自动设置Y轴范围
        max_perf = max([kp['perf'] for kp in knee_points])
        plt.ylim(1, max_perf * 10)
    
    # 设置标签和标题
    plt.xlabel('运算强度 (FLOP/Byte)', fontsize=14)
    plt.ylabel('性能 (TFLOPS)', fontsize=14)
    plt.title('Roofline模型对比 - 拐点标记', fontsize=16, fontweight='bold')
    
    # 网格和图例
    plt.grid(True, alpha=0.3, which='both')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    # 添加拐点信息表格
    print("\n=== 拐点信息汇总 ===")
    print(f"{'加速器':<20} {'数据类型':<8} {'拐点运算强度':<15} {'峰值性能':<12}")
    print("-" * 65)
    for kp in knee_points:
        print(f"{kp['name']:<20} {kp['dtype']:<8} {kp['oi']:<15.1f} {kp['perf']:<12.0f}")
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"\n图片已保存到: {save_path}")
    
    plt.show()
    
    return knee_points

def main():
    """主函数 - 展示不同的对比场景"""
    
    print("=== Roofline拐点分析 ===\n")
    
    # 场景1: AI训练对比 - H100 vs H20 vs Ascend 910B
    print("场景1: AI训练加速器对比")
    ai_specs = [
        ("nvidia_h100_sxm5", "bf16"),
        ("nvidia_h20", "bf16"), 
        ("huawei_ascend_910b", "fp16")
    ]
    
    knee_points_ai = plot_roofline_with_knee_points(
        ai_specs, 
        save_path="roofline_ai_knee_points.png",
        xlim=(0.1, 500),
        ylim=(10, 2000)
    )
    
    # 场景2: 超低精度推理对比
    print("\n" + "="*50)
    print("场景2: 超低精度推理对比")
    inference_specs = [
        ("nvidia_h100_sxm5", "fp8"),
        ("nvidia_h20", "fp8"),
        ("huawei_ascend_910b", "int8")
    ]
    
    knee_points_inference = plot_roofline_with_knee_points(
        inference_specs,
        save_path="roofline_inference_knee_points.png", 
        xlim=(0.1, 1000),
        ylim=(100, 5000)
    )
    
    # 场景3: 全面对比 - 包含Blackwell
    print("\n" + "="*50)
    print("场景3: 全面性能对比")
    comprehensive_specs = [
        ("nvidia_h100_sxm5", "fp8"),
        ("nvidia_b200", "fp8"),
        ("nvidia_h20", "fp8"),
        ("huawei_ascend_910b", "int8")
    ]
    
    knee_points_comprehensive = plot_roofline_with_knee_points(
        comprehensive_specs,
        save_path="roofline_comprehensive_knee_points.png",
        xlim=(0.1, 2000), 
        ylim=(100, 10000)
    )

if __name__ == "__main__":
    main()