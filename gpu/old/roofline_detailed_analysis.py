#!/usr/bin/env python3
"""
详细的roofline拐点分析 - 单场景深度分析
"""

import numpy as np
import matplotlib.pyplot as plt
from hardware import (
    load_gpu_specs, create_gpu_from_spec, create_npu_from_spec, Dtype
)

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detailed_roofline_analysis(accelerators_specs, title="Roofline详细分析", save_path=None):
    """
    详细的roofline分析，包含更多信息标注
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 运算强度范围
    oi_range = np.logspace(-1, 3, 2000)
    
    # 加载数据
    gpu_data = load_gpu_specs()
    colors = plt.cm.Set1(np.linspace(0, 1, len(accelerators_specs)))
    
    analysis_data = []
    
    # 左图：完整roofline
    for i, (acc_key, dtype_str) in enumerate(accelerators_specs):
        # 创建加速器实例
        if acc_key in gpu_data['gpus']:
            accelerator = create_gpu_from_spec(acc_key, gpu_data)
        elif acc_key in gpu_data['npus']:
            accelerator = create_npu_from_spec(acc_key, gpu_data)
        else:
            continue
        
        # 转换数据类型
        try:
            dtype = Dtype[dtype_str.lower()]
        except KeyError:
            continue
        
        # 获取性能参数
        tensor_perf = accelerator.tensor_ops(dtype)
        vector_perf = accelerator.vector_ops(dtype)
        peak_perf = max(tensor_perf, vector_perf)
        mem_bw = accelerator.memory_bandwidth_gbps * 1e9
        
        if peak_perf == 0 or mem_bw == 0:
            continue
        
        # 计算roofline和拐点
        roofline_perf = np.minimum(peak_perf, mem_bw * oi_range)
        roofline_perf_tflops = roofline_perf / 1e12
        
        knee_oi = peak_perf / mem_bw
        knee_perf_tflops = peak_perf / 1e12
        
        color = colors[i]
        label = f"{accelerator.name} {dtype_str.upper()}"
        
        # 绘制roofline
        ax1.plot(oi_range, roofline_perf_tflops, color=color, linewidth=3, label=label)
        
        # 标记拐点
        ax1.plot(knee_oi, knee_perf_tflops, 'o', markersize=12, color=color, 
                markeredgecolor='black', markeredgewidth=2)
        
        # 拐点标注
        ax1.annotate(f'拐点: {knee_oi:.1f} FLOP/Byte', 
                    xy=(knee_oi, knee_perf_tflops),
                    xytext=(knee_oi * 2, knee_perf_tflops * 1.5),
                    fontsize=11, ha='left',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor=color, alpha=0.8),
                    arrowprops=dict(arrowstyle='->', color='black', lw=1.5))
        
        # 存储分析数据
        analysis_data.append({
            'name': accelerator.name,
            'dtype': dtype_str.upper(),
            'peak_perf': knee_perf_tflops,
            'mem_bw': accelerator.memory_bandwidth_gbps,
            'knee_oi': knee_oi,
            'memory_size': accelerator.memory_size_gb,
            'color': color
        })
    
    # 设置左图
    ax1.set_xscale('log')
    ax1.set_yscale('log')
    ax1.set_xlabel('运算强度 (FLOP/Byte)', fontsize=12)
    ax1.set_ylabel('性能 (TFLOPS)', fontsize=12)
    ax1.set_title('Roofline模型 - 拐点分析', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3, which='both')
    ax1.legend(fontsize=10)
    
    # 右图：性能对比柱状图
    names = [data['name'] for data in analysis_data]
    peak_perfs = [data['peak_perf'] for data in analysis_data]
    mem_bws = [data['mem_bw'] for data in analysis_data]
    knee_ois = [data['knee_oi'] for data in analysis_data]
    colors_list = [data['color'] for data in analysis_data]
    
    x = np.arange(len(names))
    width = 0.25
    
    # 峰值性能柱状图
    bars1 = ax2.bar(x - width, peak_perfs, width, label='峰值性能 (TFLOPS)', 
                    color=colors_list, alpha=0.8)
    
    # 内存带宽柱状图 (缩放到合适范围)
    mem_bw_scaled = [bw/100 for bw in mem_bws]  # 除以100以适应显示
    bars2 = ax2.bar(x, mem_bw_scaled, width, label='内存带宽 (×100 GB/s)', 
                    color=colors_list, alpha=0.6)
    
    # 拐点运算强度柱状图 (缩放)
    knee_oi_scaled = [oi/10 for oi in knee_ois]  # 除以10以适应显示
    bars3 = ax2.bar(x + width, knee_oi_scaled, width, label='拐点运算强度 (×10 FLOP/Byte)', 
                    color=colors_list, alpha=0.4)
    
    # 添加数值标签
    for i, (bar1, bar2, bar3) in enumerate(zip(bars1, bars2, bars3)):
        ax2.text(bar1.get_x() + bar1.get_width()/2, bar1.get_height() + 10,
                f'{peak_perfs[i]:.0f}', ha='center', va='bottom', fontsize=9)
        ax2.text(bar2.get_x() + bar2.get_width()/2, bar2.get_height() + 1,
                f'{mem_bws[i]:.0f}', ha='center', va='bottom', fontsize=9)
        ax2.text(bar3.get_x() + bar3.get_width()/2, bar3.get_height() + 1,
                f'{knee_ois[i]:.1f}', ha='center', va='bottom', fontsize=9)
    
    ax2.set_xlabel('加速器', fontsize=12)
    ax2.set_ylabel('性能指标', fontsize=12)
    ax2.set_title('关键性能指标对比', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels([f"{data['name']}\n{data['dtype']}" for data in analysis_data], 
                       rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"详细分析图已保存到: {save_path}")
    
    plt.show()
    
    # 打印详细分析报告
    print(f"\n=== {title} - 详细报告 ===")
    print(f"{'加速器':<20} {'数据类型':<8} {'峰值性能':<12} {'内存带宽':<12} {'拐点OI':<12} {'内存容量':<10}")
    print("-" * 85)
    for data in analysis_data:
        print(f"{data['name']:<20} {data['dtype']:<8} {data['peak_perf']:<12.0f} "
              f"{data['mem_bw']:<12.0f} {data['knee_oi']:<12.1f} {data['memory_size']:<10.0f}")
    
    return analysis_data

def main():
    """主函数"""
    
    # AI推理场景详细分析
    print("=== AI推理场景详细分析 ===")
    inference_specs = [
        ("nvidia_h100_sxm5", "fp8"),
        ("nvidia_h20", "fp8"), 
        ("huawei_ascend_910b", "int8")
    ]
    
    detailed_roofline_analysis(
        inference_specs,
        title="AI推理加速器对比",
        save_path="roofline_detailed_inference.png"
    )

if __name__ == "__main__":
    main()