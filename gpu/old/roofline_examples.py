#!/usr/bin/env python3
"""
Examples showing different ways to use the roofline plotting functionality
"""

from hardware import (
    load_gpu_specs, create_gpu_from_spec, create_npu_from_spec, Dtype
)
from plot_roofline import plot_roofline, compare_specific_combinations

def example_1_specific_combinations():
    """Example 1: Compare specific GPU-dtype combinations using the helper function"""
    print("Example 1: Comparing H20 FP8 vs Ascend 910B INT8 vs B200 FP8")
    
    compare_specific_combinations([
        ("nvidia_h20", "fp8"),
        ("huawei_ascend_910b", "int8"),
        ("nvidia_b200", "fp8")
    ], save_path="example1_specific.png")

def example_2_manual_pairs():
    """Example 2: Manually create GPU instances and use plot_roofline directly"""
    print("Example 2: Manual GPU instance creation")
    
    # Load specs
    specs = load_gpu_specs()
    
    # Create specific GPU instances
    h100 = create_gpu_from_spec("nvidia_h100_sxm5", specs)
    h20 = create_gpu_from_spec("nvidia_h20", specs)
    ascend = create_npu_from_spec("huawei_ascend_910b", specs)
    
    # Plot specific combinations
    plot_roofline(
        gpus=None,
        gpu_dtype_pairs=[
            (h100, Dtype.bf16),
            (h20, Dtype.fp8),
            (ascend, Dtype.int8)
        ],
        save_path="example2_manual.png"
    )

def example_3_traditional():
    """Example 3: Traditional approach - all combinations of GPUs and dtypes"""
    print("Example 3: Traditional all-combinations approach")
    
    specs = load_gpu_specs()
    
    # Create GPU instances
    gpus = [
        create_gpu_from_spec("nvidia_h100_sxm5", specs),
        create_gpu_from_spec("nvidia_h20", specs)
    ]
    
    # Plot all combinations of these GPUs with specified dtypes
    plot_roofline(
        gpus=gpus,
        data_types=[Dtype.fp8, Dtype.bf16],
        save_path="example3_traditional.png"
    )

if __name__ == "__main__":
    example_1_specific_combinations()
    example_2_manual_pairs() 
    example_3_traditional()
    
    print("\nAll examples completed! Check the generated PNG files.")