from abc import ABC, abstractmethod
from enum import Enum
import yaml
from pathlib import Path

class Dtype(Enum):
    fp64 = 0
    fp32 = 1
    fp16 = 2
    bf16 = 3
    fp8 = 4
    fp6 = 5
    fp4 = 6
    int32 = 7
    int8 = 8

class Accelerator(ABC):
    def __init__(self, name="Unknown"):
        self.name = name
        self.memory_bandwidth = 0  # GB/s
        self.memory_size = 0  # GB
        self.l2_size = 0  # MB
        self.l2_bandwidth = 0  # GB/s

    @property
    @abstractmethod
    def memory_size_gb(self):
        pass 

    @property
    @abstractmethod
    def memory_bandwidth_gbps(self):
        pass

    @property
    @abstractmethod
    def l2_size_mb(self):
        pass

    @abstractmethod
    def ops(self, dtype=Dtype.bf16):
        pass
    
    @abstractmethod
    def vector_ops(self, dtype=Dtype.bf16):
        pass
    
    @abstractmethod
    def tensor_ops(self, dtype=Dtype.bf16):
        pass

class GPU(Accelerator):
    def __init__(self, name="Unknown GPU", spec_data=None):
        super().__init__(name)
        self.spec_data = spec_data or {}
        
    @property
    def memory_size_gb(self):
        return self.spec_data.get('memory_size_gb', 0)
    
    @property
    def memory_bandwidth_gbps(self):
        return self.spec_data.get('memory_bandwidth_gbps', 0)
    
    @property
    def l2_size_mb(self):
        return self.spec_data.get('l2_cache_mb', 0)
    
    def ops(self, dtype=Dtype.bf16):
        """Get peak operations per second for given data type"""
        dtype_map = {
            Dtype.fp64: 'fp64',
            Dtype.fp32: 'fp32', 
            Dtype.fp16: 'fp16',
            Dtype.bf16: 'bf16',
            Dtype.int32: 'int32'
        }
        
        if dtype in dtype_map:
            return self.spec_data.get('vector_performance', {}).get(dtype_map[dtype], 0) * 1e12  # Convert TFLOPS to FLOPS
        return 0
    
    def vector_ops(self, dtype=Dtype.bf16):
        """Get vector (non-tensor) operations per second"""
        return self.ops(dtype)
    
    def tensor_ops(self, dtype=Dtype.bf16):
        """Get tensor operations per second for given data type"""
        dtype_map = {
            Dtype.fp64: 'fp64_tensor',
            # Dtype.fp32: 'fp32',  # Use vector performance for FP32
            Dtype.fp16: 'fp16_tensor',
            Dtype.bf16: 'bf16_tensor',
            Dtype.fp8: 'fp8_tensor',
            Dtype.fp6: 'fp6_tensor',
            Dtype.fp4: 'fp4_tensor',
            Dtype.int8: 'int8_tensor'
        }
        
        if dtype in dtype_map:
            tensor_perf = self.spec_data.get('tensor_performance', {})
            if dtype == Dtype.fp32:
                # Use vector performance for FP32 if tensor not available
                return self.vector_ops(dtype)
            perf = tensor_perf.get(dtype_map[dtype])
            if perf is not None:
                return perf * 1e12  # Convert TFLOPS to FLOPS
        return 0

class NPU(Accelerator):
    def __init__(self, name="Unknown NPU", spec_data=None):
        super().__init__(name)
        self.spec_data = spec_data or {}
    
    @property
    def memory_size_gb(self):
        return self.spec_data.get('memory_size_gb', 0)
    
    @property 
    def memory_bandwidth_gbps(self):
        return self.spec_data.get('memory_bandwidth_gbps', 0)
    
    @property
    def l2_size_mb(self):
        return self.spec_data.get('l2_cache_mb', 0)
    
    def ops(self, dtype=Dtype.bf16):
        """Get peak operations per second for given data type"""
        dtype_map = {
            Dtype.fp64: 'fp64',
            Dtype.fp32: 'fp32', 
            Dtype.fp16: 'fp16',
            Dtype.bf16: 'bf16',
            Dtype.int32: 'int32'
        }
        
        if dtype in dtype_map:
            return self.spec_data.get('vector_performance', {}).get(dtype_map[dtype], 0) * 1e12  # Convert TFLOPS to FLOPS
        return 0
    
    def vector_ops(self, dtype=Dtype.bf16):
        """Get vector (non-tensor) operations per second"""
        return self.ops(dtype)
    
    def tensor_ops(self, dtype=Dtype.bf16):
        """Get tensor operations per second for given data type"""
        dtype_map = {
            Dtype.fp64: 'fp64_tensor',
            Dtype.fp32: 'fp32_tensor',
            Dtype.fp16: 'fp16_tensor',
            Dtype.bf16: 'bf16_tensor',
            Dtype.fp8: 'fp8_tensor',
            Dtype.fp6: 'fp6_tensor',
            Dtype.fp4: 'fp4_tensor',
            Dtype.int8: 'int8_tensor'
        }
        
        if dtype in dtype_map:
            tensor_perf = self.spec_data.get('tensor_performance', {})
            perf = tensor_perf.get(dtype_map[dtype])
            if perf is not None:
                return perf * 1e12  # Convert TFLOPS to FLOPS
        return 0

def load_gpu_specs(yaml_file="data/gpu_specs.yaml"):
    """Load GPU specifications from YAML file"""
    with open(yaml_file, 'r') as f:
        data = yaml.safe_load(f)
    return data

def create_gpu_from_spec(gpu_key, spec_data):
    """Create GPU instance from specification data"""
    gpu_spec = spec_data['gpus'][gpu_key]
    return GPU(name=gpu_spec['name'], spec_data=gpu_spec)

def create_npu_from_spec(npu_key, spec_data):
    """Create NPU instance from specification data"""
    npu_spec = spec_data['npus'][npu_key]
    return NPU(name=npu_spec['name'], spec_data=npu_spec)



def compare_gpus(gpu_specs_file="data/gpu_specs.yaml"):
    """Load and compare all GPUs and NPUs from specs file"""
    spec_data = load_gpu_specs(gpu_specs_file)
    
    accelerators = []
    
    # Load GPUs
    for gpu_key in spec_data['gpus'].keys():
        gpu = create_gpu_from_spec(gpu_key, spec_data)
        accelerators.append(gpu)
    
    # Load NPUs
    if 'npus' in spec_data:
        for npu_key in spec_data['npus'].keys():
            npu = create_npu_from_spec(npu_key, spec_data)
            accelerators.append(npu)
    
    return accelerators

