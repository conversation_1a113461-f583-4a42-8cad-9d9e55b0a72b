# Operator-Based LLM Modeling

This document describes the new operator-based modeling system that provides detailed analysis similar to Table 2 in the paper. The system breaks down LLM operations into individual operators (Attention, MatMul, MLP, Communication, etc.) and provides comprehensive performance analysis.

## Overview

The operator-based modeling system provides:

1. **Operator-level breakdown** - Analyze individual operations like attention, MLP, layer normalization
2. **Hardware-aware performance modeling** - Use GPU specifications for realistic performance estimates
3. **Roofline analysis** - Determine compute vs memory bottlenecks
4. **Parallel execution modeling** - Include communication costs for tensor/expert parallelism
5. **Table generation** - Create publication-ready tables similar to research papers

## Key Components

### 1. Operator Base Classes (`llm_modeling_metrics/core/operators.py`)

- **BaseOperator**: Abstract base class for all operators
- **AttentionOperator**: Multi-head attention computation
- **MatMulOperator**: Matrix multiplication operations
- **MLPOperator**: Feed-forward networks with gated activation
- **MoEOperator**: Mixture of Experts with routing
- **CommunicationOperator**: AllReduce, AllGather, AllToAll operations
- **LayerNormOperator**: Layer/RMS normalization
- **HardwareSpecs**: GPU specifications loader

### 2. Enhanced Model Classes

Both `DenseModel` and `MoEModel` now support:

- `get_operator_breakdown()` - Detailed operator-level analysis
- `get_roofline_analysis()` - Performance bottleneck analysis

### 3. Table Generation (`llm_modeling_metrics/utils/table_generator.py`)

- `generate_operator_table()` - Create operator analysis tables
- `generate_roofline_table()` - Create roofline analysis tables
- `compare_models_table()` - Compare multiple models
- `generate_paper_style_table()` - LaTeX-formatted tables

## Usage Examples

### Basic Operator Analysis

```python
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.utils.table_generator import generate_operator_table

# Create model
config = {
    'hidden_size': 4096,
    'num_hidden_layers': 32,
    'num_attention_heads': 32,
    'intermediate_size': 11008,
    'vocab_size': 32000,
    # ... other config parameters
}

model = DenseModel("llama-7b", config)

# Generate operator table
table = generate_operator_table(
    model, 
    batch_size=1, 
    sequence_length=2048,
    hardware_name='nvidia_h100_sxm5',
    format_type='pandas'
)

print(table)
```

### MoE Model with Parallel Configuration

```python
from llm_modeling_metrics.models.moe_model import MoEModel
from llm_modeling_metrics.core.base_model import ParallelConfig

# Create MoE model
moe_config = {
    'hidden_size': 4096,
    'num_hidden_layers': 27,
    'n_shared_experts': 2,
    'n_routed_experts': 64,
    'num_experts_per_tok': 6,
    # ... other MoE config parameters
}

model = MoEModel("deepseek-moe", moe_config)

# Define parallel configuration
parallel_config = ParallelConfig(
    tensor_parallel_size=2,
    expert_parallel_size=4
)

# Get operator breakdown with communication analysis
breakdown = model.get_operator_breakdown(
    batch_size=1,
    sequence_length=2048,
    hardware_name='nvidia_h100_sxm5',
    parallel_config=parallel_config
)
```

### Roofline Analysis

```python
# Get roofline analysis
roofline = model.get_roofline_analysis(
    batch_size=1,
    sequence_length=2048,
    hardware_name='nvidia_h100_sxm5'
)

# Check if operators are compute or memory bound
for op_name, op_data in roofline['operators'].items():
    print(f"{op_name}: {op_data['bottleneck']} bound")
    print(f"  Arithmetic Intensity: {op_data['arithmetic_intensity']:.2f}")
    print(f"  Achieved GFLOPS: {op_data['achieved_gflops']:.1f}")
```

## Hardware Specifications

The system uses GPU specifications from `gpu/data/gpu_specs.yaml`. Supported GPUs include:

- NVIDIA A100 (40GB)
- NVIDIA H100 SXM5 (80GB)
- NVIDIA B200 (192GB)
- NVIDIA B300 (288GB)
- NVIDIA L20 (48GB)
- NVIDIA H20 (96GB)
- Huawei Ascend 910B (64GB)

Each specification includes:
- Peak FLOPS for different precisions (FP16, BF16, FP32, etc.)
- Memory bandwidth
- Memory size
- Tensor core counts

## Operator Metrics

Each operator provides the following metrics:

- **FLOPs**: Floating point operations count
- **Memory Capacity**: Memory storage requirements in bytes (peak memory footprint)
- **Memory Movement**: Data transfer requirements in bytes (total data moved)
- **Execution Time**: Estimated execution time in milliseconds
- **Arithmetic Intensity**: FLOPs per byte moved (indicates compute vs memory bound)
- **Utilization**: Hardware utilization percentage

## Table Formats

The system supports multiple output formats:

1. **Pandas DataFrame**: For interactive analysis
2. **Dictionary**: For programmatic access
3. **Markdown**: For documentation
4. **LaTeX**: For research papers

### Example Output

```
     Operator FLOPs (TFLOPS) Memory Capacity (GB) Memory Movement (GB) Time (ms) Arithmetic Intensity Utilization (%)
    Attention          5.498               10.000                  5.4      5.56              1024.00            63.5
          Mlp          8.866               11.750                 14.6      8.96               607.44            73.7
    Layernorm          0.003                2.000                  2.1      0.64                 1.25             9.1
   Embeddings          0.268                0.382                  0.4      0.27               654.73            73.1
      Lm Head          0.268                0.382                  0.4      0.27               654.73            73.1
        TOTAL         14.903               24.514                 22.9     15.70               649.87           130.4
```

## Communication Modeling

For parallel execution, the system models communication operations:

- **Tensor Parallel**: AllReduce operations for attention/MLP outputs
- **Expert Parallel**: AllToAll operations for token routing in MoE
- **Pipeline Parallel**: Point-to-point communication between stages

Communication time is estimated based on:
- Data size
- Number of devices
- Network bandwidth
- Topology (ring, tree, etc.)

## Roofline Analysis

The roofline model helps identify performance bottlenecks:

- **Compute Bound**: High arithmetic intensity, limited by peak FLOPS
- **Memory Bound**: Low arithmetic intensity, limited by memory bandwidth

The analysis provides:
- Arithmetic intensity for each operator
- Achieved vs peak performance
- Memory bandwidth utilization
- Bottleneck identification

## Running the Example

```bash
python examples/operator_analysis_example.py
```

This example demonstrates:
- Dense model (Llama-style) analysis
- MoE model (DeepSeek-style) analysis with parallelism
- Model comparison
- LaTeX table generation
- Detailed operator breakdown

## Integration with Existing Code

The operator-based modeling is fully compatible with existing functionality. All previous methods continue to work, and the new operator analysis provides additional insights without breaking changes.

## Future Extensions

Potential areas for extension:
- More operator types (embedding layers, positional encoding)
- Advanced communication patterns (hierarchical AllReduce)
- Mixed precision modeling per operator
- Dynamic batching analysis
- Memory optimization strategies