# Roofline Model Controls Implementation

## Overview
Added two new switches to the Roofline Model visualization to enhance user control:

1. **Log Scale Toggle**: Switch between logarithmic and linear scales for both X and Y axes
2. **Mouse Wheel Zoom Toggle**: Enable/disable zooming with mouse scroll wheel

## Changes Made

### 1. State Variables Added
- `this.useLogScale = true` - Controls whether to use logarithmic or linear scales
- `this.enableWheelZoom = true` - Controls whether mouse wheel zoom is enabled

### 2. Chart Configuration Refactoring
- Moved chart configuration from constructor to `getChartConfig()` method
- Made scale type dynamic based on `useLogScale` state
- Made wheel zoom setting dynamic based on `enableWheelZoom` state

### 3. UI Controls Added
Added two new checkboxes in the Display Options section:
```html
<div class="form-check">
    <input class="form-check-input" type="checkbox" id="useLogScale" checked>
    <label class="form-check-label" for="useLogScale">
        Use log scale
    </label>
</div>
<div class="form-check">
    <input class="form-check-input" type="checkbox" id="enableWheelZoom" checked>
    <label class="form-check-label" for="enableWheelZoom">
        Enable mouse wheel zoom
    </label>
</div>
```

### 4. Event Handlers Added
- Event listener for log scale checkbox that calls `updateScaleType()`
- Event listener for wheel zoom checkbox that calls `updateZoomSettings()`

### 5. New Methods Added

#### `updateScaleType()`
- Updates chart scale types (logarithmic/linear) for both X and Y axes
- Adjusts scale ranges appropriately:
  - Log scale: X(0.01-1000), Y(0.1-1000)
  - Linear scale: X(0-100), Y(0-100)
- Updates the chart display

#### `updateZoomSettings()`
- Updates the wheel zoom setting in the chart's zoom plugin configuration
- Updates the chart display

## File Modified
- `llm_modeling_metrics/web/static/js/roofline-visualizer.js`

## Testing

### Manual Testing
1. Open `test_roofline_controls.html` in a web browser
2. Click "Initialize Roofline Visualizer"
3. Test the controls:
   - Click "Toggle Log Scale" to switch between log and linear scales
   - Click "Toggle Wheel Zoom" to enable/disable mouse wheel zooming
   - Observe the chart behavior and axis scaling changes

### Expected Behavior

#### Log Scale Toggle
- **ON (default)**: Both axes use logarithmic scale with appropriate ranges
- **OFF**: Both axes use linear scale with adjusted ranges
- Chart should update immediately when toggled
- Axis labels and grid should reflect the scale change

#### Wheel Zoom Toggle
- **ON (default)**: Mouse wheel scrolling zooms the chart
- **OFF**: Mouse wheel scrolling is disabled for the chart
- Pinch zoom should remain enabled regardless of wheel zoom setting
- Pan functionality should remain unaffected

### Integration Testing
The controls integrate seamlessly with existing Roofline Visualizer functionality:
- Precision selection continues to work
- Intensity range controls continue to work
- Other display options (operator labels, knee points) continue to work
- Export and reset zoom functions continue to work

## Benefits
1. **Improved Usability**: Users can choose the most appropriate scale for their data
2. **Better Data Visualization**: Linear scale may be more suitable for certain datasets
3. **Enhanced Control**: Users can disable wheel zoom to prevent accidental zooming
4. **Accessibility**: Provides more options for users with different interaction preferences

## Backward Compatibility
- All existing functionality remains unchanged
- Default behavior (log scale ON, wheel zoom ON) matches previous behavior
- No breaking changes to the API or existing integrations
