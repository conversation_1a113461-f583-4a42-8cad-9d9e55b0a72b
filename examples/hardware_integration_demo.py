#!/usr/bin/env python3
"""
Hardware Integration Demo

This script demonstrates the hardware integration infrastructure
for the LLM modeling metrics system.
"""

from llm_modeling_metrics.hardware import HardwareService
from llm_modeling_metrics.hardware.models import WorkloadProfile, HardwareType
from llm_modeling_metrics.hardware.validation import validate_hardware_config_file


def main():
    """Demonstrate hardware integration functionality."""
    print("🔧 Hardware Integration Demo")
    print("=" * 50)
    
    try:
        # Initialize hardware service
        print("\n1. Initializing Hardware Service...")
        service = HardwareService()
        
        # Get available hardware
        print("\n2. Loading Available Hardware...")
        hardware = service.get_available_hardware()
        
        print(f"   📊 Found {len(hardware['gpu'])} GPUs and {len(hardware['npu'])} NPUs")
        
        # Display GPU information
        print("\n3. GPU Information:")
        for gpu in hardware['gpu'][:3]:  # Show first 3 GPUs
            print(f"   🖥️  {gpu.name}")
            print(f"      Memory: {gpu.memory_size_gb}GB")
            print(f"      Bandwidth: {gpu.memory_bandwidth_gbps}GB/s")
            print(f"      Precisions: {', '.join(gpu.supported_precisions[:5])}...")
            print(f"      Tensor Cores: {'Yes' if gpu.is_tensor_core_capable() else 'No'}")
            if gpu.supports_precision('fp16'):
                print(f"      FP16 Performance: {gpu.get_peak_flops('fp16')} TFLOPS")
            print()
        
        # Display NPU information
        if hardware['npu']:
            print("\n4. NPU Information:")
            for npu in hardware['npu']:
                print(f"   🧠 {npu.name}")
                print(f"      Memory: {npu.memory_size_gb}GB")
                print(f"      Bandwidth: {npu.memory_bandwidth_gbps}GB/s")
                print(f"      Precisions: {', '.join(npu.supported_precisions)}")
                print()
        
        # Test specific hardware lookup
        print("\n5. Specific Hardware Lookup:")
        h100 = service.get_hardware_specs('nvidia_h100_sxm5')
        if h100:
            print(f"   🚀 {h100.name}")
            print(f"      Architecture: {h100.architecture}")
            print(f"      Memory: {h100.memory_size_gb}GB {h100.memory_type}")
            print(f"      Peak BF16: {h100.get_peak_flops('bf16')} TFLOPS")
            print(f"      Peak FP8: {h100.get_peak_flops('fp8')} TFLOPS")
        
        # Test workload recommendations
        print("\n6. Hardware Recommendations:")
        workload = WorkloadProfile(
            model_type="dense",
            batch_size=32,
            sequence_length=2048,
            precision_requirements=["fp16", "bf16"],
            memory_constraints=50
        )
        
        print(f"   📋 Workload: {workload.model_type} model")
        print(f"      Batch size: {workload.batch_size}")
        print(f"      Sequence length: {workload.sequence_length}")
        print(f"      Required precisions: {', '.join(workload.precision_requirements)}")
        print(f"      Memory constraint: {workload.memory_constraints}GB")
        
        recommendations = service.get_hardware_recommendations(workload)
        print(f"\n   🏆 Top 3 Recommendations:")
        
        for i, rec in enumerate(recommendations[:3]):
            print(f"      {i+1}. {rec.hardware_name} (Score: {rec.score:.1f}/100)")
            print(f"         Reasons: {', '.join(rec.reasons[:2])}")
            if rec.estimated_performance:
                print(f"         Est. Performance: {rec.estimated_performance} TFLOPS")
        
        # Test hardware validation
        print("\n7. Hardware Configuration Validation:")
        try:
            result = validate_hardware_config_file('gpu/data/gpu_specs.yaml')
            print(f"   ✅ Configuration: {'Valid' if result.is_valid else 'Invalid'}")
            if result.warnings:
                print(f"   ⚠️  Warnings: {len(result.warnings)}")
            if result.errors:
                print(f"   ❌ Errors: {len(result.errors)}")
                for error in result.errors[:3]:  # Show first 3 errors
                    print(f"      • {error}")
        except Exception as e:
            print(f"   ❌ Validation failed: {e}")
        
        # Test hardware compatibility
        print("\n8. Hardware Compatibility Check:")
        if h100:
            # Create mock operators for testing
            class MockOperator:
                def __init__(self, name, operator_type="attention"):
                    self.name = name
                    self.operator_type = operator_type
                    self.precision = "fp16"
                    self.parameters = 1000000  # 1M parameters
            
            operators = [
                MockOperator("attention_1", "attention"),
                MockOperator("mlp_1", "mlp"),
                MockOperator("attention_2", "attention")
            ]
            
            validation = service.validate_hardware_compatibility('nvidia_h100_sxm5', operators)
            print(f"   🔍 Compatibility with {h100.name}: {'✅ Valid' if validation.is_valid else '❌ Invalid'}")
            
            if validation.warnings:
                print(f"   ⚠️  Warnings:")
                for warning in validation.warnings:
                    print(f"      • {warning}")
            
            if validation.recommendations:
                print(f"   💡 Recommendations:")
                for rec in validation.recommendations:
                    print(f"      • {rec}")
        
        print("\n🎉 Hardware integration demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()