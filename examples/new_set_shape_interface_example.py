#!/usr/bin/env python3
"""
Example demonstrating the new set_shape interface for operators.

This example shows how to use the new set_shape() method to configure
operators before computing their metrics.
"""

from llm_modeling_metrics.core.operators import (
    AttentionOperator, MatMulOperator, MLPOperator, MoEOperator, 
    LayerNormOperator, EmbeddingOperator, HardwareSpecs
)

def main():
    """Demonstrate the new set_shape interface."""
    
    print("=== New set_shape Interface Example ===\n")
    
    # Define shape parameters
    batch_size = 4
    sequence_length = 2048
    kv_lens = 4096  # For attention operators
    
    print(f"Shape parameters:")
    print(f"  Batch size: {batch_size}")
    print(f"  Sequence length: {sequence_length}")
    print(f"  KV cache length: {kv_lens}")
    print()
    
    # Create hardware specs for performance modeling
    hardware = HardwareSpecs(
        name="Example GPU",
        peak_flops={'fp16': 100.0, 'bf16': 100.0, 'fp32': 50.0},
        memory_bandwidth_gbps=1000.0,
        memory_size_gb=40
    )
    
    # Example 1: AttentionOperator
    print("1. AttentionOperator Example:")
    attention_op = AttentionOperator(
        hidden_size=4096,
        num_heads=32,
        num_kv_heads=32  # Multi-head attention
    )
    
    # Set shape before computing metrics
    attention_op.set_shape(batch_size, sequence_length, kv_lens)
    
    # Now compute metrics without parameters
    flops = attention_op.compute_flops()
    memory_capacity = attention_op.compute_memory_capacity_bytes()
    memory_movement = attention_op.compute_memory_movement_bytes()
    params = attention_op.compute_params()
    
    print(f"   FLOPs: {flops:,}")
    print(f"   Memory Capacity: {memory_capacity:,} bytes ({memory_capacity/1e9:.2f} GB)")
    print(f"   Memory Movement: {memory_movement:,} bytes ({memory_movement/1e9:.2f} GB)")
    print(f"   Parameters: {params:,}")
    
    # Compute comprehensive metrics with hardware
    metrics = attention_op.compute_metrics(hardware)
    print(f"   Execution Time: {metrics.execution_time_ms:.2f} ms")
    print(f"   Arithmetic Intensity: {metrics.arithmetic_intensity:.2f} FLOP/Byte")
    print()
    
    # Example 2: MLPOperator
    print("2. MLPOperator Example:")
    mlp_op = MLPOperator(
        hidden_size=4096,
        intermediate_size=11008
    )
    
    # Set shape before computing metrics
    mlp_op.set_shape(batch_size, sequence_length)
    
    # Compute metrics
    flops = mlp_op.compute_flops()
    memory_capacity = mlp_op.compute_memory_capacity_bytes()
    memory_movement = mlp_op.compute_memory_movement_bytes()
    params = mlp_op.compute_params()
    
    print(f"   FLOPs: {flops:,}")
    print(f"   Memory Capacity: {memory_capacity:,} bytes ({memory_capacity/1e9:.2f} GB)")
    print(f"   Memory Movement: {memory_movement:,} bytes ({memory_movement/1e9:.2f} GB)")
    print(f"   Parameters: {params:,}")
    print()
    
    # Example 3: MoEOperator
    print("3. MoEOperator Example:")
    moe_op = MoEOperator(
        hidden_size=4096,
        intermediate_size=1407,
        num_experts=64,
        experts_per_token=6
    )
    
    # Set shape before computing metrics
    moe_op.set_shape(batch_size, sequence_length)
    
    # Compute metrics
    flops = moe_op.compute_flops()
    memory_capacity = moe_op.compute_memory_capacity_bytes()
    memory_movement = moe_op.compute_memory_movement_bytes()
    params = moe_op.compute_params()
    
    print(f"   FLOPs: {flops:,}")
    print(f"   Memory Capacity: {memory_capacity:,} bytes ({memory_capacity/1e9:.2f} GB)")
    print(f"   Memory Movement: {memory_movement:,} bytes ({memory_movement/1e9:.2f} GB)")
    print(f"   Parameters: {params:,}")
    print()
    
    # Example 4: Error handling when shape is not set
    print("4. Error Handling Example:")
    print("   Creating operator without setting shape...")
    
    test_op = MatMulOperator(M=1, N=4096, K=4096)
    
    try:
        test_op.compute_flops()
        print("   ❌ No error raised (unexpected)")
    except ValueError as e:
        print(f"   ✅ Correctly caught error: {e}")
    
    print("\n   Setting shape and trying again...")
    test_op.set_shape(batch_size, sequence_length)
    flops = test_op.compute_flops()
    print(f"   ✅ Success! FLOPs: {flops:,}")
    print()
    
    # Example 5: Shape propagation to child operators
    print("5. Shape Propagation Example:")
    print("   Creating AttentionOperator with child MatMul operators...")
    
    attention_op2 = AttentionOperator(hidden_size=2048, num_heads=16)
    
    # Get child operators before setting shape
    child_ops = attention_op2.get_matmul_operators()
    print(f"   Found {len(child_ops)} child MatMul operators")
    
    # Set shape on parent - should propagate to children
    attention_op2.set_shape(batch_size, sequence_length, kv_lens)
    
    # Verify children can compute metrics
    for name, child_op in child_ops.items():
        try:
            child_flops = child_op.compute_flops()
            print(f"   ✅ {name}: {child_flops:,} FLOPs")
        except ValueError as e:
            print(f"   ❌ {name}: {e}")
    
    print("\n=== Example Complete ===")

if __name__ == "__main__":
    main()
