#!/usr/bin/env python3
"""
Mixed Precision MoE Example

This example demonstrates mixed precision support specifically for Mixture of Experts (MoE) models.
MoE models have additional complexity with expert parameters that can benefit from different
precision settings than the attention parameters.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from llm_modeling_metrics.models.moe_model import <PERSON>EModel


def create_deepseek_moe_config():
    """Create a DeepSeek MoE-style model configuration."""
    return {
        'hidden_size': 4096,
        'num_hidden_layers': 27,
        'num_attention_heads': 32,
        'num_key_value_heads': 32,
        'intermediate_size': 11008,
        'moe_intermediate_size': 1407,
        'vocab_size': 102400,
        'max_position_embeddings': 4096,
        'model_type': 'deepseek',
        'tie_word_embeddings': False,
        'rms_norm_eps': 1e-6,
        
        # MoE-specific parameters
        'n_shared_experts': 2,
        'n_routed_experts': 64,
        'num_experts_per_tok': 6,
        'moe_layer_freq': 1,
        'first_k_dense_replace': 1,
        'routed_scaling_factor': 1.0,
        
        # DeepSeek MLA parameters
        'kv_lora_rank': 512,
        'q_lora_rank': 1536,
        'qk_rope_head_dim': 64,
        'v_head_dim': 128,
        'qk_nope_head_dim': 128,
    }


def format_bytes(bytes_value):
    """Format bytes in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"


def main():
    print("=== Mixed Precision MoE Example ===\n")
    
    # Create MoE model instance
    config = create_deepseek_moe_config()
    model = MoEModel("deepseek-moe", config)
    
    # Analysis parameters
    sequence_length = 4096
    batch_size = 1
    
    print(f"Model: DeepSeek MoE (64 experts, 6 active)")
    print(f"Sequence length: {sequence_length}")
    print(f"Batch size: {batch_size}")
    print()
    
    # 1. Baseline: All BF16
    print("=== 1. Baseline: All BF16 ===")
    baseline_memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',  # Legacy parameter
        include_kv_cache=True
    )
    
    print(f"Total memory: {format_bytes(baseline_memory['total'])}")
    print(f"Attention parameters: {format_bytes(baseline_memory.get('attention_parameters', 0))}")
    print(f"Expert parameters: {format_bytes(baseline_memory.get('expert_parameters', 0))}")
    print(f"Activations: {format_bytes(baseline_memory['activations'])}")
    print(f"KV Cache: {format_bytes(baseline_memory['kv_cache'])}")
    print()
    
    # 2. MoE-Optimized Mixed Precision
    print("=== 2. MoE-Optimized Mixed Precision ===")
    moe_optimized = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='bf16',                    # Base weight precision
        activation_dtype='bf16',                # Keep activations high precision
        kv_cache_dtype='fp8',                   # Compress KV cache
        attention_parameter_dtype='bf16',       # Keep attention params high precision
        expert_parameter_dtype='fp8',           # Compress expert params (most memory)
        include_kv_cache=True
    )
    
    print(f"Total memory: {format_bytes(moe_optimized['total'])}")
    print(f"Attention parameters: {format_bytes(moe_optimized.get('attention_parameters', 0))}")
    print(f"Expert parameters: {format_bytes(moe_optimized.get('expert_parameters', 0))}")
    print(f"Activations: {format_bytes(moe_optimized['activations'])}")
    print(f"KV Cache: {format_bytes(moe_optimized['kv_cache'])}")
    
    # Calculate savings
    moe_savings = baseline_memory['total'] - moe_optimized['total']
    moe_percent = (moe_savings / baseline_memory['total']) * 100
    print(f"Memory savings: {format_bytes(moe_savings)} ({moe_percent:.1f}%)")
    print()
    
    # 3. Aggressive MoE Mixed Precision
    print("=== 3. Aggressive MoE Mixed Precision ===")
    aggressive_moe = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='int8',                    # Quantize base weights
        activation_dtype='bf16',                # Keep activations stable
        kv_cache_dtype='fp8',                   # Compress KV cache
        attention_parameter_dtype='fp8',        # Compress attention params
        expert_parameter_dtype='fp4',           # Aggressively compress experts
        include_kv_cache=True
    )
    
    print(f"Total memory: {format_bytes(aggressive_moe['total'])}")
    print(f"Attention parameters: {format_bytes(aggressive_moe.get('attention_parameters', 0))}")
    print(f"Expert parameters: {format_bytes(aggressive_moe.get('expert_parameters', 0))}")
    print(f"Activations: {format_bytes(aggressive_moe['activations'])}")
    print(f"KV Cache: {format_bytes(aggressive_moe['kv_cache'])}")
    
    # Calculate savings
    aggressive_savings = baseline_memory['total'] - aggressive_moe['total']
    aggressive_percent = (aggressive_savings / baseline_memory['total']) * 100
    print(f"Memory savings: {format_bytes(aggressive_savings)} ({aggressive_percent:.1f}%)")
    print()
    
    # 4. Expert-Focused Analysis
    print("=== 4. Expert Parameter Impact Analysis ===")
    expert_precisions = ['bf16', 'fp8', 'fp4']
    
    print(f"{'Expert Precision':<15} {'Expert Memory':<15} {'Total Memory':<15} {'Savings':<10}")
    print("-" * 60)
    
    for precision in expert_precisions:
        memory = model.compute_memory_requirements(
            sequence_length=sequence_length,
            batch_size=batch_size,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='bf16',
            attention_parameter_dtype='bf16',
            expert_parameter_dtype=precision,
            include_kv_cache=True
        )
        
        expert_mem = memory.get('expert_parameters', 0)
        total_mem = memory['total']
        savings = ((baseline_memory['total'] - total_mem) / baseline_memory['total']) * 100
        
        print(f"{precision:<15} {format_bytes(expert_mem):<15} {format_bytes(total_mem):<15} {savings:>6.1f}%")
    
    print()
    
    # 5. Training Configuration for MoE
    print("=== 5. MoE Training Configuration ===")
    training_memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='bf16',                    # Weights in BF16
        activation_dtype='bf16',                # Activations in BF16
        attention_parameter_dtype='bf16',       # Attention params in BF16
        expert_parameter_dtype='bf16',          # Keep experts in BF16 for training
        grad_dtype='fp16',                      # Gradients in FP16
        optimizer_dtype='fp32',                 # Optimizer in FP32
        training=True,
        include_kv_cache=False
    )
    
    print(f"Total memory: {format_bytes(training_memory['total'])}")
    print(f"Parameters: {format_bytes(training_memory['parameters'])}")
    print(f"Activations: {format_bytes(training_memory['activations'])}")
    print(f"Gradients: {format_bytes(training_memory['gradients'])}")
    print(f"Optimizer: {format_bytes(training_memory['optimizer'])}")
    print()
    
    # 6. Comparison Summary
    print("=== 6. MoE Mixed Precision Summary ===")
    print(f"{'Configuration':<25} {'Total Memory':<15} {'Expert Memory':<15} {'Savings':<10}")
    print("-" * 70)
    
    configs = [
        ("Baseline (BF16)", baseline_memory, 0),
        ("MoE Optimized", moe_optimized, moe_percent),
        ("Aggressive MoE", aggressive_moe, aggressive_percent),
    ]
    
    for name, memory, savings in configs:
        total_gb = memory['total'] / (1024**3)
        expert_gb = memory.get('expert_parameters', 0) / (1024**3)
        savings_str = f"{savings:.1f}%" if savings > 0 else "Baseline"
        print(f"{name:<25} {total_gb:>10.2f} GB {expert_gb:>10.2f} GB {savings_str:>10}")
    
    print()
    
    # 7. MoE-Specific Recommendations
    print("=== 7. MoE-Specific Recommendations ===")
    print("• Inference (Balanced): Use MoE-optimized mixed precision")
    print("  - attention_parameter_dtype='bf16', expert_parameter_dtype='fp8'")
    print("  - Maintains quality while significantly reducing expert memory")
    print()
    print("• Inference (Memory Critical): Use aggressive expert quantization")
    print("  - expert_parameter_dtype='fp4' can save 75% of expert memory")
    print("  - Monitor quality degradation, especially for specialized tasks")
    print()
    print("• Training: Keep experts in BF16 during training")
    print("  - expert_parameter_dtype='bf16' for stability")
    print("  - Use FP16 gradients and FP32 optimizer states")
    print()
    print("• Expert Scaling: FP8/FP4 expert precision becomes more beneficial")
    print("  - As number of experts increases, precision impact grows")
    print("  - Consider per-expert precision based on usage patterns")
    print()


if __name__ == "__main__":
    main()