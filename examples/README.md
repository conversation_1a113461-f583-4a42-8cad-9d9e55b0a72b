# LLM Modeling Metrics Examples

This directory contains example scripts demonstrating various features of the LLM modeling metrics package.

## Available Examples

### Core Functionality

- **[operator_analysis_example.py](operator_analysis_example.py)** - Demonstrates operator-based modeling and analysis similar to research papers
- **[attention_operators_example.py](attention_operators_example.py)** - Shows attention operator analysis and optimization

### Mixed Precision Examples

- **[mixed_precision_basic_example.py](mixed_precision_basic_example.py)** - Basic introduction to mixed precision concepts and usage
- **[mixed_precision_moe_example.py](mixed_precision_moe_example.py)** - Mixed precision optimization specifically for MoE models
- **[mixed_precision_optimization_example.py](mixed_precision_optimization_example.py)** - Systematic optimization and trade-off analysis

## Running the Examples

### Prerequisites

Make sure you have the package installed:

```bash
pip install -e .
```

### Basic Usage

Run any example script directly:

```bash
python examples/mixed_precision_basic_example.py
```

### Example Output

Each example provides detailed output showing:
- Memory usage breakdowns
- Optimization recommendations
- Performance trade-offs
- Best practices

## Mixed Precision Examples Overview

### Basic Example

The basic example demonstrates:
- Baseline memory usage with BF16
- Optimized mixed precision configuration
- Aggressive quantization for maximum memory savings
- Training configuration with mixed precision
- Comparison table and recommendations

**Key Learning**: Start with conservative mixed precision and gradually optimize based on your requirements.

### MoE Example

The MoE example focuses on:
- Expert parameter optimization (largest memory component in MoE)
- Different precision strategies for experts vs attention
- Impact analysis of expert quantization
- MoE-specific recommendations

**Key Learning**: Expert parameters are prime candidates for aggressive quantization in MoE models.

### Optimization Example

The optimization example provides:
- Systematic exploration of precision combinations
- Scenario-based recommendations
- Quality vs memory trade-off analysis
- Hardware-specific considerations

**Key Learning**: Different deployment scenarios require different optimization strategies.

## Customizing Examples

### Adding Your Own Model

To test with your own model configuration:

```python
def create_your_model_config():
    """Create your custom model configuration."""
    return {
        'hidden_size': 4096,
        'num_hidden_layers': 32,
        'num_attention_heads': 32,
        # ... your model parameters
    }

# Use in examples
config = create_your_model_config()
model = DenseModel("your-model", config)
```

### Custom Quality Validation

Implement your own quality validation:

```python
def your_quality_validator(config):
    """Implement your quality validation logic."""
    
    # Run your model with the config
    # Compute your quality metrics
    # Return validation results
    
    return {
        'quality_score': 0.95,  # Your metric
        'passed_validation': True
    }
```

### Hardware-Specific Testing

Test with your specific hardware:

```python
def get_your_hardware_config():
    """Get configuration optimized for your hardware."""
    
    # Check your GPU model
    import torch
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name()
        print(f"Optimizing for: {gpu_name}")
    
    # Return hardware-optimized config
    return {
        'weight_dtype': 'bf16',
        'activation_dtype': 'bf16',
        'kv_cache_dtype': 'fp8'  # Adjust based on your GPU
    }
```

## Best Practices for Using Examples

1. **Start with Basic Example**: Understand fundamental concepts first
2. **Validate on Your Data**: Always test quality with your specific use case
3. **Monitor in Production**: Use examples as starting points, not final solutions
4. **Iterate Gradually**: Make incremental changes and validate each step
5. **Document Results**: Keep track of what works for your specific scenario

## Common Use Cases

### Memory-Constrained Deployment

If you have limited GPU memory:

```bash
# Focus on aggressive optimization example
python examples/mixed_precision_optimization_example.py
```

Look for configurations with highest memory savings while maintaining acceptable quality.

### Quality-Critical Applications

If quality is paramount:

```bash
# Start with basic example, conservative settings
python examples/mixed_precision_basic_example.py
```

Focus on configurations that maintain >95% of baseline quality.

### MoE Model Deployment

For Mixture of Experts models:

```bash
# Use MoE-specific example
python examples/mixed_precision_moe_example.py
```

Pay special attention to expert parameter optimization strategies.

### Research and Development

For research purposes:

```bash
# Use optimization example for comprehensive analysis
python examples/mixed_precision_optimization_example.py
```

Explore the full range of precision combinations and their trade-offs.

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure the package is installed (`pip install -e .`)
2. **Memory Errors**: Reduce batch size or sequence length in examples
3. **Hardware Compatibility**: Some precisions may not be supported on older GPUs
4. **Quality Degradation**: Start with more conservative precision settings

### Getting Help

- Check the [Mixed Precision Guide](../docs/mixed_precision_guide.md) for detailed explanations
- Review [Best Practices](../docs/mixed_precision_best_practices.md) for production guidance
- Follow the [Migration Guide](../docs/mixed_precision_migration.md) for systematic adoption

## Contributing

To add new examples:

1. Follow the existing example structure
2. Include comprehensive comments and documentation
3. Provide clear output and recommendations
4. Test with different model configurations
5. Update this README with your new example

## Next Steps

After running the examples:

1. **Implement Quality Validation**: Add your specific quality metrics
2. **Test on Real Data**: Validate with your actual use cases
3. **Monitor in Production**: Set up monitoring for your deployment
4. **Iterate and Optimize**: Continuously improve based on results

For production deployment, always refer to the [Best Practices Guide](../docs/mixed_precision_best_practices.md) and follow the [Migration Guide](../docs/mixed_precision_migration.md).