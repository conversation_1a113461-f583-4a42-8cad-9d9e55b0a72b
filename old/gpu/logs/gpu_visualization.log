2025-08-13 20:07:23,906 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:63]
2025-08-13 20:07:23,906 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:68]
2025-08-13 20:16:49,722 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:63]
2025-08-13 20:16:49,722 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:68]
2025-08-13 20:16:55,907 ERROR: Error rendering index page: No route exists for name "static" and params "filename". [in /Users/<USER>/code/llm/modeling/gpu/web_interface/routes.py:25]
2025-08-13 20:16:55,910 ERROR: Unhandled exception: No route exists for name "static" and params "filename". [in /Users/<USER>/code/llm/modeling/gpu/app.py:52]
2025-08-13 20:16:56,249 ERROR: Unhandled exception: No route exists for name "static" and params "filename". [in /Users/<USER>/code/llm/modeling/gpu/app.py:52]
2025-08-13 20:17:32,514 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:66]
2025-08-13 20:17:32,514 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:71]
2025-08-13 20:17:44,969 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:69]
2025-08-13 20:17:44,969 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:74]
2025-08-13 20:18:21,355 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:77]
2025-08-13 20:18:31,086 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:18:31,086 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:20:32,706 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:20:32,706 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:22:05,457 ERROR: Error rendering index page: No route exists for name "static" and params "filename". [in /Users/<USER>/code/llm/modeling/gpu/web_interface/routes.py:25]
2025-08-13 20:24:07,053 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:24:07,070 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:24:08,021 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:24:08,022 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:24:08,101 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:24:08,101 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:24:14,870 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:24:14,899 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:24:15,829 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:24:15,829 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:24:15,829 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:24:15,829 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:47:16,737 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:47:16,795 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:47:17,720 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:47:17,720 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:47:17,788 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:47:17,788 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:47:24,389 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:47:24,460 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:47:25,225 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:47:25,225 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:47:25,396 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:47:25,396 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:47:59,960 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:48:00,012 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-13 20:48:01,029 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:48:01,032 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-13 20:48:01,139 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-13 20:48:01,139 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:32:11,051 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:32:11,051 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:32:53,386 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:34:57,573 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:34:57,574 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:36:08,780 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:438]
2025-08-15 09:36:08,819 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 09:36:08,831 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:36:08,832 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:36:08,832 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:36:08,833 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:36:08,833 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:36:08,833 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 09:36:08,833 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 09:36:08,844 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 09:36:08,848 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:499]
2025-08-15 09:36:08,850 ERROR: Unhandled exception: Out of range float values are not JSON compliant [in /Users/<USER>/code/llm/modeling/gpu/app.py:87]
2025-08-15 09:36:08,864 ERROR: Unhandled exception: Out of range float values are not JSON compliant [in /Users/<USER>/code/llm/modeling/gpu/app.py:87]
2025-08-15 09:37:08,387 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:37:09,383 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:37:09,383 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:37:18,781 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:37:19,699 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:37:19,699 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:37:24,654 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:38:03,231 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:38:03,231 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:39:17,285 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:464]
2025-08-15 09:39:17,332 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 09:39:17,340 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:39:17,341 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:39:17,342 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:39:17,342 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:39:17,343 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:39:17,343 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 09:39:17,343 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 09:39:17,351 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 09:39:17,356 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:525]
2025-08-15 09:43:01,135 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:43:02,059 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:43:02,059 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:43:16,932 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:44:06,312 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:44:06,313 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:44:54,923 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:44:54,923 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:44:57,992 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 09:44:58,041 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 09:44:58,057 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:44:58,058 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:44:58,067 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:44:58,076 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:44:58,077 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:44:58,085 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 09:44:58,086 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 09:44:58,093 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 09:44:58,095 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 09:49:53,723 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:50:10,803 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:50:10,804 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:50:29,317 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 09:50:29,366 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 09:50:29,385 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:50:29,408 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:50:29,414 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:50:29,415 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:50:29,416 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:50:29,416 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 09:50:29,417 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 09:50:29,424 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 09:50:29,425 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 09:56:38,844 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 09:56:51,408 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 09:56:51,408 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 09:56:54,885 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 09:56:54,933 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 09:56:54,939 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:56:54,939 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:56:54,940 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:56:54,941 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:56:54,941 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:380]
2025-08-15 09:56:54,942 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 09:56:54,942 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 09:56:54,955 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 09:56:54,958 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 10:04:05,292 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:04:06,390 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:04:06,390 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:04:12,005 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 10:04:12,059 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 10:04:12,064 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:386]
2025-08-15 10:04:12,067 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:386]
2025-08-15 10:04:12,068 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:386]
2025-08-15 10:04:12,068 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:386]
2025-08-15 10:04:12,069 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:386]
2025-08-15 10:04:12,069 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 10:04:12,069 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 10:04:12,094 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 10:04:12,097 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 10:04:19,174 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:04:20,240 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:04:20,240 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:04:36,307 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 10:04:36,364 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 10:04:36,378 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:395]
2025-08-15 10:04:36,379 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:395]
2025-08-15 10:04:36,379 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:395]
2025-08-15 10:04:36,380 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:395]
2025-08-15 10:04:36,380 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:395]
2025-08-15 10:04:36,380 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 10:04:36,381 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 10:04:36,391 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 10:04:36,392 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 10:05:43,394 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:05:44,455 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:05:44,455 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:05:55,993 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:05:57,167 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:05:57,167 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:06:03,225 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:08:23,167 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:08:23,167 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:08:26,236 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 10:08:26,286 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 10:08:26,294 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:506]
2025-08-15 10:08:26,294 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:506]
2025-08-15 10:08:26,295 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:506]
2025-08-15 10:08:26,296 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:506]
2025-08-15 10:08:26,297 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:506]
2025-08-15 10:08:26,297 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 10:08:26,297 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 10:08:26,327 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 10:08:26,328 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 10:16:31,006 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:16:31,962 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:16:31,962 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:16:37,821 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:16:38,550 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:16:38,550 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:16:52,033 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:16:52,910 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:16:52,910 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:16:59,896 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:17:00,726 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:17:00,726 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:19:33,033 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:19:33,969 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:19:33,969 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:19:40,335 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:19:41,228 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:19:41,228 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:20:34,847 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:20:35,952 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:20:35,952 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:20:48,374 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:20:49,425 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:20:49,425 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:23:40,583 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:23:41,378 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:23:41,378 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:23:47,848 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:23:48,586 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:23:48,586 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:24:36,579 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:24:37,427 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:24:37,427 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:25:02,738 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:25:03,766 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:25:03,766 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:25:49,273 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:25:50,242 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:25:50,243 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 10:26:12,758 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 10:26:13,822 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 10:26:13,822 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:18:44,865 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 14:18:44,899 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 14:18:44,910 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:18:44,911 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:18:44,912 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:18:44,912 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:18:44,914 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:18:44,941 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 14:18:44,942 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 14:18:44,970 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 14:18:44,974 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 14:21:05,219 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:21:06,137 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:21:06,137 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:21:09,168 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:21:09,906 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:21:09,906 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:21:20,128 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 14:21:20,195 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 14:21:20,209 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:21:20,210 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:21:20,211 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:21:20,211 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:21:20,211 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:606]
2025-08-15 14:21:20,212 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 14:21:20,212 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 14:21:20,225 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 14:21:20,226 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 14:22:58,004 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:22:59,045 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:22:59,045 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:23:00,474 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 14:23:00,492 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:78]
2025-08-15 14:23:00,505 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:608]
2025-08-15 14:23:00,506 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:608]
2025-08-15 14:23:00,506 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:608]
2025-08-15 14:23:00,507 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:608]
2025-08-15 14:23:00,507 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:608]
2025-08-15 14:23:00,508 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:105]
2025-08-15 14:23:00,508 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 14:23:00,525 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 14:23:00,527 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 14:23:05,719 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:23:06,701 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:23:06,701 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:49:06,462 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:49:07,469 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:49:07,469 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:50:04,162 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:50:05,036 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:50:05,037 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:50:06,150 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:50:06,911 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:50:06,911 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:53:15,873 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:53:17,084 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:53:17,084 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:53:37,828 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:53:38,812 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:53:38,812 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:54:13,179 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:54:14,059 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:54:14,059 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:54:18,505 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:54:19,303 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:54:19,303 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:55:42,641 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:55:43,478 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:55:43,478 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:55:49,134 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:55:49,889 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:55:49,889 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:58:10,533 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 14:58:11,761 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 14:58:11,761 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 14:59:22,609 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:07:10,893 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:07:10,893 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:07:18,903 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 15:07:18,917 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:77]
2025-08-15 15:07:18,924 INFO: Merged 2 conflicting records for FP16/BF16 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:629]
2025-08-15 15:07:18,925 INFO: Merged 3 conflicting records for FP4 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:629]
2025-08-15 15:07:18,925 INFO: Merged 3 conflicting records for FP8/FP6 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:629]
2025-08-15 15:07:18,925 INFO: Merged 2 conflicting records for INT8 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:629]
2025-08-15 15:07:18,925 INFO: Merged 2 conflicting records for TF32 Tensor Core Dense/Sparse [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:629]
2025-08-15 15:07:18,925 INFO: Consolidated 19 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:104]
2025-08-15 15:07:18,925 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 15:07:18,931 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 15:07:18,933 INFO: Data loaded successfully: 19 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 15:30:12,740 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:30:13,594 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:30:13,594 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:30:20,968 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:30:21,749 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:30:21,749 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:30:41,558 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:30:42,572 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:30:42,573 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:30:49,645 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:30:50,383 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:30:50,383 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:32:15,854 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:32:16,764 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:32:16,764 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:32:21,817 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:32:22,561 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:32:22,562 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:35:52,397 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:35:53,216 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:35:53,216 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:35:58,372 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:35:59,105 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:35:59,105 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:38:55,109 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:38:56,151 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:38:56,151 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:39:12,852 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:39:13,607 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:39:13,607 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:39:18,156 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:39:18,886 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:39:18,886 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:39:43,745 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:39:44,783 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:39:44,783 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:40:15,608 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:40:16,337 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:40:16,337 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 15:45:56,143 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
2025-08-15 15:53:52,096 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-15 15:53:52,097 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-15 16:12:38,966 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-15 16:12:38,985 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:77]
2025-08-15 16:12:38,996 INFO: Consolidated 7 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:104]
2025-08-15 16:12:38,996 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-15 16:12:39,000 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-15 16:12:39,015 WARNING: Error generating system processing summary: 'gpu_model' [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:513]
2025-08-15 16:12:39,015 INFO: Data loaded successfully: 7 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-15 16:44:07,290 WARNING: Error generating system processing summary: 'gpu_model' [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:162]
Traceback (most recent call last):
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3790, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 152, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 181, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7080, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'gpu_model'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/code/llm/modeling/gpu/web_interface/api.py", line 157, in get_systems
    summary = processor.get_system_processing_summary(system_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py", line 515, in get_system_processing_summary
    gpu_models = system_df['gpu_model'].dropna().unique().tolist()
                 ~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/frame.py", line 3896, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3797, in get_loc
    raise KeyError(key) from err
KeyError: 'gpu_model'
2025-08-15 16:44:11,453 WARNING: Error generating system processing summary: 'gpu_model' [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:162]
Traceback (most recent call last):
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3790, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 152, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 181, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7080, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'gpu_model'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/code/llm/modeling/gpu/web_interface/api.py", line 157, in get_systems
    summary = processor.get_system_processing_summary(system_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py", line 515, in get_system_processing_summary
    gpu_models = system_df['gpu_model'].dropna().unique().tolist()
                 ~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/frame.py", line 3896, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3797, in get_loc
    raise KeyError(key) from err
KeyError: 'gpu_model'
2025-08-15 16:44:13,823 WARNING: Error generating system processing summary: 'gpu_model' [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:162]
Traceback (most recent call last):
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3790, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 152, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 181, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7080, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'gpu_model'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/code/llm/modeling/gpu/web_interface/api.py", line 157, in get_systems
    summary = processor.get_system_processing_summary(system_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py", line 515, in get_system_processing_summary
    gpu_models = system_df['gpu_model'].dropna().unique().tolist()
                 ~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/frame.py", line 3896, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3797, in get_loc
    raise KeyError(key) from err
KeyError: 'gpu_model'
2025-08-18 11:55:49,914 INFO: Configuration validated successfully [in /Users/<USER>/code/llm/modeling/gpu/app.py:17]
2025-08-18 11:55:49,914 INFO: GPU Visualization Tool startup [in /Users/<USER>/code/llm/modeling/gpu/app.py:22]
2025-08-18 11:55:53,706 INFO: Loading data from markdown files [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:462]
2025-08-18 11:55:53,727 INFO: Starting GPU data consolidation [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:77]
2025-08-18 11:55:53,737 INFO: Consolidated 7 GPU records [in /Users/<USER>/code/llm/modeling/gpu/data_processor/gpu_data_processor.py:104]
2025-08-18 11:55:53,737 INFO: Starting system configuration processing [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:59]
2025-08-18 11:55:53,741 INFO: Processed 10 system configurations [in /Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py:89]
2025-08-18 11:55:53,745 WARNING: Error generating system processing summary: 'gpu_model' [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:513]
2025-08-18 11:55:53,745 INFO: Data loaded successfully: 7 GPUs, 10 systems [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:523]
2025-08-18 11:55:53,754 WARNING: Error generating system processing summary: 'gpu_model' [in /Users/<USER>/code/llm/modeling/gpu/web_interface/api.py:162]
Traceback (most recent call last):
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3790, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 152, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 181, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7080, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'gpu_model'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/code/llm/modeling/gpu/web_interface/api.py", line 157, in get_systems
    summary = processor.get_system_processing_summary(system_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/llm/modeling/gpu/data_processor/system_data_processor.py", line 515, in get_system_processing_summary
    gpu_models = system_df['gpu_model'].dropna().unique().tolist()
                 ~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/frame.py", line 3896, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3797, in get_loc
    raise KeyError(key) from err
KeyError: 'gpu_model'
2025-08-18 14:06:21,122 INFO: GPU Visualization Tool shutdown [in /Users/<USER>/code/llm/modeling/gpu/app.py:25]
