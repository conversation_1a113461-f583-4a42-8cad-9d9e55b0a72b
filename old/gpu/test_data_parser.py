"""
Unit tests for the data parser module.
"""

import unittest
import tempfile
import os
from pathlib import Path
import pandas as pd

from data_parser import Markdown<PERSON>arser, TableExtractor, SpecificationNormalizer


class TestTableExtractor(unittest.TestCase):
    """Test cases for TableExtractor class."""
    
    def setUp(self):
        self.extractor = TableExtractor()
    
    def test_extract_simple_markdown_table(self):
        """Test extraction of a simple markdown table."""
        content = """
# GPU Specifications

| GPU Model | Memory | Performance |
|-----------|--------|-------------|
| H100 | 80 GB | 1000 TFLOPS |
| A100 | 40 GB | 500 TFLOPS |
"""
        tables = self.extractor.extract_tables_from_markdown(content)
        
        self.assertEqual(len(tables), 1)
        table = tables[0]
        self.assertEqual(table['type'], 'markdown')
        self.assertEqual(len(table['headers']), 3)
        self.assertEqual(len(table['data']), 2)
        self.assertEqual(table['headers'], ['GPU Model', 'Memory', 'Performance'])
    
    def test_extract_table_with_dense_sparse_values(self):
        """Test extraction of table with dense/sparse values."""
        content = """
| GPU | FP4 Tensor | FP8 Tensor |
|-----|------------|------------|
| B200 | 72/144 petaFLOPS | 36/72 petaFLOPS |
| B300 | 105/144 petaFLOPS | 36/72 petaFLOPS |
"""
        tables = self.extractor.extract_tables_from_markdown(content)
        
        self.assertEqual(len(tables), 1)
        table = tables[0]
        self.assertEqual(len(table['data']), 2)
        self.assertIn('72/144 petaFLOPS', table['data'][0])
        self.assertIn('105/144 petaFLOPS', table['data'][1])
    
    def test_convert_to_dataframe(self):
        """Test conversion of table data to DataFrame."""
        table_data = {
            'type': 'markdown',
            'headers': ['GPU', 'Memory', 'Performance'],
            'data': [
                ['H100', '80 GB', '1000 TFLOPS'],
                ['A100', '40 GB', '500 TFLOPS']
            ]
        }
        
        df = self.extractor.convert_to_dataframe(table_data)
        
        self.assertFalse(df.empty)
        self.assertEqual(len(df), 2)
        self.assertEqual(len(df.columns), 3)
        self.assertEqual(df.iloc[0, 0], 'H100')
        self.assertEqual(df.iloc[1, 0], 'A100')
    
    def test_handle_malformed_table(self):
        """Test handling of malformed tables."""
        content = """
| GPU | Memory |
| H100 | 80 GB | Extra column |
| A100 |
"""
        tables = self.extractor.extract_tables_from_markdown(content)
        
        # Should still extract the table but handle inconsistent columns
        if tables:
            df = self.extractor.convert_to_dataframe(tables[0])
            self.assertFalse(df.empty)
    
    def test_no_tables_in_content(self):
        """Test content with no tables."""
        content = """
# Some heading

This is just regular text with no tables.
Some more text here.
"""
        tables = self.extractor.extract_tables_from_markdown(content)
        self.assertEqual(len(tables), 0)


class TestSpecificationNormalizer(unittest.TestCase):
    """Test cases for SpecificationNormalizer class."""
    
    def setUp(self):
        self.normalizer = SpecificationNormalizer()
    
    def test_extract_dense_value_simple(self):
        """Test extraction of dense values from dense/sparse format."""
        test_cases = [
            ('105/144 petaFLOPS', 105.0),
            ('72/144', 72.0),
            ('1.1/2.2 TFLOPS', 1.1),
            ('500 TFLOPS', 500.0),
            ('80 GB', 80.0),
            ('invalid', None)
        ]
        
        for input_val, expected in test_cases:
            with self.subTest(input_val=input_val):
                result = self.normalizer.extract_dense_value(input_val)
                self.assertEqual(result, expected)
    
    def test_normalize_unit_petaflops(self):
        """Test normalization of petaFLOPS to teraFLOPS."""
        result = self.normalizer.normalize_unit('105 petaFLOPS')
        
        self.assertEqual(result['normalized_value'], 105000.0)  # 105 * 1000
        self.assertEqual(result['normalized_unit'], 'TFLOPS')
        self.assertEqual(result['original_value'], '105 petaFLOPS')
    
    def test_normalize_unit_dense_sparse(self):
        """Test normalization of dense/sparse values."""
        result = self.normalizer.normalize_unit('72/144 petaFLOPS')
        
        self.assertEqual(result['normalized_value'], 72000.0)  # 72 * 1000
        self.assertEqual(result['normalized_unit'], 'TFLOPS')
    
    def test_normalize_unit_memory(self):
        """Test normalization of memory units."""
        test_cases = [
            ('80 GB', 80.0, 'GB'),
            ('1.4 TB', 1433.6, 'GB'),  # 1.4 * 1024
            ('50 MB', 0.05, 'GB')      # 50 * 0.001
        ]
        
        for input_val, expected_value, expected_unit in test_cases:
            with self.subTest(input_val=input_val):
                result = self.normalizer.normalize_unit(input_val)
                self.assertAlmostEqual(result['normalized_value'], expected_value, places=1)
                self.assertEqual(result['normalized_unit'], expected_unit)
    
    def test_normalize_unit_bandwidth(self):
        """Test normalization of bandwidth units."""
        test_cases = [
            ('64 TB/s', 65536.0, 'GB/s'),  # 64 * 1024
            ('8 TB/s', 8192.0, 'GB/s'),    # 8 * 1024
            ('1000 GB/s', 1000.0, 'GB/s'), # 1000 * 1
            ('500 MB/s', 0.5, 'GB/s')      # 500 * 0.001
        ]
        
        for input_val, expected_value, expected_unit in test_cases:
            with self.subTest(input_val=input_val):
                result = self.normalizer.normalize_unit(input_val)
                self.assertAlmostEqual(result['normalized_value'], expected_value, places=1)
                self.assertEqual(result['normalized_unit'], expected_unit)
    
    def test_categorize_specification(self):
        """Test specification categorization."""
        test_cases = [
            ('SMs', 'hardware_specs'),
            ('FP32 Cores / SM', 'hardware_specs'),
            ('FP4 Tensor TFLOPS', 'computing_capacity'),
            ('Memory Size', 'memory_info'),
            ('HBM Memory Bandwidth', 'memory_info'),
            ('TDP', 'system_info'),
            ('Random Spec', 'other')
        ]
        
        for spec_name, expected_category in test_cases:
            with self.subTest(spec_name=spec_name):
                result = self.normalizer.categorize_specification(spec_name)
                self.assertEqual(result, expected_category)
    
    def test_normalize_dataframe(self):
        """Test DataFrame normalization."""
        df = pd.DataFrame({
            'GPU Model': ['H100', 'A100'],
            'FP4 Performance': ['72/144 petaFLOPS', '50/100 petaFLOPS'],
            'Memory': ['80 GB', '40 GB']
        })
        
        normalized_df = self.normalizer.normalize_dataframe(df)
        
        # Check that values were normalized
        self.assertEqual(normalized_df.iloc[0]['FP4 Performance'], 72000.0)
        self.assertEqual(normalized_df.iloc[1]['FP4 Performance'], 50000.0)
        self.assertEqual(normalized_df.iloc[0]['Memory'], 80.0)
        
        # Check that metadata columns were added
        self.assertIn('FP4 Performance_normalized_unit', normalized_df.columns)
        self.assertIn('Memory_category', normalized_df.columns)


class TestMarkdownParser(unittest.TestCase):
    """Test cases for MarkdownParser class."""
    
    def setUp(self):
        self.parser = MarkdownParser()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_parse_file_with_gpu_table(self):
        """Test parsing a file with GPU specifications."""
        content = """
# NVIDIA GPU Specifications

| GPU Model | SMs | FP32 Performance | Memory |
|-----------|-----|------------------|--------|
| H100 | 132 | 66.9 TFLOPS | 80 GB |
| A100 | 108 | 19.5 TFLOPS | 40 GB |
"""
        
        # Create temporary file
        temp_file = Path(self.temp_dir) / 'gpu_specs.md'
        with open(temp_file, 'w') as f:
            f.write(content)
        
        result = self.parser.parse_file(str(temp_file))
        
        self.assertIsNotNone(result)
        self.assertEqual(len(result['tables']), 1)
        self.assertTrue(result['metadata']['gpu_related'])
        
        # Check DataFrame
        table = result['tables'][0]
        df = table['dataframe']
        self.assertEqual(len(df), 2)
        self.assertIn('H100', df.iloc[0].values)
        self.assertIn('A100', df.iloc[1].values)
    
    def test_parse_directory(self):
        """Test parsing a directory with multiple markdown files."""
        # Create multiple test files
        files_content = {
            'gpu1.md': """
# GPU Specs 1
| GPU | Performance |
|-----|-------------|
| H100 | 1000 TFLOPS |
""",
            'gpu2.md': """
# GPU Specs 2
| GPU | Memory |
|-----|--------|
| A100 | 40 GB |
""",
            'non_gpu.md': """
# Random Content
This file has no GPU-related content or tables.
"""
        }
        
        for filename, content in files_content.items():
            temp_file = Path(self.temp_dir) / filename
            with open(temp_file, 'w') as f:
                f.write(content)
        
        result = self.parser.parse_directory(self.temp_dir)
        
        self.assertEqual(result['metadata']['total_files'], 3)
        self.assertEqual(result['metadata']['files_with_tables'], 2)  # Only GPU files
        self.assertEqual(result['metadata']['total_tables'], 2)
    
    def test_extract_dense_values(self):
        """Test the extract_dense_values method."""
        test_cases = [
            ('105/144 petaFLOPS', 105.0),
            ('72/144', 72.0),
            ('500 TFLOPS', 500.0),
            ('invalid', None)
        ]
        
        for input_val, expected in test_cases:
            with self.subTest(input_val=input_val):
                result = self.parser.extract_dense_values(input_val)
                self.assertEqual(result, expected)
    
    def test_categorize_specifications(self):
        """Test specification categorization."""
        specs = {
            'SMs': 132,
            'FP32 Performance': '66.9 TFLOPS',
            'Memory Size': '80 GB',
            'TDP': '700 W',
            'Random Spec': 'Some value'
        }
        
        categorized = self.parser.categorize_specifications(specs)
        
        self.assertIn('SMs', categorized['hardware_specs'])
        self.assertIn('FP32 Performance', categorized['computing_capacity'])
        self.assertIn('Memory Size', categorized['memory_info'])
        self.assertIn('TDP', categorized['system_info'])
        self.assertIn('Random Spec', categorized['other'])
    
    def test_get_consolidated_dataframe(self):
        """Test creation of consolidated DataFrame."""
        # Create mock parsed data
        df1 = pd.DataFrame({
            'GPU': ['H100'],
            'Performance': [1000.0]
        })
        df2 = pd.DataFrame({
            'GPU': ['A100'],
            'Performance': [500.0]
        })
        
        parsed_data = {
            'consolidated_tables': [
                {'dataframe': df1, 'source_file': 'file1.md'},
                {'dataframe': df2, 'source_file': 'file2.md'}
            ]
        }
        
        consolidated_df = self.parser.get_consolidated_dataframe(parsed_data)
        
        self.assertEqual(len(consolidated_df), 2)
        self.assertIn('source_file', consolidated_df.columns)
        self.assertIn('H100', consolidated_df['GPU'].values)
        self.assertIn('A100', consolidated_df['GPU'].values)


class TestIntegrationWithRealFiles(unittest.TestCase):
    """Integration tests using the actual markdown files in the docs directory."""
    
    def setUp(self):
        self.parser = MarkdownParser()
        self.docs_dir = 'docs'
    
    def test_parse_nvidia_blackwell_specs(self):
        """Test parsing the NVIDIA Blackwell specifications file."""
        file_path = os.path.join(self.docs_dir, 'nvidia-blackwell-specs.md')
        
        if not os.path.exists(file_path):
            self.skipTest(f"File not found: {file_path}")
        
        result = self.parser.parse_file(file_path)
        
        self.assertIsNotNone(result)
        self.assertGreater(len(result['tables']), 0)
        
        # Check that we found the main specifications table
        table = result['tables'][0]
        df = table['dataframe']
        
        # Should have data for HGX B300 and HGX B200
        self.assertGreater(len(df), 0)
        
        # Check for expected specifications
        spec_metadata = table['specification_metadata']
        self.assertGreater(len(spec_metadata), 0)
    
    def test_parse_gpu_comparison_table(self):
        """Test parsing the GPU comparison table file."""
        file_path = os.path.join(self.docs_dir, 'nvidia-gpu-comparison-table.md')
        
        if not os.path.exists(file_path):
            self.skipTest(f"File not found: {file_path}")
        
        result = self.parser.parse_file(file_path)
        
        self.assertIsNotNone(result)
        self.assertGreater(len(result['tables']), 0)
        
        # Check that we found GPU comparison data
        table = result['tables'][0]
        df = table['dataframe']
        
        # Should have data for A100 and H100
        self.assertGreater(len(df), 0)
        
        # Check categories
        categories = table['categories']
        self.assertIn('hardware_specs', categories)
        self.assertIn('computing_capacity', categories)
        self.assertIn('memory_info', categories)
    
    def test_parse_nvl72_specs(self):
        """Test parsing the NVL72 specifications file."""
        file_path = os.path.join(self.docs_dir, 'nvl72.md')
        
        if not os.path.exists(file_path):
            self.skipTest(f"File not found: {file_path}")
        
        result = self.parser.parse_file(file_path)
        
        self.assertIsNotNone(result)
        self.assertGreater(len(result['tables']), 0)
        
        # Check that we found system-level specifications
        table = result['tables'][0]
        df = table['dataframe']
        
        # Should have data for GB300 NVL72 and GB200 NVL72
        self.assertGreater(len(df), 0)
    
    def test_parse_docs_directory(self):
        """Test parsing the entire docs directory."""
        if not os.path.exists(self.docs_dir):
            self.skipTest(f"Directory not found: {self.docs_dir}")
        
        result = self.parser.parse_directory(self.docs_dir)
        
        # Should find multiple files and tables
        self.assertGreater(result['metadata']['total_files'], 0)
        self.assertGreater(result['metadata']['total_tables'], 0)
        
        # Should be able to create consolidated DataFrame
        consolidated_df = self.parser.get_consolidated_dataframe(result)
        self.assertFalse(consolidated_df.empty)
        
        # Validate the results
        validation = self.parser.validate_parsed_data(result)
        self.assertTrue(validation['is_valid'])


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)