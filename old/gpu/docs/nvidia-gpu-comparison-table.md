# NVIDIA A100 vs H100 Data Center GPU Comparison

## Table 3. Comparison of NVIDIA A100 and H100 Data Center GPUs

| GPU Features | NVIDIA A100 | NVIDIA H100 SXM5 | NVIDIA H100 PCIe |
|--------------|-------------|-------------------|-------------------|
| GPU Architecture | NVIDIA Ampere | NVIDIA Hopper | NVIDIA Hopper |
| GPU Board Form Factor | SXM4 | SXM5 | PCIe Gen 5 |
| SMs | 108 | 132 | 114 |
| TPCs | 54 | 66 | 57 |
| FP32 Cores / SM | 64 | 128 | 128 |
| FP32 Cores / GPU | 6912 | 16896 | 14592 |
| FP64 Cores / SM (excl. Tensor) | 32 | 64 | 64 |
| FP64 Cores / GPU (excl. Tensor) | 3456 | 8448 | 7296 |
| INT32 Cores / SM | 64 | 64 | 64 |
| INT32 Cores / GPU | 6912 | 8448 | 7296 |
| Tensor Cores / SM | 4 | 4 | 4 |
| Tensor Cores / GPU | 432 | 528 | 456 |
| GPU Boost Clock² for FP8, FP16, BF16, TF32 Tensor Core Ops | 1410 MHz | 1830 MHz | 1620 MHz |
| GPU Boost Clock² for FP64 Tensor Core Ops, FP32 and FP64 non-Tensor Core Ops | 1410 MHz | 1980 MHz | 1755 MHz |
| Peak FP8 Tensor TFLOPS with FP16 Accumulate | NA | 1978.9/3957.8¹ | 1513/3026¹ |
| Peak FP8 Tensor TFLOPS with FP32 Accumulate | NA | 1978.9/3957.8¹ | 1513/3026¹ |
| Peak FP16 Tensor TFLOPS with FP16 Accumulate | 312/624¹ | 989.4/1978.9¹ | 756/1513¹ |
| Peak FP16 Tensor TFLOPS with FP32 Accumulate | 312/624¹ | 989.4/1978.9¹ | 756/1513¹ |
| Peak BF16 Tensor TFLOPS with FP32 Accumulate | 312/624¹ | 989.4/1978.9¹ | 756/1513¹ |
| Peak TF32 Tensor TFLOPS | 156/312¹ | 494.7/989.4¹ | 378/756¹ |
| Peak FP64 Tensor TFLOPS | 19.5 | 66.9 | 51.2 |
| Peak INT8 Tensor TOPS | 624/1248¹ | 1978.9/3957.8¹ | 1513/3026¹ |
| Peak FP16 TFLOPS (non-Tensor) | 78 | 133.8 | 102.4 |
| Peak BF16 TFLOPS (non-Tensor) | 39 | 133.8 | 102.4 |
| Peak FP32 TFLOPS (non-Tensor) | 19.5 | 66.9 | 51.2 |
| Peak FP64 TFLOPS (non-Tensor) | 9.7 | 33.5 | 25.6 |
| Peak INT32 TOPS | 19.5 | 33.5 | 25.6 |
| Texture Units | 432 | 528 | 456 |
| Memory Interface | 5120-bit HBM2 | 5120-bit HBM3 | 5120-bit HBM2e |
| Memory Size | 40 GB | 80 GB | 80 GB |
| Memory Data Rate¹ | 1215 MHz DDR | 2619 MHz DDR | 1593 MHz DDR |
| Memory Bandwidth (Not Finalized for H100)¹ | 1555 GB/sec | 3352 GB/sec | 2039 GB/sec |
| L2 Cache Size | 40 MB | 50 MB | 50 MB |
| Shared Memory Size / SM | Configurable up to 164 KB | Configurable up to 228 KB | Configurable up to 228 KB |
| Register File Size / SM | 256 KB | 256 KB | 256 KB |
| Register File Size / GPU | 27648 KB | 33792 KB | 29184 KB |
| TDP¹ | 400 Watts | 700 Watts | 350 Watts |
| Transistors | 54.2 billion | 80 billion | 80 billion |
| GPU Die Size | 826 mm² | 814 mm² | 814 mm² |
| TSMC Manufacturing Process | 7 nm N7 | 4N customized for NVIDIA | 4N customized for NVIDIA |

## Notes

1. Effective TOPs / TFLOPS using the Sparsity feature
2. GPU Peak Clock and GPU Boost Clock are synonymous for NVIDIA Data Center GPUs

**Note:** Because the H100 and A100 Tensor Core GPUs are designed to be installed in high-performance servers and data center racks to power AI and HPC compute workloads, they do not include display connectors, NVIDIA RT Cores for ray tracing acceleration, or an NVENC encoder.