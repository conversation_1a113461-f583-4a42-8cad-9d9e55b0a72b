<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang xml:lang>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>full</title>
  <style>
html {
color: #1a1a1a;
background-color: #fdfdfd;
}
body {
margin: 0 auto;
max-width: 36em;
padding-left: 50px;
padding-right: 50px;
padding-top: 50px;
padding-bottom: 50px;
hyphens: auto;
overflow-wrap: break-word;
text-rendering: optimizeLegibility;
font-kerning: normal;
}
@media (max-width: 600px) {
body {
font-size: 0.9em;
padding: 12px;
}
h1 {
font-size: 1.8em;
}
}
@media print {
html {
background-color: white;
}
body {
background-color: transparent;
color: black;
font-size: 12pt;
}
p, h2, h3 {
orphans: 3;
widows: 3;
}
h2, h3, h4 {
page-break-after: avoid;
}
}
p {
margin: 1em 0;
}
a {
color: #1a1a1a;
}
a:visited {
color: #1a1a1a;
}
img {
max-width: 100%;
}
svg {
height: auto;
max-width: 100%;
}
h1, h2, h3, h4, h5, h6 {
margin-top: 1.4em;
}
h5, h6 {
font-size: 1em;
font-style: italic;
}
h6 {
font-weight: normal;
}
ol, ul {
padding-left: 1.7em;
margin-top: 1em;
}
li > ol, li > ul {
margin-top: 0;
}
blockquote {
margin: 1em 0 1em 1.7em;
padding-left: 1em;
border-left: 2px solid #e6e6e6;
color: #606060;
}
code {
font-family: Menlo, Monaco, Consolas, 'Lucida Console', monospace;
font-size: 85%;
margin: 0;
hyphens: manual;
}
pre {
margin: 1em 0;
overflow: auto;
}
pre code {
padding: 0;
overflow: visible;
overflow-wrap: normal;
}
.sourceCode {
background-color: transparent;
overflow: visible;
}
hr {
border: none;
border-top: 1px solid #1a1a1a;
height: 1px;
margin: 1em 0;
}
table {
margin: 1em 0;
border-collapse: collapse;
width: 100%;
overflow-x: auto;
display: block;
font-variant-numeric: lining-nums tabular-nums;
}
table caption {
margin-bottom: 0.75em;
}
tbody {
margin-top: 0.5em;
border-top: 1px solid #1a1a1a;
border-bottom: 1px solid #1a1a1a;
}
th {
border-top: 1px solid #1a1a1a;
padding: 0.25em 0.5em 0.25em 0.5em;
}
td {
padding: 0.125em 0.5em 0.25em 0.5em;
}
header {
margin-bottom: 4em;
text-align: center;
}
#TOC li {
list-style: none;
}
#TOC ul {
padding-left: 1.3em;
}
#TOC > ul {
padding-left: 0;
}
#TOC a:not(:hover) {
text-decoration: none;
}
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}

ul.task-list[class]{list-style: none;}
ul.task-list li input[type="checkbox"] {
font-size: inherit;
width: 0.8em;
margin: 0 0.8em 0.2em -1.6em;
vertical-align: middle;
}
table {
display: table; overflow: auto;
max-width: 100%;
border-collapse: collapse;
page-break-inside: avoid;
}
table th {
text-align: center;
font-weight: bold;
}
table td,table th {
border: 1px solid #dfe2e5;
padding: 6px 13px;
}
table tr {
background-color: #fff;
border-top: 1px solid #c6cbd1;
}
table tr:nth-child(2n) {
background-color: #f6f8fa;
}
</style>
</head>
<body>
<p>Table 3. Comparison of NVIDIA A100 and H100’ Data Center GPUs</p>
<table>
<tr>
<td>
GPU Features
</td>
<td>
NVIDIA A100
</td>
<td>
NVIDIA H100 SXM5
</td>
<td>
NVIDIA H100 PCIe
</td>
</tr>
<tr>
<td>
GPU Architecture
</td>
<td>
NVIDIA Ampere
</td>
<td>
NVIDIA Hopper
</td>
<td>
NVIDIA Hopper
</td>
</tr>
<tr>
<td>
GPU Board Form Factor
</td>
<td>
SXM4
</td>
<td>
SXM5
</td>
<td>
PCIe Gen 5
</td>
</tr>
<tr>
<td>
SMs
</td>
<td>
108
</td>
<td>
132
</td>
<td>
114
</td>
</tr>
<tr>
<td>
TPCs
</td>
<td>
54
</td>
<td>
66
</td>
<td>
57
</td>
</tr>
<tr>
<td>
FP32 Cores / SM
</td>
<td>
64
</td>
<td>
128
</td>
<td>
128
</td>
</tr>
<tr>
<td>
FP32 Cores / GPU
</td>
<td>
6912
</td>
<td>
16896
</td>
<td>
14592
</td>
</tr>
<tr>
<td>
FP64 Cores / SM (excl. Tensor)
</td>
<td>
32
</td>
<td>
64
</td>
<td>
64
</td>
</tr>
<tr>
<td>
FP64 Cores / GPU (excl. Tensor)
</td>
<td>
3456
</td>
<td>
8448
</td>
<td>
7296
</td>
</tr>
<tr>
<td>
INT32 Cores / SM
</td>
<td>
64
</td>
<td>
64
</td>
<td>
64
</td>
</tr>
<tr>
<td>
INT32 Cores / GPU
</td>
<td>
6912
</td>
<td>
8448
</td>
<td>
7296
</td>
</tr>
<tr>
<td>
Tensor Cores / SM
</td>
<td>
4
</td>
<td>
4
</td>
<td>
4
</td>
</tr>
<tr>
<td>
Tensor Cores / GPU
</td>
<td>
432
</td>
<td>
528
</td>
<td>
456
</td>
</tr>
<tr>
<td>
GPU Boost Clock² for FP8, FP16, BF16, TF32 Tensor Core Ops
</td>
<td>
1410 MHz
</td>
<td>
1830 MHz
</td>
<td>
1620 MHz
</td>
</tr>
<tr>
<td>
GPU Boost Clock² for FP64 Tensor Core Ops, FP32 and FP64 non-Tensor Core
Ops
</td>
<td>
1410 MHz
</td>
<td>
1980 MHz
</td>
<td>
1755 MHz
</td>
</tr>
<tr>
<td>
Peak FP8 Tensor TFLOPS with FP16 Accumulate
</td>
<td>
NA
</td>
<td>
1978.9/3957.8¹
</td>
<td>
1513/3026¹
</td>
</tr>
<tr>
<td>
Peak FP8 Tensor TFLOPS with FP32 Accumulate
</td>
<td>
NA
</td>
<td>
1978.9/3957.8¹
</td>
<td>
1513/3026¹
</td>
</tr>
<tr>
<td>
Peak FP16 Tensor TFLOPS with FP16 Accumulate
</td>
<td>
312/624¹
</td>
<td>
989.4/1978.9¹
</td>
<td>
756/1513¹
</td>
</tr>
<tr>
<td>
Peak FP16 Tensor TFLOPS with FP32 Accumulate
</td>
<td>
312/624¹
</td>
<td>
989.4/1978.9¹
</td>
<td>
756/1513¹
</td>
</tr>
<tr>
<td>
Peak BF16 Tensor TFLOPS with FP32 Accumulate
</td>
<td>
312/624¹
</td>
<td>
989.4/1978.9¹
</td>
<td>
756/1513¹
</td>
</tr>
<tr>
<td>
Peak TF32 Tensor TFLOPS
</td>
<td>
156/312¹
</td>
<td>
494.7/989.4¹
</td>
<td>
378/756¹
</td>
</tr>
<tr>
<td>
Peak FP64 Tensor TFLOPS
</td>
<td>
19.5
</td>
<td>
66.9
</td>
<td>
51.2
</td>
</tr>
<tr>
<td>
Peak INT8 Tensor TOPS
</td>
<td>
624/1248¹
</td>
<td>
1978.9/3957.8¹
</td>
<td>
1513/3026¹
</td>
</tr>
<tr>
<td>
Peak FP16 TFLOPS (non-Tensor)
</td>
<td>
78
</td>
<td>
133.8
</td>
<td>
102.4
</td>
</tr>
<tr>
<td>
Peak BF16 TFLOPS (non-Tensor)
</td>
<td>
39
</td>
<td>
133.8
</td>
<td>
102.4
</td>
</tr>
<tr>
<td>
Peak FP32 TFLOPS (non-Tensor)
</td>
<td>
19.5
</td>
<td>
66.9
</td>
<td>
51.2
</td>
</tr>
<tr>
<td>
Peak FP64 TFLOPS (non-Tensor)
</td>
<td>
9.7
</td>
<td>
33.5
</td>
<td>
25.6
</td>
</tr>
</table>
<table>
<tr>
<td>
Peak INT32 TOPS
</td>
<td>
19.5
</td>
<td>
33.5
</td>
<td>
25.6
</td>
</tr>
<tr>
<td>
Texture Units
</td>
<td>
432
</td>
<td>
528
</td>
<td>
456
</td>
</tr>
<tr>
<td>
Memory Interface
</td>
<td>
5120-bit HBM2
</td>
<td>
5120-bit HBM3
</td>
<td>
5120-bit HBM2e
</td>
</tr>
<tr>
<td>
Memory Size
</td>
<td>
40 GB
</td>
<td>
80 GB
</td>
<td>
80 GB
</td>
</tr>
<tr>
<td>
Memory Data Rate¹
</td>
<td>
1215 MHz DDR
</td>
<td>
2619 MHz DDR
</td>
<td>
1593 MHz DDR
</td>
</tr>
<tr>
<td>
Memory Bandwidth (Not Finalized for H100)¹
</td>
<td>
1555 GB/sec
</td>
<td>
3352 GB/sec
</td>
<td>
2039 GB/sec
</td>
</tr>
<tr>
<td>
L2 Cache Size
</td>
<td>
40 MB
</td>
<td>
50 MB
</td>
<td>
50 MB
</td>
</tr>
<tr>
<td>
Shared Memory Size / SM
</td>
<td>
Configurable up to 164 KB
</td>
<td>
Configurable up to 228 KB
</td>
<td>
Configurable up to 228 KB
</td>
</tr>
<tr>
<td>
Register File Size / SM
</td>
<td>
256 KB
</td>
<td>
256 KB
</td>
<td>
256 KB
</td>
</tr>
<tr>
<td>
Register File Size / GPU
</td>
<td>
27648 KB
</td>
<td>
33792 KB
</td>
<td>
29184 KB
</td>
</tr>
<tr>
<td>
TDP¹
</td>
<td>
400 Watts
</td>
<td>
700 Watts
</td>
<td>
350 Watts
</td>
</tr>
<tr>
<td>
Transistors
</td>
<td>
54.2 billion
</td>
<td>
80 billion
</td>
<td>
80 billion
</td>
</tr>
<tr>
<td>
GPU Die Size
</td>
<td>
826 mm2
</td>
<td>
814 mm2
</td>
<td>
814 mm2
</td>
</tr>
<tr>
<td>
TSMC Manufacturing Process
</td>
<td>
7 nm N7
</td>
<td>
4N customized for NVIDIA
</td>
<td>
4N customized for NVIDIA
</td>
</tr>
</table>
<p>1.Effective TOPs / TFLOPS using the Sparsity feature 2. GPU Peak
Clock and GPU Boost Clock are synonymous for NVIDIA Data Center GPUs</p>
<p>Note: Because the H100 and A100 Tensor Core GPUs are designed to be
installed in high- performance servers and data center racks to power AI
and HPC compute workloads, they do not include display connectors,
NVIDIA RT Cores for ray tracing acceleration, or an NVENC encoder.</p>
</body>
</html>
