<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang xml:lang>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>full</title>
  <style>
html {
color: #1a1a1a;
background-color: #fdfdfd;
}
body {
margin: 0 auto;
max-width: 36em;
padding-left: 50px;
padding-right: 50px;
padding-top: 50px;
padding-bottom: 50px;
hyphens: auto;
overflow-wrap: break-word;
text-rendering: optimizeLegibility;
font-kerning: normal;
}
@media (max-width: 600px) {
body {
font-size: 0.9em;
padding: 12px;
}
h1 {
font-size: 1.8em;
}
}
@media print {
html {
background-color: white;
}
body {
background-color: transparent;
color: black;
font-size: 12pt;
}
p, h2, h3 {
orphans: 3;
widows: 3;
}
h2, h3, h4 {
page-break-after: avoid;
}
}
p {
margin: 1em 0;
}
a {
color: #1a1a1a;
}
a:visited {
color: #1a1a1a;
}
img {
max-width: 100%;
}
svg {
height: auto;
max-width: 100%;
}
h1, h2, h3, h4, h5, h6 {
margin-top: 1.4em;
}
h5, h6 {
font-size: 1em;
font-style: italic;
}
h6 {
font-weight: normal;
}
ol, ul {
padding-left: 1.7em;
margin-top: 1em;
}
li > ol, li > ul {
margin-top: 0;
}
blockquote {
margin: 1em 0 1em 1.7em;
padding-left: 1em;
border-left: 2px solid #e6e6e6;
color: #606060;
}
code {
font-family: Menlo, Monaco, Consolas, 'Lucida Console', monospace;
font-size: 85%;
margin: 0;
hyphens: manual;
}
pre {
margin: 1em 0;
overflow: auto;
}
pre code {
padding: 0;
overflow: visible;
overflow-wrap: normal;
}
.sourceCode {
background-color: transparent;
overflow: visible;
}
hr {
border: none;
border-top: 1px solid #1a1a1a;
height: 1px;
margin: 1em 0;
}
table {
margin: 1em 0;
border-collapse: collapse;
width: 100%;
overflow-x: auto;
display: block;
font-variant-numeric: lining-nums tabular-nums;
}
table caption {
margin-bottom: 0.75em;
}
tbody {
margin-top: 0.5em;
border-top: 1px solid #1a1a1a;
border-bottom: 1px solid #1a1a1a;
}
th {
border-top: 1px solid #1a1a1a;
padding: 0.25em 0.5em 0.25em 0.5em;
}
td {
padding: 0.125em 0.5em 0.25em 0.5em;
}
header {
margin-bottom: 4em;
text-align: center;
}
#TOC li {
list-style: none;
}
#TOC ul {
padding-left: 1.3em;
}
#TOC > ul {
padding-left: 0;
}
#TOC a:not(:hover) {
text-decoration: none;
}
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}

ul.task-list[class]{list-style: none;}
ul.task-list li input[type="checkbox"] {
font-size: inherit;
width: 0.8em;
margin: 0 0.8em 0.2em -1.6em;
vertical-align: middle;
}
table {
display: table; overflow: auto;
max-width: 100%;
border-collapse: collapse;
page-break-inside: avoid;
}
table th {
text-align: center;
font-weight: bold;
}
table td,table th {
border: 1px solid #dfe2e5;
padding: 6px 13px;
}
table tr {
background-color: #fff;
border-top: 1px solid #c6cbd1;
}
table tr:nth-child(2n) {
background-color: #f6f8fa;
}
</style>
</head>
<body>
<h1 id="nvidia-blackwell-hgx">NVIDIA Blackwell HGX</h1>
<p>The NVIDIA Blackwell HGX B300 and HGX B200 systems include
groundbreaking advancements for generative AI, data analytics, and high-
performance computing,</p>
<p>NVIDIA HGX™ B300: Built for the age of AI reasoning with enhanced
compute and increased memory. Featuring 7X more AI compute than Hopper
platforms, 2.3TB of HBM3e memory, and high- performance networking
integration with NVIDIA ConnectX- 8 SuperNics, HGX B300 delivers
breakthrough performance on the most complex workloads from agentic
systems and reasoning, to real time video generation for every data
center.</p>
<p>HGX B200: A Blackwell x86 platform based on an eight- Blackwell GPU
baseboard, delivering 144 petaFLOPS of AI performance. HGX B200 delivers
the best performance (15X more than HGX H100) and TCO (12X more than HGX
H100) for x86 scale- up platforms and infrastructure. Each GPU is
configurable up to 1000 Watts per GPU.</p>
<p>Table 4. System Specifications for HGX B200</p>
<table>
<tr>
<td>
</td>
<td>
HGX B300
</td>
<td>
HGX B200
</td>
</tr>
<tr>
<td>
</td>
<td colspan="2">
Per Server Specs Below
</td>
</tr>
<tr>
<td>
Blackwell GPUs
</td>
<td>
16 Blackwell Ultra Die for 8 GPUs
</td>
<td>
16 Blackwell Die for 8 GPUs
</td>
</tr>
<tr>
<td>
FP4 Tensor Core Dense/Sparse
</td>
<td>
105/144 petaFLOPS
</td>
<td>
72/144 petaFLOPS
</td>
</tr>
<tr>
<td>
FP8/FP6 Tensor Core Dense/Sparse
</td>
<td>
36/72 petaFLOPS
</td>
<td>
36/72 petaFLOPS
</td>
</tr>
<tr>
<td>
Fast Memory
</td>
<td>
Up to 2.3 TB
</td>
<td>
Up to 1.4 TB
</td>
</tr>
<tr>
<td>
Aggregate Memory Bandwidth
</td>
<td>
Up to 64 TB/s
</td>
<td>
Up to 62 TB/s
</td>
</tr>
<tr>
<td>
Aggregate NVLink Bandwidth
</td>
<td>
14.4 TB/s
</td>
<td>
14.4 TB/s
</td>
</tr>
<tr>
<td>
</td>
<td colspan="2">
Per GPU Specs Below
</td>
</tr>
<tr>
<td>
FP4 Tensor Core Dense/Sparse
</td>
<td>
13/18 petaFLOPS
</td>
<td>
9/18 petaFLOPS
</td>
</tr>
<tr>
<td>
FP8/FP6 Tensor Core Dense/Sparse
</td>
<td>
4.5/9 petaFLOPS
</td>
<td>
4.5/9 petaFLOPS
</td>
</tr>
<tr>
<td>
INT8 Tensor Core Dense/Sparse
</td>
<td>
0.14/0.28 petaOPS
</td>
<td>
4.5/9 petaOPS
</td>
</tr>
</table>
<table>
<tr>
<td>
FP16/BF16 Tensor Core Dense/Sparse
</td>
<td>
2.2/4.5 petaFLOPS
</td>
<td>
2.2/4.5 petaFLOPS
</td>
</tr>
<tr>
<td>
TF32 Tensor Core Dense/Sparse
</td>
<td>
1.1/2.2 petaFLOPS
</td>
<td>
1.1/2.2 petaFLOPS
</td>
</tr>
<tr>
<td>
FP32 Dense/Sparse
</td>
<td>
72 teraFLOPS
</td>
<td>
75 teraFLOPS
</td>
</tr>
<tr>
<td>
FP64 Tensor Core | FP64
</td>
<td>
1.2 teraFLOPS
</td>
<td>
37 teraFLOPS
</td>
</tr>
<tr>
<td>
GPU memory | Bandwidth
</td>
<td>
Up to 288 GB HBM3e | Up to 8 TB/s
</td>
<td>
Up to 192 GB HBM3e | Up to 8 TB/s
</td>
</tr>
<tr>
<td>
Max thermal design power (TDP)
</td>
<td>
1200 W
</td>
<td>
1000W
</td>
</tr>
<tr>
<td>
Interconnect
</td>
<td>
NVLink 5 PCIe Gen6
</td>
<td>
NVLink 5 PCIe Gen5
</td>
</tr>
<tr>
<td>
Server options
</td>
<td>
NVIDIA HGX B300 partner and NVIDIA-Certified Systems with 16 die for 8
GPUs
</td>
<td>
NVIDIA HGX B200 partner and NVIDIA-Certified Systems with 16 die for 8
GPUs
</td>
</tr>
</table>
<p>Preliminary specifications subject to change for HGX B300.</p>
</body>
</html>
