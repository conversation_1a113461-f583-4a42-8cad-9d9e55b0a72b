"""
API routes for the GPU Visualization Tool.
Provides RESTful endpoints for data access and manipulation.
"""
from fastapi import API<PERSON>out<PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
import logging
import pandas as pd
from typing import Dict, Any, Optional, List
import os
from pathlib import Path
from pydantic import BaseModel

from data_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor
from data_processor.system_data_processor import SystemDataProcessor
from config import Config

# Create router for API routes
router = APIRouter()

logger = logging.getLogger(__name__)

# Global data cache
_data_cache = {
    'gpu_data': None,
    'system_data': None,
    'last_refresh': None,
    'parsing_summary': None
}

# Pydantic models for request bodies
class FilterCriteria(BaseModel):
    model_name: Optional[str] = None
    architecture: Optional[str] = None
    min_memory: Optional[float] = None
    max_memory: Optional[float] = None
    min_fp32_tflops: Optional[float] = None
    max_fp32_tflops: Optional[float] = None
    min_fp16_tflops: Optional[float] = None
    max_fp16_tflops: Optional[float] = None
    min_tdp: Optional[float] = None
    max_tdp: Optional[float] = None
    category: Optional[str] = None
    source_file: Optional[str] = None

class GPUUpdate(BaseModel):
    fp4_tensor_tflops: Optional[float] = None
    fp8_tensor_tflops: Optional[float] = None
    fp16_tensor_tflops: Optional[float] = None
    fp32_tflops: Optional[float] = None
    fp64_tflops: Optional[float] = None
    memory_size_gb: Optional[float] = None
    memory_bandwidth_gbps: Optional[float] = None
    l2_cache_mb: Optional[float] = None
    tdp_watts: Optional[float] = None
    sms: Optional[int] = None
    fp32_cores_per_sm: Optional[int] = None
    fp64_cores_per_sm: Optional[int] = None
    tensor_cores_per_sm: Optional[int] = None
    tensor_cores_total: Optional[int] = None


@router.get('/gpus')
async def get_gpus():
    """Get all individual GPU data."""
    try:
        # Ensure data is loaded
        logger.debug("Ensuring data is loaded")
        _ensure_data_loaded()
        
        gpu_data = _data_cache['gpu_data']
        logger.debug(f"GPU data type: {type(gpu_data)}, empty: {gpu_data.empty if gpu_data is not None else 'None'}")
        
        if gpu_data is None or gpu_data.empty:
            return {
                'success': True,
                'data': [],
                'message': 'No GPU data available',
                'total_count': 0
            }
        
        # Convert DataFrame to list of dictionaries
        logger.debug("Converting DataFrame to records")
        gpu_records = _dataframe_to_records(gpu_data)
        logger.debug(f"Converted {len(gpu_records)} records")
        
        # Get categorized specifications in the format expected by frontend
        try:
            logger.debug("Categorizing specifications")
            processor = GPUDataProcessor()
            categories = _get_frontend_categories(processor, gpu_data)
            logger.debug("Categorization completed")
        except Exception as cat_error:
            logger.warning(f"Error categorizing specifications: {cat_error}", exc_info=True)
            categories = {}
        
        # Serialize the timestamp
        last_refresh = _data_cache['last_refresh']
        if last_refresh:
            last_refresh = last_refresh.isoformat()
        
        return {
            'success': True,
            'data': gpu_records,
            'total_count': len(gpu_records),
            'categories': categories,
            'last_refresh': last_refresh,
            'message': f'Retrieved {len(gpu_records)} GPU specifications'
        }
        
    except Exception as e:
        logger.error(f"Error retrieving GPU data: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': 'Failed to retrieve GPU data',
            'message': str(e)
        })


@router.get('/systems')
async def get_systems():
    """Get all system configuration data."""
    try:
        # Ensure data is loaded
        logger.debug("Ensuring data is loaded for systems")
        _ensure_data_loaded()
        
        system_data = _data_cache['system_data']
        logger.debug(f"System data type: {type(system_data)}, empty: {system_data.empty if system_data is not None else 'None'}")
        
        if system_data is None or system_data.empty:
            return {
                'success': True,
                'data': [],
                'message': 'No system configuration data available',
                'total_count': 0
            }
        
        # Convert DataFrame to list of dictionaries
        logger.debug("Converting system DataFrame to records")
        try:
            system_records = _dataframe_to_records(system_data)
            logger.debug(f"Converted {len(system_records)} system records")
        except Exception as convert_error:
            logger.error(f"Error converting system data to records: {convert_error}", exc_info=True)
            raise HTTPException(status_code=500, detail={
                'success': False,
                'error': 'Failed to convert system data',
                'message': str(convert_error)
            })
        
        # Get processing summary
        try:
            logger.debug("Generating system processing summary")
            processor = SystemDataProcessor()
            summary = processor.get_system_processing_summary(system_data)
            # Serialize the summary
            summary = _serialize_for_json(summary)
            logger.debug("System processing summary generated")
        except Exception as summary_error:
            logger.warning(f"Error generating system processing summary: {summary_error}", exc_info=True)
            summary = {}
        
        # Serialize the timestamp
        last_refresh = _data_cache['last_refresh']
        if last_refresh:
            last_refresh = last_refresh.isoformat()
        
        return {
            'success': True,
            'data': system_records,
            'total_count': len(system_records),
            'summary': summary,
            'last_refresh': last_refresh,
            'message': f'Retrieved {len(system_records)} system configurations'
        }
        
    except Exception as e:
        logger.error(f"Error retrieving system data: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': 'Failed to retrieve system data',
            'message': str(e)
        })


@router.post('/gpus/filter')
async def filter_gpus(filter_criteria: FilterCriteria):
    """Filter GPU data based on criteria."""
    try:
        # Ensure data is loaded
        _ensure_data_loaded()
        
        gpu_data = _data_cache['gpu_data']
        if gpu_data is None or gpu_data.empty:
            return {
                'success': True,
                'data': [],
                'message': 'No GPU data available to filter',
                'total_count': 0
            }
        
        # Convert Pydantic model to dict for filtering
        filter_dict = filter_criteria.dict(exclude_unset=True)
        
        # Apply filters
        filtered_data = _apply_gpu_filters(gpu_data, filter_dict)
        
        # Convert to list of dictionaries
        filtered_records = _dataframe_to_records(filtered_data)
        
        return {
            'success': True,
            'data': filtered_records,
            'total_count': len(filtered_records),
            'original_count': len(gpu_data),
            'filters_applied': filter_dict,
            'message': f'Filtered to {len(filtered_records)} GPU specifications'
        }
        
    except Exception as e:
        logger.error(f"Error filtering GPU data: {str(e)}")
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': 'Failed to filter GPU data',
            'message': str(e)
        })


@router.post('/refresh')
async def refresh_data():
    """Refresh data from markdown files."""
    try:
        logger.info("Starting data refresh from markdown files")
        
        # Force reload of data
        _load_data(force_refresh=True)
        
        gpu_count = len(_data_cache['gpu_data']) if _data_cache['gpu_data'] is not None else 0
        system_count = len(_data_cache['system_data']) if _data_cache['system_data'] is not None else 0
        
        # Serialize the timestamp and summary
        last_refresh = _data_cache['last_refresh']
        if last_refresh:
            last_refresh = last_refresh.isoformat()
        
        parsing_summary = _serialize_for_json(_data_cache['parsing_summary'])
        
        return {
            'success': True,
            'message': 'Data refreshed successfully',
            'gpu_count': gpu_count,
            'system_count': system_count,
            'last_refresh': last_refresh,
            'parsing_summary': parsing_summary
        }
        
    except Exception as e:
        logger.error(f"Error refreshing data: {str(e)}")
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': 'Failed to refresh data',
            'message': str(e)
        })


@router.put('/gpus/{gpu_id}')
async def update_gpu(gpu_id: str, update_data: GPUUpdate):
    """Update GPU specification data."""
    try:
        # Ensure data is loaded
        _ensure_data_loaded()
        
        gpu_data = _data_cache['gpu_data']
        if gpu_data is None or gpu_data.empty:
            raise HTTPException(status_code=404, detail={
                'success': False,
                'error': 'No GPU data available',
                'message': 'GPU data not loaded'
            })
        
        # Convert Pydantic model to dict, excluding unset values
        update_dict = update_data.dict(exclude_unset=True)
        
        # Find GPU to update
        gpu_mask = gpu_data['model_name'] == gpu_id
        if not gpu_mask.any():
            raise HTTPException(status_code=404, detail={
                'success': False,
                'error': 'GPU not found',
                'message': f'GPU with ID {gpu_id} not found'
            })
        
        # Update GPU data
        for column, value in update_dict.items():
            if column in gpu_data.columns:
                # Validate and process the value
                processed_value = _process_update_value(value, column)
                gpu_data.loc[gpu_mask, column] = processed_value
        
        # Update cache timestamp
        import datetime
        _data_cache['last_refresh'] = datetime.datetime.now()
        
        return {
            'success': True,
            'message': f'GPU {gpu_id} updated successfully',
            'updated_fields': list(update_dict.keys())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating GPU data: {str(e)}")
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': 'Failed to update GPU data',
            'message': str(e)
        })


@router.get('/export/{format}')
async def export_data(format: str):
    """Export data in specified format."""
    try:
        if format not in ['csv', 'json', 'markdown']:
            raise HTTPException(status_code=400, detail={
                'success': False,
                'error': 'Invalid export format',
                'message': f'Format {format} not supported'
            })
        
        # TODO: Implement actual export functionality in later tasks
        return {
            'success': True,
            'format': format,
            'message': f'Export endpoint ready for {format} format'
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting data: {str(e)}")
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': 'Failed to export data',
            'message': str(e)
        })


# Helper functions for data management

def _serialize_for_json(obj):
    """Convert pandas/numpy objects to JSON-serializable types."""
    import numpy as np
    import math
    
    # Handle None first
    if obj is None:
        return None
    
    # Check for NaN and infinity values first (before type checking)
    try:
        if pd.isna(obj) or (isinstance(obj, (int, float)) and (math.isnan(obj) or math.isinf(obj))):
            return None
    except (ValueError, TypeError, OverflowError):
        # pd.isna or math functions failed, continue with other checks
        pass
    
    # Handle numpy scalar types
    if hasattr(obj, 'item'):  # numpy types
        try:
            item_val = obj.item()
            # Check if the item value is NaN or infinity
            if isinstance(item_val, (int, float)) and (math.isnan(item_val) or math.isinf(item_val)):
                return None
            return item_val
        except (ValueError, AttributeError, OverflowError):
            return None
    
    # Handle basic JSON-serializable types (after NaN check)
    if isinstance(obj, (str, int, bool)):
        return obj
    elif isinstance(obj, float):
        # Double-check float values for NaN/infinity
        if math.isnan(obj) or math.isinf(obj):
            return None
        return obj
    
    # Handle arrays/lists
    if isinstance(obj, (list, tuple)):
        return [_serialize_for_json(v) for v in obj]
    elif isinstance(obj, dict):
        # Ensure dictionary keys are strings and values are serializable
        serialized_dict = {}
        for k, v in obj.items():
            # Convert key to string if it's not already
            key_str = str(k) if not isinstance(k, str) else k
            serialized_dict[key_str] = _serialize_for_json(v)
        return serialized_dict
    elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes)):
        # Handle numpy arrays and other iterables
        try:
            return [_serialize_for_json(v) for v in obj]
        except (TypeError, ValueError):
            # If iteration fails, convert to string
            return str(obj)
    
    # Handle pandas/numpy specific types
    if isinstance(obj, (pd.Timestamp, pd.Timedelta)):
        return str(obj)
    
    # Fallback: convert to string
    try:
        return str(obj)
    except Exception:
        return None


def _dataframe_to_records(df: pd.DataFrame) -> list:
    """Convert DataFrame to list of JSON-serializable records."""
    # First, replace any NaN/infinity values with None in the DataFrame
    df_clean = df.copy()
    
    # Replace NaN and infinity values with None
    df_clean = df_clean.replace([float('inf'), float('-inf')], None)
    df_clean = df_clean.where(pd.notna(df_clean), None)
    
    # Convert to records using pandas built-in method, then serialize
    records = df_clean.to_dict('records')
    
    # Apply additional serialization to handle any remaining pandas/numpy types
    serialized_records = []
    for record in records:
        serialized_record = {}
        for key, value in record.items():
            serialized_record[key] = _serialize_for_json(value)
        serialized_records.append(serialized_record)
    
    return serialized_records


def _ensure_data_loaded():
    """Ensure data is loaded in cache."""
    if (_data_cache['gpu_data'] is None or 
        _data_cache['system_data'] is None or 
        _data_cache['last_refresh'] is None):
        _load_data()


def _load_data(force_refresh: bool = False):
    """Load GPU and system data from markdown files."""
    import datetime
    
    # Check if we need to refresh based on cache timeout
    if not force_refresh and _data_cache['last_refresh'] is not None:
        cache_age = datetime.datetime.now() - _data_cache['last_refresh']
        if cache_age.total_seconds() < Config.DATA_CACHE_TIMEOUT:
            logger.debug("Using cached data (within timeout)")
            return
    
    logger.info("Loading data from markdown files")
    
    try:
        # Initialize parsers and processors
        parser = MarkdownParser()
        gpu_processor = GPUDataProcessor()
        system_processor = SystemDataProcessor()
        
        # Parse markdown files
        docs_path = Config.DOCS_PATH
        if not docs_path.exists():
            logger.warning(f"Documentation directory not found: {docs_path}")
            _data_cache['gpu_data'] = pd.DataFrame()
            _data_cache['system_data'] = pd.DataFrame()
            _data_cache['parsing_summary'] = {'error': 'Documentation directory not found'}
            _data_cache['last_refresh'] = datetime.datetime.now()
            return
        
        logger.debug(f"Parsing directory: {docs_path}")
        parsed_data = parser.parse_directory(str(docs_path))
        logger.debug(f"Parsed data keys: {parsed_data.keys()}")
        
        # Process GPU data
        logger.debug("Processing GPU data")
        gpu_data = gpu_processor.consolidate_gpu_data(parsed_data)
        logger.debug(f"GPU data shape: {gpu_data.shape if not gpu_data.empty else 'empty'}")
        
        # Process system data
        logger.debug("Processing system data")
        try:
            system_data = system_processor.process_system_configs(parsed_data, gpu_data)
            logger.debug(f"System data shape: {system_data.shape if not system_data.empty else 'empty'}")
        except Exception as sys_error:
            logger.warning(f"Error processing system data: {sys_error}", exc_info=True)
            system_data = pd.DataFrame()  # Use empty DataFrame on error
        
        # Update cache
        _data_cache['gpu_data'] = gpu_data
        _data_cache['system_data'] = system_data
        _data_cache['last_refresh'] = datetime.datetime.now()
        
        # Generate processing summary
        try:
            gpu_summary = gpu_processor.get_processing_summary(parsed_data, gpu_data)
        except Exception as gpu_summary_error:
            logger.warning(f"Error generating GPU processing summary: {gpu_summary_error}")
            gpu_summary = {'error': str(gpu_summary_error)}
        
        try:
            system_summary = system_processor.get_system_processing_summary(system_data)
        except Exception as sys_summary_error:
            logger.warning(f"Error generating system processing summary: {sys_summary_error}")
            system_summary = {'error': str(sys_summary_error)}
        
        _data_cache['parsing_summary'] = {
            'gpu_processing': gpu_summary,
            'system_processing': system_summary,
            'files_processed': len(parsed_data.get('files', {})),
            'tables_processed': len(parsed_data.get('consolidated_tables', []))
        }
        
        logger.info(f"Data loaded successfully: {len(gpu_data)} GPUs, {len(system_data)} systems")
        
    except Exception as e:
        logger.error(f"Error loading data: {str(e)}", exc_info=True)
        # Initialize empty data on error
        _data_cache['gpu_data'] = pd.DataFrame()
        _data_cache['system_data'] = pd.DataFrame()
        _data_cache['parsing_summary'] = {'error': str(e)}
        _data_cache['last_refresh'] = datetime.datetime.now()
        # Don't re-raise the exception, just log it and continue with empty data


def _process_update_value(value, column):
    """Process and validate update values for GPU data."""
    if value is None or value == '':
        return None
    
    # Numeric columns
    numeric_columns = [
        'fp4_tensor_tflops', 'fp8_tensor_tflops', 'fp16_tensor_tflops', 
        'fp32_tflops', 'fp64_tflops', 'memory_size_gb', 'memory_bandwidth_gbps',
        'l2_cache_mb', 'tdp_watts', 'sms', 'fp32_cores_per_sm', 'fp64_cores_per_sm',
        'tensor_cores_per_sm', 'tensor_cores_total'
    ]
    
    if column in numeric_columns:
        try:
            num_value = float(value)
            if num_value < 0:
                raise ValueError("Negative values not allowed")
            return num_value
        except (ValueError, TypeError):
            raise ValueError(f"Invalid numeric value for {column}: {value}")
    
    # String columns - just return as string
    return str(value)


def _get_frontend_categories(processor: GPUDataProcessor, gpu_data: pd.DataFrame) -> Dict[str, List[str]]:
    """Convert processor categories to frontend-expected format."""
    if gpu_data.empty:
        return {}
    
    # Get actual columns from the data
    actual_columns = set(gpu_data.columns.tolist())
    
    # Create frontend-compatible categories
    frontend_categories = {
        'Basic Info': ['model_name', 'source_file', 'table_context'],
        'Hardware Specs': [],
        'Computing Capacity': [],
        'Memory Info': [],
        'System Info': []
    }
    
    # Map processor categories to frontend categories
    category_mapping = {
        'hardware_specs': 'Hardware Specs',
        'computing_capacity': 'Computing Capacity', 
        'memory_info': 'Memory Info',
        'system_info': 'System Info'
    }
    
    # Add columns from processor categories that exist in the data
    for proc_category, columns in processor.category_groups.items():
        frontend_category = category_mapping.get(proc_category)
        if frontend_category:
            # Only include columns that actually exist in the data
            existing_columns = [col for col in columns if col in actual_columns]
            frontend_categories[frontend_category].extend(existing_columns)
    
    # Add any remaining columns to "Other" category
    categorized_columns = set()
    for columns in frontend_categories.values():
        categorized_columns.update(columns)
    
    uncategorized = actual_columns - categorized_columns
    if uncategorized:
        frontend_categories['Other'] = list(uncategorized)
    
    # Remove empty categories
    return {k: v for k, v in frontend_categories.items() if v}


def _apply_gpu_filters(gpu_data: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
    """Apply filtering criteria to GPU data."""
    if gpu_data.empty or not filters:
        return gpu_data
    
    filtered_data = gpu_data.copy()
    
    try:
        # Model name filter (partial match, case-insensitive)
        if 'model_name' in filters and filters['model_name']:
            model_filter = str(filters['model_name']).lower()
            filtered_data = filtered_data[
                filtered_data['model_name'].str.lower().str.contains(model_filter, na=False)
            ]
        
        # Architecture filter
        if 'architecture' in filters and filters['architecture']:
            arch_filter = str(filters['architecture']).lower()
            filtered_data = filtered_data[
                filtered_data['architecture'].str.lower().str.contains(arch_filter, na=False)
            ]
        
        # Numerical range filters
        numerical_filters = {
            'memory_size_gb': ('min_memory', 'max_memory'),
            'fp32_tflops': ('min_fp32_tflops', 'max_fp32_tflops'),
            'fp16_tensor_tflops': ('min_fp16_tflops', 'max_fp16_tflops'),
            'tdp_watts': ('min_tdp', 'max_tdp')
        }
        
        for column, (min_key, max_key) in numerical_filters.items():
            if column not in filtered_data.columns:
                continue
            
            # Apply minimum filter
            if min_key in filters and filters[min_key] is not None:
                min_val = float(filters[min_key])
                mask = (filtered_data[column].isna()) | (pd.to_numeric(filtered_data[column], errors='coerce') >= min_val)
                filtered_data = filtered_data[mask]
            
            # Apply maximum filter
            if max_key in filters and filters[max_key] is not None:
                max_val = float(filters[max_key])
                mask = (filtered_data[column].isna()) | (pd.to_numeric(filtered_data[column], errors='coerce') <= max_val)
                filtered_data = filtered_data[mask]
        
        # Category filter (hardware_specs, computing_capacity, memory_info, system_info)
        if 'category' in filters and filters['category']:
            category = filters['category']
            processor = GPUDataProcessor()
            category_specs = processor.category_groups.get(category, [])
            
            if category_specs:
                # Filter to only include rows that have data in this category
                category_columns = [col for col in category_specs if col in filtered_data.columns]
                if category_columns:
                    # Keep rows that have at least one non-null value in category columns
                    filtered_data = filtered_data[
                        filtered_data[category_columns].notna().any(axis=1)
                    ]
        
        # Source file filter
        if 'source_file' in filters and filters['source_file']:
            source_filter = str(filters['source_file']).lower()
            if 'source_file' in filtered_data.columns:
                filtered_data = filtered_data[
                    filtered_data['source_file'].str.lower().str.contains(source_filter, na=False)
                ]
        
        logger.info(f"Applied filters: {filters}, result: {len(filtered_data)} records")
        
    except Exception as e:
        logger.error(f"Error applying filters: {str(e)}")
        # Return original data if filtering fails
        return gpu_data
    
    return filtered_data