"""
Web interface routes for the GPU Visualization Tool.
Handles main dashboard and web page routing.
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import <PERSON><PERSON>2Templates
import logging

# Create router for web interface routes
router = APIRouter()

# Setup templates
templates = Jinja2Templates(directory="templates")

logger = logging.getLogger(__name__)


@router.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Main dashboard with tabbed interface."""
    try:
        return templates.TemplateResponse("index.html", {"request": request})
    except Exception as e:
        logger.error(f"Error rendering index page: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error": "Failed to load dashboard"
        }, status_code=500)


@router.get("/gpus", response_class=HTMLResponse)
async def gpu_view(request: Request):
    """Individual GPU specifications view."""
    try:
        return templates.TemplateResponse("gpu_table.html", {"request": request})
    except Exception as e:
        logger.error(f"Error rendering GPU view: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error": "Failed to load GPU specifications"
        }, status_code=500)


@router.get("/systems", response_class=HTMLResponse)
async def system_view(request: Request):
    """System configurations view."""
    try:
        return templates.TemplateResponse("system_table.html", {"request": request})
    except Exception as e:
        logger.error(f"Error rendering system view: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error": "Failed to load system configurations"
        }, status_code=500)