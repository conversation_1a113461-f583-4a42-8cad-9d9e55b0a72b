# Main FastAPI application entry point
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from config import Config
import logging
import os
from logging.handlers import <PERSON><PERSON>tingFileHandler
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    try:
        Config.validate_config()
        logging.info("Configuration validated successfully")
    except ValueError as e:
        logging.error(f"Configuration validation failed: {str(e)}")
        raise
    
    logging.info("GPU Visualization Tool startup")
    yield
    # Shutdown (if needed)
    logging.info("GPU Visualization Tool shutdown")

def create_app():
    app = FastAPI(
        title="GPU Visualization Tool",
        description="A tool for visualizing and comparing GPU specifications",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Configure logging
    configure_logging()
    
    # Mount static files (ensure directory exists)
    if not os.path.exists("static"):
        os.makedirs("static")
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # Setup templates with auto-reload for development
    templates = Jinja2Templates(directory="templates")
    if Config.DEBUG:
        templates.env.auto_reload = True
    
    # Include routers
    from web_interface.routes import router as web_router
    from web_interface.api import router as api_router
    
    app.include_router(web_router)
    app.include_router(api_router, prefix="/api")
    
    # Global exception handlers
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc: HTTPException):
        try:
            return templates.TemplateResponse("error.html", {
                "request": request, 
                "error": "Page not found"
            }, status_code=404)
        except Exception as e:
            logging.error(f"Error rendering error template: {e}")
            return HTMLResponse(
                content="<h1>404 - Page not found</h1>",
                status_code=404
            )
    
    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc: HTTPException):
        logging.error(f"Internal server error: {str(exc)}")
        try:
            return templates.TemplateResponse("error.html", {
                "request": request, 
                "error": "Internal server error"
            }, status_code=500)
        except Exception as e:
            logging.error(f"Error rendering error template: {e}")
            return HTMLResponse(
                content="<h1>500 - Internal server error</h1>",
                status_code=500
            )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logging.error(f"Unhandled exception: {str(exc)}")
        try:
            return templates.TemplateResponse("error.html", {
                "request": request, 
                "error": "An unexpected error occurred"
            }, status_code=500)
        except Exception as e:
            logging.error(f"Error rendering error template: {e}")
            return HTMLResponse(
                content="<h1>500 - An unexpected error occurred</h1>",
                status_code=500
            )
    
    return app

def configure_logging():
    """Configure application logging."""
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    # Set up file handler with rotation
    file_handler = RotatingFileHandler(
        'logs/gpu_visualization.log',
        maxBytes=10240000,  # 10MB
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    
    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG if Config.DEBUG else logging.INFO,
        handlers=[file_handler, logging.StreamHandler()]
    )

# Create the FastAPI app
app = create_app()

if __name__ == '__main__':
    import sys
    import uvicorn
    
    port = 9000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number, using default 9000")
    
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=port,
        reload=Config.DEBUG,
        log_level="debug" if Config.DEBUG else "info"
    )