"""
Configuration settings for the GPU Visualization Tool.
"""
import os
from pathlib import Path


class Config:
    """Base configuration class."""
    
    # Application settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    # Application settings
    DOCS_DIRECTORY = os.environ.get('DOCS_DIRECTORY') or 'docs'
    DATA_CACHE_TIMEOUT = int(os.environ.get('DATA_CACHE_TIMEOUT', '3600'))  # 1 hour
    
    # File paths
    BASE_DIR = Path(__file__).parent
    DOCS_PATH = BASE_DIR / DOCS_DIRECTORY
    
    # Data processing settings
    EXTRACT_DENSE_VALUES = True
    HANDLE_MISSING_VALUES = True
    NORMALIZE_UNITS = True
    
    # Web interface settings
    ITEMS_PER_PAGE = 50
    ENABLE_INLINE_EDITING = True
    ENABLE_EXPORT = True
    
    # Supported export formats
    EXPORT_FORMATS = ['csv', 'json', 'markdown']
    
    @classmethod
    def validate_config(cls):
        """Validate configuration settings."""
        if not cls.DOCS_PATH.exists():
            raise ValueError(f"Documentation directory not found: {cls.DOCS_PATH}")
        
        return True