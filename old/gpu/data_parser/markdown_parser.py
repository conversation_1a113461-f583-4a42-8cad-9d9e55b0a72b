"""
Main markdown parser for GPU specifications.
"""

import os
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd

from .table_extractor import TableExtractor
from .specification_normalizer import SpecificationNormalizer


class MarkdownParser:
    """Main parser class for extracting GPU specifications from markdown files."""
    
    def __init__(self):
        self.table_extractor = TableExtractor()
        self.spec_normalizer = SpecificationNormalizer()
        
        # Patterns for identifying GPU-related content
        self.gpu_indicators = [
            r'gpu', r'graphics', r'tensor', r'cuda', r'nvidia', r'amd', r'intel',
            r'flops', r'memory', r'bandwidth', r'cores', r'sm', r'tpc',
            r'h100', r'a100', r'v100', r'rtx', r'gtx', r'blackwell', r'hopper', r'ampere',
            r'nvl72', r'gb300', r'gb200', r'nvlink', r'hbm', r'superchip', r'grace'
        ]
        
        # Compile patterns for efficiency
        self.gpu_pattern = re.compile('|'.join(self.gpu_indicators), re.IGNORECASE)
    
    def parse_directory(self, directory_path: str) -> Dict[str, Any]:
        """
        Parse all markdown files in a directory.
        
        Args:
            directory_path: Path to directory containing markdown files
            
        Returns:
            Dictionary containing parsed data from all files
        """
        directory = Path(directory_path)
        if not directory.exists():
            raise FileNotFoundError(f"Directory not found: {directory_path}")
        
        parsed_data = {
            'files': {},
            'consolidated_tables': [],
            'metadata': {
                'total_files': 0,
                'files_with_tables': 0,
                'total_tables': 0,
                'parsing_errors': []
            }
        }
        
        # Find all markdown files
        markdown_files = list(directory.glob('**/*.md'))
        parsed_data['metadata']['total_files'] = len(markdown_files)
        
        for file_path in markdown_files:
            try:
                file_data = self.parse_file(str(file_path))
                if file_data:
                    parsed_data['files'][str(file_path)] = file_data
                    
                    # Add tables to consolidated list
                    if file_data.get('tables'):
                        parsed_data['consolidated_tables'].extend(file_data['tables'])
                        parsed_data['metadata']['files_with_tables'] += 1
                        parsed_data['metadata']['total_tables'] += len(file_data['tables'])
                        
            except Exception as e:
                error_info = {
                    'file': str(file_path),
                    'error': str(e),
                    'type': type(e).__name__
                }
                parsed_data['metadata']['parsing_errors'].append(error_info)
        
        return parsed_data
    
    def parse_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Parse a single markdown file.
        
        Args:
            file_path: Path to the markdown file
            
        Returns:
            Dictionary containing parsed data from the file
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
        
        # Check if content appears to be GPU-related
        if not self._is_gpu_related_content(content):
            return None
        
        # Extract tables
        tables = self.table_extractor.extract_tables_from_markdown(content)
        
        if not tables:
            return None
        
        # Process and normalize tables
        processed_tables = []
        for table_data in tables:
            processed_table = self._process_table(table_data, content, str(file_path))
            if processed_table:
                processed_tables.append(processed_table)
        
        return {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'content_length': len(content),
            'tables': processed_tables,
            'metadata': {
                'total_tables': len(processed_tables),
                'gpu_related': True
            }
        }
    
    def _is_gpu_related_content(self, content: str) -> bool:
        """Check if content appears to be GPU-related."""
        # Look for GPU-related keywords
        matches = self.gpu_pattern.findall(content.lower())
        return len(matches) >= 3  # Require at least 3 GPU-related terms
    
    def _process_table(self, table_data: Dict[str, Any], content: str, file_path: str) -> Optional[Dict[str, Any]]:
        """Process and normalize a single table."""
        try:
            # Convert to DataFrame
            df = self.table_extractor.convert_to_dataframe(table_data)
            if df.empty:
                return None
            
            # Check if table appears to contain GPU specifications
            if not self._is_gpu_specification_table(df):
                return None
            
            # Normalize the data
            normalized_df = self.spec_normalizer.normalize_dataframe(df)
            
            # Get specification metadata
            spec_metadata = self.spec_normalizer.get_specification_metadata(df)
            
            # Get table context
            context = self.table_extractor.get_table_context(content, table_data)
            
            # Analyze table structure to determine categories
            categories = self._analyze_table_categories(df, spec_metadata)
            
            return {
                'original_data': table_data,
                'dataframe': normalized_df,
                'specification_metadata': spec_metadata,
                'context': context,
                'source_file': file_path,
                'table_type': table_data.get('type', 'unknown'),
                'row_count': len(df),
                'column_count': len(df.columns),
                'categories': categories
            }
            
        except Exception as e:
            # Log error but don't fail completely
            print(f"Error processing table from {file_path}: {e}")
            return None
    
    def _is_gpu_specification_table(self, df: pd.DataFrame) -> bool:
        """Check if a DataFrame contains GPU specifications."""
        if df.empty or len(df.columns) < 2:
            return False
        
        # Check column headers for GPU-related terms
        headers_text = ' '.join(df.columns).lower()
        header_matches = self.gpu_pattern.findall(headers_text)
        
        # Check all cell content for GPU-related terms
        sample_text = ''
        for col in df.columns:
            sample_values = df[col].dropna().head(5).astype(str)
            sample_text += ' '.join(sample_values).lower() + ' '
        
        content_matches = self.gpu_pattern.findall(sample_text)
        
        # More lenient matching - require evidence in either headers or content
        return len(header_matches) >= 1 or len(content_matches) >= 3
    
    def _analyze_table_categories(self, df: pd.DataFrame, spec_metadata: Dict[str, Dict[str, Any]]) -> Dict[str, List[str]]:
        """Analyze table structure and categorize specifications."""
        categories = {}
        
        # If the first column contains specification names (common pattern),
        # categorize based on those names
        if not df.empty and len(df.columns) > 1:
            first_col = df.columns[0]
            spec_names = df[first_col].dropna().astype(str).tolist()
            
            # Check if first column contains specification names
            spec_like_count = 0
            for spec_name in spec_names[:10]:  # Check first 10 entries
                category = self.spec_normalizer.categorize_specification(spec_name)
                if category != 'other':
                    spec_like_count += 1
            
            # If more than half look like specifications, categorize by row content
            if spec_like_count > len(spec_names[:10]) * 0.3:
                for spec_name in spec_names:
                    category = self.spec_normalizer.categorize_specification(spec_name)
                    if category not in categories:
                        categories[category] = []
                    categories[category].append(spec_name)
                return categories
        
        # Fallback to column-based categorization
        for spec_name, metadata in spec_metadata.items():
            category = metadata.get('category', 'other')
            if category not in categories:
                categories[category] = []
            categories[category].append(spec_name)
        
        return categories
    
    def _get_table_categories(self, spec_metadata: Dict[str, Dict[str, Any]]) -> Dict[str, List[str]]:
        """Group specifications by category (legacy method)."""
        categories = {}
        
        for spec_name, metadata in spec_metadata.items():
            category = metadata.get('category', 'other')
            if category not in categories:
                categories[category] = []
            categories[category].append(spec_name)
        
        return categories
    
    def extract_dense_values(self, value: str) -> Optional[float]:
        """
        Extract dense values from sparse/dense format.
        
        Args:
            value: String that may contain dense/sparse values
            
        Returns:
            Dense value as float
        """
        return self.spec_normalizer.extract_dense_value(value)
    
    def categorize_specifications(self, specs: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Categorize specifications into hardware, computing, memory, and system groups.
        
        Args:
            specs: Dictionary of specifications
            
        Returns:
            Dictionary organized by categories
        """
        categorized = {
            'hardware_specs': {},
            'computing_capacity': {},
            'memory_info': {},
            'system_info': {},
            'other': {}
        }
        
        for spec_name, spec_value in specs.items():
            category = self.spec_normalizer.categorize_specification(spec_name)
            categorized[category][spec_name] = spec_value
        
        return categorized
    
    def get_consolidated_dataframe(self, parsed_data: Dict[str, Any]) -> pd.DataFrame:
        """
        Create a consolidated DataFrame from all parsed tables.
        
        Args:
            parsed_data: Output from parse_directory
            
        Returns:
            Consolidated DataFrame with all GPU specifications
        """
        all_dataframes = []
        
        for table in parsed_data.get('consolidated_tables', []):
            df = table.get('dataframe')
            if df is not None and not df.empty:
                # Add source information
                df = df.copy()
                df['source_file'] = table.get('source_file', 'unknown')
                all_dataframes.append(df)
        
        if not all_dataframes:
            return pd.DataFrame()
        
        # Concatenate all DataFrames
        try:
            consolidated_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
            
            # Remove duplicate rows based on the first column (usually GPU model names)
            if len(consolidated_df.columns) > 0:
                first_col = consolidated_df.columns[0]
                consolidated_df = consolidated_df.drop_duplicates(subset=[first_col], keep='first')
            
            return consolidated_df
            
        except Exception as e:
            print(f"Error consolidating DataFrames: {e}")
            return pd.DataFrame()
    
    def validate_parsed_data(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate parsed data and return validation results.
        
        Args:
            parsed_data: Output from parse_directory
            
        Returns:
            Validation results dictionary
        """
        validation_results = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'statistics': {
                'total_files_processed': len(parsed_data.get('files', {})),
                'total_tables_found': len(parsed_data.get('consolidated_tables', [])),
                'total_gpu_models': 0,
                'categories_found': set()
            }
        }
        
        # Check for parsing errors
        parsing_errors = parsed_data.get('metadata', {}).get('parsing_errors', [])
        if parsing_errors:
            validation_results['warnings'].extend([
                f"Parsing error in {error['file']}: {error['error']}" 
                for error in parsing_errors
            ])
        
        # Analyze consolidated data
        consolidated_df = self.get_consolidated_dataframe(parsed_data)
        if not consolidated_df.empty:
            validation_results['statistics']['total_gpu_models'] = len(consolidated_df)
            
            # Find categories
            for table in parsed_data.get('consolidated_tables', []):
                categories = table.get('categories', {})
                validation_results['statistics']['categories_found'].update(categories.keys())
        else:
            validation_results['warnings'].append("No valid GPU specification tables found")
        
        # Convert set to list for JSON serialization
        validation_results['statistics']['categories_found'] = list(
            validation_results['statistics']['categories_found']
        )
        
        return validation_results