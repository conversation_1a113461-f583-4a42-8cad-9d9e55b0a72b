"""
Table extraction utilities for parsing markdown tables.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd


class TableExtractor:
    """Extracts and parses table structures from markdown content."""
    
    def __init__(self):
        # Regex pattern for markdown tables
        self.markdown_table_pattern = re.compile(
            r'^\|.*\|$',  # Lines that start and end with |
            re.MULTILINE
        )
        
        # Pattern for table separator rows (e.g., |---|---|)
        self.separator_pattern = re.compile(r'^\|[\s\-\|:]+\|$')
        
        # Pattern for HTML tables in markdown
        self.html_table_pattern = re.compile(
            r'<table.*?</table>',
            re.DOTALL | re.IGNORECASE
        )
    
    def extract_tables_from_markdown(self, content: str) -> List[Dict[str, Any]]:
        """
        Extract all tables from markdown content.
        
        Args:
            content: Raw markdown content
            
        Returns:
            List of dictionaries containing table data and metadata
        """
        tables = []
        
        # Extract markdown tables
        markdown_tables = self._extract_markdown_tables(content)
        tables.extend(markdown_tables)
        
        # Extract HTML tables if any
        html_tables = self._extract_html_tables(content)
        tables.extend(html_tables)
        
        return tables
    
    def _extract_markdown_tables(self, content: str) -> List[Dict[str, Any]]:
        """Extract standard markdown tables."""
        tables = []
        lines = content.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Check if this line looks like a table row
            if self._is_table_row(line):
                table_data = self._parse_markdown_table_from_line(lines, i)
                if table_data:
                    tables.append(table_data)
                    # Skip past this table
                    i += len(table_data['raw_lines'])
                    continue
            i += 1
        
        return tables
    
    def _extract_html_tables(self, content: str) -> List[Dict[str, Any]]:
        """Extract HTML tables embedded in markdown."""
        tables = []
        html_matches = self.html_table_pattern.findall(content)
        
        for match in html_matches:
            table_data = self._parse_html_table(match)
            if table_data:
                tables.append(table_data)
        
        return tables
    
    def _is_table_row(self, line: str) -> bool:
        """Check if a line appears to be a table row."""
        if not line.strip():
            return False
        
        # Must start and end with |
        if not (line.strip().startswith('|') and line.strip().endswith('|')):
            return False
        
        # Must have at least 2 | characters (for at least 1 column)
        return line.count('|') >= 2
    
    def _parse_markdown_table_from_line(self, lines: List[str], start_idx: int) -> Optional[Dict[str, Any]]:
        """Parse a markdown table starting from a specific line."""
        table_lines = []
        current_idx = start_idx
        
        # Collect all consecutive table lines
        while current_idx < len(lines):
            line = lines[current_idx].strip()
            if self._is_table_row(line):
                table_lines.append(line)
                current_idx += 1
            else:
                break
        
        if len(table_lines) < 2:  # Need at least header and one data row
            return None
        
        # Parse the table structure
        headers = self._parse_table_row(table_lines[0])
        
        # Check if second row is a separator
        separator_idx = 1
        if separator_idx < len(table_lines) and self.separator_pattern.match(table_lines[separator_idx]):
            separator_idx = 2
        
        # Parse data rows
        data_rows = []
        for i in range(separator_idx, len(table_lines)):
            row_data = self._parse_table_row(table_lines[i])
            if row_data:
                data_rows.append(row_data)
        
        if not data_rows:
            return None
        
        return {
            'type': 'markdown',
            'headers': headers,
            'data': data_rows,
            'raw_lines': table_lines,
            'line_start': start_idx,
            'line_end': start_idx + len(table_lines) - 1
        }
    
    def _parse_table_row(self, line: str) -> List[str]:
        """Parse a single table row into columns."""
        # Remove leading/trailing |
        line = line.strip()
        if line.startswith('|'):
            line = line[1:]
        if line.endswith('|'):
            line = line[:-1]
        
        # Split by | and clean up
        columns = [col.strip() for col in line.split('|')]
        return columns
    
    def _parse_html_table(self, html_content: str) -> Optional[Dict[str, Any]]:
        """Parse HTML table content."""
        # This is a placeholder for HTML table parsing
        # For now, we'll focus on markdown tables since the sample files don't contain HTML tables
        return None
    
    def convert_to_dataframe(self, table_data: Dict[str, Any]) -> pd.DataFrame:
        """
        Convert extracted table data to a pandas DataFrame.
        
        Args:
            table_data: Table data dictionary from extract_tables_from_markdown
            
        Returns:
            pandas DataFrame with the table data
        """
        if not table_data or not table_data.get('data'):
            return pd.DataFrame()
        
        headers = table_data['headers']
        data_rows = table_data['data']
        
        # Ensure all rows have the same number of columns as headers
        normalized_rows = []
        for row in data_rows:
            # Pad or truncate row to match header length
            if len(row) < len(headers):
                row.extend([''] * (len(headers) - len(row)))
            elif len(row) > len(headers):
                row = row[:len(headers)]
            normalized_rows.append(row)
        
        # Create DataFrame
        df = pd.DataFrame(normalized_rows, columns=headers)
        
        # Clean up column names (remove extra whitespace, special characters)
        df.columns = [self._clean_column_name(col) for col in df.columns]
        
        return df
    
    def _clean_column_name(self, column_name: str) -> str:
        """Clean up column names for better processing."""
        # Remove extra whitespace
        cleaned = column_name.strip()
        
        # Replace problematic characters
        cleaned = re.sub(r'[^\w\s\-\(\)\/]', '', cleaned)
        
        # Replace multiple spaces with single space
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned
    
    def get_table_context(self, content: str, table_data: Dict[str, Any], context_lines: int = 3) -> str:
        """
        Get context around a table (preceding and following text).
        
        Args:
            content: Original markdown content
            table_data: Table data with line information
            context_lines: Number of lines of context to include
            
        Returns:
            Context text around the table
        """
        lines = content.split('\n')
        start_line = max(0, table_data.get('line_start', 0) - context_lines)
        end_line = min(len(lines), table_data.get('line_end', len(lines)) + context_lines + 1)
        
        context_lines_list = lines[start_line:table_data.get('line_start', 0)]
        context_lines_list.extend(lines[table_data.get('line_end', 0) + 1:end_line])
        
        return '\n'.join(context_lines_list).strip()