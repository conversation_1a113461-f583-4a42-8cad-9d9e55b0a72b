"""
Specification normalization utilities for extracting dense values and standardizing units.
"""

import re
from typing import Any, Optional, Dict, Union
import pandas as pd


class SpecificationNormalizer:
    """Normalizes GPU specification values and units."""
    
    def __init__(self):
        # Pattern for dense/sparse values (e.g., "105/144", "1.1/2.2")
        self.dense_sparse_pattern = re.compile(r'(\d+(?:\.\d+)?)\s*/\s*(\d+(?:\.\d+)?)')
        
        # Pattern for extracting numerical values with units
        self.value_unit_pattern = re.compile(r'(\d+(?:\.\d+)?)\s*([a-zA-Z/]+(?:\s*[a-zA-Z/]+)*)')
        
        # Unit conversion mappings
        self.unit_mappings = {
            # FLOPS conversions (all to TFLOPS for consistency)
            'petaflops': 1000,  # petaFLOPS to teraFLOPS
            'petaflop': 1000,
            'pflops': 1000,
            'teraflops': 1,     # teraFLOPS (base unit)
            'teraflop': 1,
            'tflops': 1,
            'gigaflops': 0.001, # gigaFLOPS to teraFLOPS
            'gigaflop': 0.001,
            'gflops': 1,
            
            # OPS conversions (all to TOPS for consistency)
            'petaops': 1000,    # petaOPS to teraOPS
            'teraops': 1,       # teraOPS (base unit)
            'tops': 1,
            'gigaops': 0.001,   # gigaOPS to teraOPS
            'gops': 0.001,
            
            # Memory size conversions (all to GB for consistency)
            'tb': 1024,         # TB to GB
            'gb': 1,            # GB (base unit)
            'mb': 0.001,        # MB to GB
            
            # Bandwidth conversions (all to GB/s for consistency)
            'tb/s': 1024,       # TB/s to GB/s
            'tbps': 1024,       # TB/s to GB/s (alternative notation)
            'gb/s': 1,          # GB/s (base unit)
            'gbps': 1,          # GB/s (alternative notation)
            'mb/s': 0.001,      # MB/s to GB/s
            'mbps': 0.001,      # MB/s (alternative notation)
            
            # Power conversions (all to Watts for consistency)
            'kw': 1000,         # kW to Watts
            'w': 1,             # Watts (base unit)
            'watts': 1,
            'watt': 1,
        }
        
        # Specification categories for organization
        self.spec_categories = {
            'hardware_specs': [
                'sms', 'sm', 'streaming multiprocessors',
                'tpcs', 'tpc', 'texture processing clusters',
                'cores per sm', 'fp32 cores', 'fp64 cores', 'int32 cores',
                'tensor cores', 'rt cores', 'cores / sm', 'cores / gpu',
                'gpu boost clock', 'base clock', 'memory clock',
                'gpu architecture', 'gpu board form factor'
            ],
            'computing_capacity': [
                'fp4', 'fp8', 'fp6', 'fp16', 'bf16', 'tf32', 'fp32', 'fp64',
                'int4', 'int8', 'int16', 'int32',
                'tensor', 'flops', 'ops', 'tflops', 'petaflops', 'teraflops',
                'peak', 'performance', 'accumulate'
            ],
            'memory_info': [
                'memory size', 'memory bandwidth', 'memory interface',
                'hbm', 'gddr', 'l2 cache', 'l1 cache', 'shared memory',
                'register file', 'memory data rate', 'cache size',
                'memory', 'bandwidth'
            ],
            'system_info': [
                'tdp', 'power', 'thermal design power',
                'interconnect', 'nvlink', 'pcie',
                'manufacturing process', 'transistors', 'die size',
                'form factor', 'board'
            ]
        }
    
    def extract_dense_value(self, value_str: str) -> Optional[float]:
        """
        Extract dense value from dense/sparse format.
        
        Args:
            value_str: String that may contain dense/sparse values like "105/144"
            
        Returns:
            Dense value as float, or None if no dense value found
        """
        if not isinstance(value_str, str):
            return None
        
        # Clean the string
        cleaned = value_str.strip().lower()
        
        # Check for dense/sparse pattern
        match = self.dense_sparse_pattern.search(cleaned)
        if match:
            return float(match.group(1))  # Return the dense value (first number)
        
        # If no dense/sparse pattern, try to extract any numerical value
        value_match = self.value_unit_pattern.search(cleaned)
        if value_match:
            return float(value_match.group(1))
        
        # Try to parse as a simple number
        try:
            # Remove common non-numeric characters
            numeric_str = re.sub(r'[^\d\.]', '', cleaned)
            if numeric_str:
                return float(numeric_str)
        except ValueError:
            pass
        
        return None
    
    def normalize_unit(self, value_str: str) -> Dict[str, Any]:
        """
        Normalize a value with units to standard units.
        
        Args:
            value_str: String containing value and unit (e.g., "105 petaFLOPS")
            
        Returns:
            Dictionary with normalized value, original value, unit, and normalized unit
        """
        if not isinstance(value_str, str):
            return {
                'normalized_value': None,
                'original_value': value_str,
                'original_unit': None,
                'normalized_unit': None
            }
        
        cleaned = value_str.strip().lower()
        
        # Extract dense value
        dense_value = self.extract_dense_value(value_str)
        if dense_value is None:
            return {
                'normalized_value': None,
                'original_value': value_str,
                'original_unit': None,
                'normalized_unit': None
            }
        
        # Extract unit by looking for known unit patterns
        original_unit = None
        normalized_unit = None
        normalized_value = dense_value
        
        # Look for unit patterns in the original string (prioritize longer matches)
        sorted_units = sorted(self.unit_mappings.items(), key=lambda x: len(x[0]), reverse=True)
        for unit_key, multiplier in sorted_units:
            if unit_key in cleaned:
                original_unit = unit_key
                normalized_value = dense_value * multiplier
                normalized_unit = self._get_normalized_unit_name(unit_key)
                break
        
        # If no specific unit found, try to extract any unit-like text
        if original_unit is None:
            unit_match = self.value_unit_pattern.search(cleaned)
            if unit_match:
                original_unit = unit_match.group(2).strip()
        
        return {
            'normalized_value': normalized_value,
            'original_value': value_str,
            'original_unit': original_unit,
            'normalized_unit': normalized_unit or original_unit
        }
    
    def _get_normalized_unit_name(self, unit_key: str) -> str:
        """Get the standard unit name for a unit key."""
        if 'flops' in unit_key:
            return 'TFLOPS'
        elif 'ops' in unit_key:
            return 'TOPS'
        elif unit_key in ['tb', 'gb', 'mb']:
            return 'GB'
        elif 'tb/s' in unit_key or 'tbps' in unit_key:
            return 'GB/s'
        elif 'gb/s' in unit_key or 'gbps' in unit_key:
            return 'GB/s'
        elif 'mb/s' in unit_key or 'mbps' in unit_key:
            return 'GB/s'
        elif unit_key in ['kw', 'w', 'watts', 'watt']:
            return 'W'
        else:
            return unit_key.upper()
    
    def categorize_specification(self, spec_name: str) -> str:
        """
        Categorize a specification name into one of the main categories.
        
        Args:
            spec_name: Name of the specification
            
        Returns:
            Category name ('hardware_specs', 'computing_capacity', 'memory_info', 'system_info', 'other')
        """
        spec_lower = spec_name.lower().strip()
        
        for category, keywords in self.spec_categories.items():
            for keyword in keywords:
                if keyword in spec_lower:
                    return category
        
        return 'other'
    
    def normalize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize all values in a DataFrame.
        
        Args:
            df: DataFrame with GPU specifications
            
        Returns:
            DataFrame with normalized values and additional metadata columns
        """
        if df.empty:
            return df
        
        normalized_df = df.copy()
        
        # Process each column (except the first one which is typically the GPU model name)
        for col in df.columns[1:]:  # Skip first column (usually GPU names)
            # Create new columns for normalized data
            normalized_values = []
            original_units = []
            normalized_units = []
            categories = []
            
            for value in df[col]:
                norm_result = self.normalize_unit(str(value))
                normalized_values.append(norm_result['normalized_value'])
                original_units.append(norm_result['original_unit'])
                normalized_units.append(norm_result['normalized_unit'])
                categories.append(self.categorize_specification(col))
            
            # Update the main column with normalized values
            normalized_df[col] = normalized_values
            
            # Add metadata columns
            normalized_df[f'{col}_original_unit'] = original_units
            normalized_df[f'{col}_normalized_unit'] = normalized_units
            normalized_df[f'{col}_category'] = categories
        
        return normalized_df
    
    def get_specification_metadata(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """
        Get metadata about specifications in the DataFrame.
        
        Args:
            df: DataFrame with GPU specifications
            
        Returns:
            Dictionary with metadata for each specification column
        """
        metadata = {}
        
        for col in df.columns:
            if col.endswith('_original_unit') or col.endswith('_normalized_unit') or col.endswith('_category'):
                continue
            
            # For tables where the first column contains specification names,
            # use those names for categorization instead of column names
            if col == df.columns[0] and not df.empty:
                # This is likely a specification names column
                spec_names = df[col].dropna().astype(str).tolist()
                categories = [self.categorize_specification(spec) for spec in spec_names]
                category_counts = {}
                for cat in categories:
                    category_counts[cat] = category_counts.get(cat, 0) + 1
                # Use the most common category
                category = max(category_counts.items(), key=lambda x: x[1])[0] if category_counts else 'other'
            else:
                category = self.categorize_specification(col)
            
            # Get sample values to determine units
            sample_values = df[col].dropna().head(3).tolist()
            sample_normalized = [self.normalize_unit(str(val)) for val in sample_values]
            
            common_unit = None
            if sample_normalized:
                units = [norm['normalized_unit'] for norm in sample_normalized if norm['normalized_unit']]
                if units:
                    # Get most common unit
                    common_unit = max(set(units), key=units.count)
            
            metadata[col] = {
                'category': category,
                'common_unit': common_unit,
                'sample_values': sample_values,
                'has_dense_sparse': any('/' in str(val) for val in sample_values if val)
            }
        
        return metadata