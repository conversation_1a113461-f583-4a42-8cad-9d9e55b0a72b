// GPU Table Management Module
// Handles the Individual GPUs tab functionality including table rendering, 
// column categorization, sorting, and filtering

class GPUTableManager {
    constructor() {
        this.gpuData = [];
        this.filteredData = [];
        this.columnCategories = {};
        this.visibleColumns = new Set();
        this.hiddenRows = new Set();
        this.columnOrder = [];
        this.sortConfig = { column: null, direction: 'asc' };
        this.searchTerm = '';
        this.apiCategories = null;
        this.editingCell = null;
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 50;
        
        // Default column categories (will be updated from API)
        this.defaultColumnCategories = {
            'Basic Info': ['model_name', 'source_file', 'table_context'],
            'Hardware Specs': [
                'architecture', 'sms', 'fp32_cores_per_sm', 
                'fp64_cores_per_sm', 'tensor_cores_per_sm', 'tensor_cores_total'
            ],
            'Computing Capacity': [
                'fp4_tensor_tflops', 'fp8_tensor_tflops', 'fp16_tensor_tflops', 
                'fp32_tflops', 'fp64_tflops'
            ],
            'Memory Info': [
                'memory_size_gb', 'memory_bandwidth_gbps', 'memory_type', 'l2_cache_mb'
            ],
            'System Info': [
                'tdp_watts', 'interconnect', 'manufacturing_process'
            ]
        };
        
        this.initializeVisibleColumns();
        this.loadSessionState();
        this.startAutoSave();
    }

    initializeVisibleColumns() {
        // Initially show all columns from default categories
        Object.values(this.defaultColumnCategories).forEach(columns => {
            columns.forEach(col => this.visibleColumns.add(col));
        });
    }

    // Session state management
    saveSessionState() {
        const state = {
            visibleColumns: Array.from(this.visibleColumns),
            hiddenRows: Array.from(this.hiddenRows),
            columnOrder: this.columnOrder,
            sortConfig: this.sortConfig,
            searchTerm: this.searchTerm,
            timestamp: Date.now(),
            version: '1.0'
        };
        try {
            sessionStorage.setItem('gpuTableState', JSON.stringify(state));
        } catch (error) {
            console.warn('Failed to save session state:', error);
        }
    }

    loadSessionState() {
        try {
            const savedState = sessionStorage.getItem('gpuTableState');
            if (savedState) {
                const state = JSON.parse(savedState);
                
                // Check if state is not too old (24 hours)
                const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
                if (state.timestamp && (Date.now() - state.timestamp) > maxAge) {
                    console.log('Session state expired, using defaults');
                    this.clearSessionState();
                    return;
                }
                
                this.visibleColumns = new Set(state.visibleColumns || []);
                this.hiddenRows = new Set(state.hiddenRows || []);
                this.columnOrder = state.columnOrder || [];
                this.sortConfig = state.sortConfig || { column: null, direction: 'asc' };
                this.searchTerm = state.searchTerm || '';
                
                // Apply search term to UI if it exists
                if (this.searchTerm) {
                    const searchInput = document.getElementById('gpu-search');
                    if (searchInput) {
                        searchInput.value = this.searchTerm;
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to load session state:', error);
            this.clearSessionState();
        }
    }

    clearSessionState() {
        sessionStorage.removeItem('gpuTableState');
    }

    // Auto-save state periodically
    startAutoSave() {
        // Save state every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            this.saveSessionState();
        }, 30000);
    }

    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }

    resetTableView() {
        this.saveState('reset_table');
        
        // Reset all customizations
        this.visibleColumns.clear();
        this.hiddenRows.clear();
        this.columnOrder = [];
        this.sortConfig = { column: null, direction: 'asc' };
        this.searchTerm = '';
        
        // Reinitialize with default settings
        this.initializeVisibleColumns();
        this.updateColumnCategoriesFromData();
        
        // Clear search input
        const searchInput = document.getElementById('gpu-search');
        if (searchInput) {
            searchInput.value = '';
        }
        
        // Refresh display
        this.applyFilters();
        this.renderTable();
        this.setupColumnControls();
        this.setupRowControls();
        this.clearSessionState();
        
        this.showNotification('Table view reset to defaults', 'success');
    }

    // Undo/Redo functionality
    saveState(action) {
        const state = {
            action,
            timestamp: Date.now(),
            visibleColumns: Array.from(this.visibleColumns),
            hiddenRows: Array.from(this.hiddenRows),
            columnOrder: [...this.columnOrder],
            gpuData: JSON.parse(JSON.stringify(this.gpuData))
        };
        
        this.undoStack.push(state);
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
        this.redoStack = []; // Clear redo stack when new action is performed
    }

    undo() {
        if (this.undoStack.length === 0) return false;
        
        const currentState = {
            action: 'current',
            timestamp: Date.now(),
            visibleColumns: Array.from(this.visibleColumns),
            hiddenRows: Array.from(this.hiddenRows),
            columnOrder: [...this.columnOrder],
            gpuData: JSON.parse(JSON.stringify(this.gpuData))
        };
        
        this.redoStack.push(currentState);
        const previousState = this.undoStack.pop();
        
        this.restoreState(previousState);
        return true;
    }

    redo() {
        if (this.redoStack.length === 0) return false;
        
        const currentState = {
            action: 'current',
            timestamp: Date.now(),
            visibleColumns: Array.from(this.visibleColumns),
            hiddenRows: Array.from(this.hiddenRows),
            columnOrder: [...this.columnOrder],
            gpuData: JSON.parse(JSON.stringify(this.gpuData))
        };
        
        this.undoStack.push(currentState);
        const nextState = this.redoStack.pop();
        
        this.restoreState(nextState);
        return true;
    }

    restoreState(state) {
        this.visibleColumns = new Set(state.visibleColumns);
        this.hiddenRows = new Set(state.hiddenRows);
        this.columnOrder = [...state.columnOrder];
        this.gpuData = JSON.parse(JSON.stringify(state.gpuData));
        this.filteredData = this.gpuData.filter(gpu => !this.hiddenRows.has(gpu.model_name));
        
        this.renderTable();
        this.setupColumnControls();
        this.saveSessionState();
    }

    updateColumnCategories() {
        // Use API categories if available, otherwise fall back to default
        if (this.apiCategories && typeof this.apiCategories === 'object' && Object.keys(this.apiCategories).length > 0) {
            // Validate that categories contain arrays
            const validCategories = {};
            Object.entries(this.apiCategories).forEach(([category, columns]) => {
                if (Array.isArray(columns) && columns.length > 0) {
                    validCategories[category] = columns;
                }
            });
            
            if (Object.keys(validCategories).length > 0) {
                this.columnCategories = validCategories;
            } else {
                this.columnCategories = this.defaultColumnCategories;
            }
        } else {
            this.columnCategories = this.defaultColumnCategories;
        }
        
        // Update visible columns based on actual data
        this.updateVisibleColumnsFromData();
    }

    updateColumnCategoriesFromData() {
        // Create categories based on actual data columns
        if (this.gpuData.length === 0) {
            this.columnCategories = this.defaultColumnCategories;
            return;
        }
        
        const actualColumns = Object.keys(this.gpuData[0] || {});
        const categorizedColumns = new Set();
        
        // Start with default categories and filter to actual columns
        this.columnCategories = {};
        
        Object.entries(this.defaultColumnCategories).forEach(([category, columns]) => {
            const availableColumns = columns.filter(col => actualColumns.includes(col));
            if (availableColumns.length > 0) {
                this.columnCategories[category] = availableColumns;
                availableColumns.forEach(col => categorizedColumns.add(col));
            }
        });
        
        // Add any uncategorized columns to a "Other" category
        const uncategorizedColumns = actualColumns.filter(col => !categorizedColumns.has(col));
        if (uncategorizedColumns.length > 0) {
            this.columnCategories['Other'] = uncategorizedColumns;
        }
        
        this.updateVisibleColumnsFromData();
    }

    updateVisibleColumnsFromData() {
        // Update visible columns to only include columns that exist in the data
        if (this.gpuData.length === 0) return;
        
        const actualColumns = Object.keys(this.gpuData[0] || {});
        const newVisibleColumns = new Set();
        
        // Only keep visible columns that actually exist in the data
        this.visibleColumns.forEach(col => {
            if (actualColumns.includes(col)) {
                newVisibleColumns.add(col);
            }
        });
        
        // If no visible columns remain, show all available columns
        if (newVisibleColumns.size === 0) {
            actualColumns.forEach(col => newVisibleColumns.add(col));
        }
        
        this.visibleColumns = newVisibleColumns;
        
        // Initialize column order if not set
        if (this.columnOrder.length === 0) {
            this.columnOrder = actualColumns;
        } else {
            // Update column order to include new columns and remove non-existent ones
            const newOrder = this.columnOrder.filter(col => actualColumns.includes(col));
            const missingColumns = actualColumns.filter(col => !this.columnOrder.includes(col));
            this.columnOrder = [...newOrder, ...missingColumns];
        }
    }

    async loadGPUData() {
        console.log('loadGPUData started');
        try {
            const container = document.getElementById('gpu-data-container');
            console.log('Container found:', container);
            this.setLoadingState(container, true, 'Loading GPU specifications...');
            
            console.log('Fetching /api/gpus...');
            const response = await fetch('/api/gpus');
            console.log('Response received:', response.status);
            const data = await response.json();
            console.log('Data parsed:', data);
            
            if (data.success && data.data) {
                console.log('Data is valid, processing...');
                this.gpuData = data.data;
                console.log('GPU data set:', this.gpuData.length, 'records');
                this.applyFilters();
                console.log('Filters applied');
                
                // Update column categories from API if available
                if (data.categories) {
                    console.log('Categories from API:', data.categories);
                    this.apiCategories = data.categories;
                    this.updateColumnCategories();
                } else {
                    console.log('No categories from API, using data-based categories');
                    this.updateColumnCategoriesFromData();
                }
                
                console.log('About to render table...');
                this.renderTable();
                console.log('Table rendered');
                this.setupColumnControls();
                this.setupRowControls();
                console.log('Controls set up');
            } else {
                throw new Error(data.message || 'Failed to load GPU data');
            }
        } catch (error) {
            console.error('Error loading GPU data:', error);
            this.showError('Failed to load GPU specifications: ' + error.message);
        }
    }

    renderTable() {
        const container = document.getElementById('gpu-data-container');
        
        if (!this.filteredData.length) {
            container.innerHTML = `
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> No GPU data available. 
                    Please check if markdown files are present in the docs directory.
                </div>
            `;
            return;
        }

        // Create table structure with categorized headers
        const tableHTML = this.generateTableHTML();
        container.innerHTML = tableHTML;
        
        // Setup table interactions
        this.setupTableInteractions();
    }

    applyFilters() {
        // Apply search filter and hidden rows filter
        this.filteredData = this.gpuData.filter(gpu => {
            // Check if row is hidden
            if (this.hiddenRows.has(gpu.model_name)) {
                return false;
            }
            
            // Apply search filter
            if (this.searchTerm) {
                return Object.values(gpu).some(value => {
                    if (value == null) return false;
                    return value.toString().toLowerCase().includes(this.searchTerm);
                });
            }
            
            return true;
        });
    }

    generateTableHTML() {
        // Use column order for visible columns
        const orderedVisibleCols = this.columnOrder.filter(col => this.visibleColumns.has(col));
        
        // Generate categorized headers
        const headerHTML = this.generateCategorizedHeaders(orderedVisibleCols);
        
        // Generate table rows
        const rowsHTML = this.filteredData.map(gpu => {
            const cells = orderedVisibleCols.map(col => {
                const value = gpu[col] || '-';
                const formattedValue = this.formatCellValue(col, value);
                const isEditable = this.isColumnEditable(col);
                const editableClass = isEditable ? 'editable-cell' : '';
                return `<td data-column="${col}" data-gpu-id="${gpu.model_name}" class="${editableClass}" title="Double-click to edit">${formattedValue}</td>`;
            }).join('');
            
            const isHidden = this.hiddenRows.has(gpu.model_name);
            const rowClass = isHidden ? 'hidden-row' : '';
            const displayStyle = isHidden ? 'style="display: none;"' : '';
            
            // Add row controls
            const rowControls = `
                <div class="row-controls">
                    <button class="row-toggle-btn" data-action="toggle-row" data-gpu-id="${gpu.model_name}" 
                            title="${isHidden ? 'Show row' : 'Hide row'}">
                        <i class="bi bi-${isHidden ? 'eye' : 'eye-slash'}"></i>
                    </button>
                </div>
            `;
            
            return `<tr data-gpu-id="${gpu.model_name || 'unknown'}" class="${rowClass}" draggable="true" ${displayStyle}>
                        <td class="row-controls-cell" style="width: 40px; padding: 0.25rem;">${rowControls}</td>
                        ${cells}
                    </tr>`;
        }).join('');

        return `
            <table class="table table-striped table-hover" id="gpu-specifications-table">
                ${headerHTML}
                <tbody id="gpu-table-body">
                    ${rowsHTML}
                </tbody>
            </table>
        `;
    }

    isColumnEditable(column) {
        // Define which columns are editable (numerical values and some text fields)
        const editableColumns = [
            'fp4_tensor_tflops', 'fp8_tensor_tflops', 'fp16_tensor_tflops', 
            'fp32_tflops', 'fp64_tflops', 'memory_size_gb', 'memory_bandwidth_gbps',
            'l2_cache_mb', 'tdp_watts', 'sms', 'fp32_cores_per_sm', 'fp64_cores_per_sm',
            'tensor_cores_per_sm', 'tensor_cores_total', 'architecture', 'memory_type',
            'interconnect', 'manufacturing_process'
        ];
        return editableColumns.includes(column);
    }

    generateCategorizedHeaders(visibleCols) {
        // Create category header row
        let categoryHeaderHTML = '<tr class="table-secondary">';
        let columnHeaderHTML = '<tr>';
        
        // Add row controls header
        categoryHeaderHTML += '<th rowspan="2" class="text-center" style="width: 40px; vertical-align: middle;">Actions</th>';
        
        // Group visible columns by category in the correct order
        const orderedCategories = [];
        Object.entries(this.columnCategories).forEach(([category, columns]) => {
            // Ensure columns is an array
            const columnArray = Array.isArray(columns) ? columns : [];
            const visibleCategoryColumns = columnArray.filter(col => visibleCols.includes(col));
            if (visibleCategoryColumns.length > 0) {
                // Sort category columns by their position in columnOrder
                const sortedCategoryColumns = visibleCategoryColumns.sort((a, b) => {
                    return this.columnOrder.indexOf(a) - this.columnOrder.indexOf(b);
                });
                orderedCategories.push([category, sortedCategoryColumns]);
            }
        });
        
        orderedCategories.forEach(([category, columns]) => {
            // Add category-specific CSS class for color coding
            const categoryClass = category.toLowerCase().replace(/\s+/g, '-');
            categoryHeaderHTML += `<th colspan="${columns.length}" class="text-center category-header ${categoryClass}">
                <strong>${category}</strong>
            </th>`;
            
            columns.forEach(col => {
                const sortIcon = this.getSortIcon(col);
                const displayName = this.getColumnDisplayName(col);
                columnHeaderHTML += `
                    <th class="sortable-header draggable-column" data-column="${col}" data-category="${categoryClass}" draggable="true">
                        <div class="column-header-content">
                            <span class="column-title">${displayName}</span>
                            <span class="sort-icon">${sortIcon}</span>
                            <span class="drag-handle">⋮⋮</span>
                        </div>
                    </th>
                `;
            });
        });
        
        categoryHeaderHTML += '</tr>';
        columnHeaderHTML += '</tr>';
        
        return `<thead>${categoryHeaderHTML}${columnHeaderHTML}</thead>`;
    }

    getColumnDisplayName(column) {
        const displayNames = {
            'model_name': 'Model',
            'source_file': 'Source File',
            'table_context': 'Context',
            'source_files': 'Source Files',
            'architecture': 'Architecture',
            'sms': 'SMs',
            'fp32_cores_per_sm': 'FP32 Cores/SM',
            'fp64_cores_per_sm': 'FP64 Cores/SM',
            'tensor_cores_per_sm': 'Tensor Cores/SM',
            'tensor_cores_total': 'Total Tensor Cores',
            'fp4_tensor_tflops': 'FP4 TFLOPS',
            'fp8_tensor_tflops': 'FP8 TFLOPS',
            'fp16_tensor_tflops': 'FP16 TFLOPS',
            'fp32_tflops': 'FP32 TFLOPS',
            'fp64_tflops': 'FP64 TFLOPS',
            'memory_size_gb': 'Memory (GB)',
            'memory_bandwidth_gbps': 'Bandwidth (GB/s)',
            'memory_type': 'Memory Type',
            'l2_cache_mb': 'L2 Cache (MB)',
            'tdp_watts': 'TDP (W)',
            'interconnect': 'Interconnect',
            'manufacturing_process': 'Process'
        };
        
        return displayNames[column] || column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    formatCellValue(column, value) {
        if (value === null || value === undefined || value === '' || value === 'nan') {
            return '-';
        }
        
        // Format numerical values
        if (typeof value === 'number') {
            if (column.includes('tflops') || column.includes('petaflops')) {
                return value.toFixed(1);
            } else if (column.includes('gb') || column.includes('gbps') || column.includes('mb')) {
                return value.toLocaleString();
            } else if (column === 'tdp_watts') {
                return value.toLocaleString();
            }
            return value.toString();
        }
        
        // Handle string values
        const stringValue = value.toString();
        
        // Truncate long text values for better display
        if (column === 'table_context' && stringValue.length > 100) {
            return `<span title="${stringValue.replace(/"/g, '&quot;')}">${stringValue.substring(0, 100)}...</span>`;
        }
        
        // Format file paths to show just the filename
        if (column === 'source_file' && stringValue.includes('/')) {
            const filename = stringValue.split('/').pop();
            return `<span title="${stringValue.replace(/"/g, '&quot;')}">${filename}</span>`;
        }
        
        return stringValue;
    }

    getSortIcon(column) {
        if (this.sortConfig.column === column) {
            return this.sortConfig.direction === 'asc' ? 
                '<i class="bi bi-arrow-up"></i>' : 
                '<i class="bi bi-arrow-down"></i>';
        }
        return '<i class="bi bi-arrow-up-down text-muted"></i>';
    }

    setupTableInteractions() {
        // Setup sorting (only on column title, not drag handle)
        document.querySelectorAll('.sortable-header .column-title').forEach(title => {
            title.addEventListener('click', (e) => {
                e.stopPropagation();
                const header = e.target.closest('.sortable-header');
                const column = header.getAttribute('data-column');
                this.sortTable(column);
            });
        });

        // Setup column drag and drop
        this.setupColumnDragAndDrop();
        
        // Setup row drag and drop
        this.setupRowDragAndDrop();
        
        // Setup inline editing
        this.setupInlineEditing();
        
        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Setup row controls
        this.setupRowControlsInteractions();

        // Setup undo/redo buttons
        this.setupUndoRedoButtons();

        // Setup table row hover effects
        document.querySelectorAll('#gpu-specifications-table tbody tr').forEach(row => {
            row.addEventListener('mouseenter', (e) => {
                if (!e.currentTarget.classList.contains('hidden-row')) {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                }
            });
            
            row.addEventListener('mouseleave', (e) => {
                if (!e.currentTarget.classList.contains('hidden-row')) {
                    e.currentTarget.style.backgroundColor = '';
                }
            });
        });
    }

    setupRowControlsInteractions() {
        document.querySelectorAll('[data-action="toggle-row"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const gpuId = btn.getAttribute('data-gpu-id');
                this.toggleRowVisibility(gpuId);
            });
        });
    }

    setupUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');

        if (undoBtn) {
            undoBtn.addEventListener('click', () => {
                if (this.undo()) {
                    this.showNotification('Undid last action', 'info');
                }
                this.updateUndoRedoButtons();
            });
        }

        if (redoBtn) {
            redoBtn.addEventListener('click', () => {
                if (this.redo()) {
                    this.showNotification('Redid last action', 'info');
                }
                this.updateUndoRedoButtons();
            });
        }

        // Initial button state
        this.updateUndoRedoButtons();
    }

    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');

        if (undoBtn) {
            undoBtn.disabled = this.undoStack.length === 0;
            undoBtn.title = this.undoStack.length > 0 ? 
                `Undo ${this.undoStack[this.undoStack.length - 1].action} (Ctrl+Z)` : 
                'Undo (Ctrl+Z)';
        }
        if (redoBtn) {
            redoBtn.disabled = this.redoStack.length === 0;
            redoBtn.title = this.redoStack.length > 0 ? 
                `Redo ${this.redoStack[this.redoStack.length - 1].action} (Ctrl+Y)` : 
                'Redo (Ctrl+Y)';
        }
    }

    // Cleanup method
    destroy() {
        this.stopAutoSave();
        this.saveSessionState();
        
        // Remove event listeners
        document.removeEventListener('keydown', this.keyboardHandler);
        
        // Clear references
        this.gpuData = [];
        this.filteredData = [];
        this.undoStack = [];
        this.redoStack = [];
    }

    toggleRowVisibility(gpuId) {
        this.saveState('toggle_row');
        
        if (this.hiddenRows.has(gpuId)) {
            this.hiddenRows.delete(gpuId);
        } else {
            this.hiddenRows.add(gpuId);
        }
        
        this.applyFilters();
        this.renderTable();
        this.setupRowControls();
        this.saveSessionState();
        
        const action = this.hiddenRows.has(gpuId) ? 'hidden' : 'shown';
        this.showNotification(`Row ${action}`, 'success');
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Remove existing notifications
        document.querySelectorAll('.notification').forEach(n => n.remove());

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${message}</span>
                <button type="button" class="btn-close btn-close-sm" aria-label="Close"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Setup close button
        notification.querySelector('.btn-close').addEventListener('click', () => {
            notification.remove();
        });

        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    setupColumnDragAndDrop() {
        const headers = document.querySelectorAll('.draggable-column');
        
        headers.forEach(header => {
            header.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', header.getAttribute('data-column'));
                header.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            });
            
            header.addEventListener('dragend', (e) => {
                header.classList.remove('dragging');
                // Clean up any remaining drag-over classes
                document.querySelectorAll('.drag-over').forEach(el => {
                    el.classList.remove('drag-over');
                });
            });
            
            header.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                header.classList.add('drag-over');
            });
            
            header.addEventListener('dragleave', (e) => {
                // Only remove drag-over if we're actually leaving the element
                if (!header.contains(e.relatedTarget)) {
                    header.classList.remove('drag-over');
                }
            });
            
            header.addEventListener('drop', (e) => {
                e.preventDefault();
                header.classList.remove('drag-over');
                
                const draggedColumn = e.dataTransfer.getData('text/plain');
                const targetColumn = header.getAttribute('data-column');
                
                if (draggedColumn !== targetColumn) {
                    this.reorderColumns(draggedColumn, targetColumn);
                }
            });
        });
    }

    setupRowDragAndDrop() {
        const rows = document.querySelectorAll('#gpu-table-body tr');
        
        rows.forEach(row => {
            row.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', row.getAttribute('data-gpu-id'));
                row.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            });
            
            row.addEventListener('dragend', (e) => {
                row.classList.remove('dragging');
                // Clean up any remaining drag-over classes
                document.querySelectorAll('#gpu-table-body tr.drag-over').forEach(el => {
                    el.classList.remove('drag-over');
                });
            });
            
            row.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                row.classList.add('drag-over');
            });
            
            row.addEventListener('dragleave', (e) => {
                // Only remove drag-over if we're actually leaving the row
                if (!row.contains(e.relatedTarget)) {
                    row.classList.remove('drag-over');
                }
            });
            
            row.addEventListener('drop', (e) => {
                e.preventDefault();
                row.classList.remove('drag-over');
                
                const draggedRowId = e.dataTransfer.getData('text/plain');
                const targetRowId = row.getAttribute('data-gpu-id');
                
                if (draggedRowId !== targetRowId) {
                    this.reorderRows(draggedRowId, targetRowId);
                }
            });
        });
    }

    setupInlineEditing() {
        document.querySelectorAll('.editable-cell').forEach(cell => {
            cell.addEventListener('dblclick', (e) => {
                e.preventDefault();
                this.startEditing(cell);
            });
        });
    }

    startEditing(cell) {
        if (this.editingCell) {
            this.cancelEditing();
        }

        this.editingCell = cell;
        const currentValue = cell.textContent.trim();
        const column = cell.getAttribute('data-column');
        const gpuId = cell.getAttribute('data-gpu-id');

        // Create input element
        const input = document.createElement('input');
        input.type = this.getInputType(column);
        input.value = currentValue === '-' ? '' : currentValue;
        input.className = 'cell-editor';
        
        // Add validation attributes
        this.addValidationAttributes(input, column);

        // Replace cell content with input
        cell.innerHTML = '';
        cell.appendChild(input);
        cell.classList.add('editing-cell');

        // Focus and select text
        input.focus();
        input.select();

        // Setup event listeners
        input.addEventListener('blur', () => {
            this.saveEditing();
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.saveEditing();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelEditing();
            }
        });

        input.addEventListener('input', (e) => {
            const isValid = this.validateInput(input, column);
            
            // Show validation feedback
            if (!isValid) {
                this.showValidationError(input, column);
            } else {
                this.hideValidationError(input);
            }
        });
    }

    getInputType(column) {
        const numericColumns = [
            'fp4_tensor_tflops', 'fp8_tensor_tflops', 'fp16_tensor_tflops', 
            'fp32_tflops', 'fp64_tflops', 'memory_size_gb', 'memory_bandwidth_gbps',
            'l2_cache_mb', 'tdp_watts', 'sms', 'fp32_cores_per_sm', 'fp64_cores_per_sm',
            'tensor_cores_per_sm', 'tensor_cores_total'
        ];
        
        return numericColumns.includes(column) ? 'number' : 'text';
    }

    addValidationAttributes(input, column) {
        if (this.getInputType(column) === 'number') {
            input.setAttribute('min', '0');
            input.setAttribute('step', 'any');
            
            // Set reasonable max values for different types
            if (column.includes('tflops') || column.includes('petaflops')) {
                input.setAttribute('max', '10000');
            } else if (column === 'memory_size_gb') {
                input.setAttribute('max', '1000');
            } else if (column === 'memory_bandwidth_gbps') {
                input.setAttribute('max', '10000');
            } else if (column === 'tdp_watts') {
                input.setAttribute('max', '2000');
            }
        }
    }

    validateInput(input, column) {
        const value = input.value.trim();
        let isValid = true;
        
        if (this.getInputType(column) === 'number' && value !== '') {
            const numValue = parseFloat(value);
            isValid = !isNaN(numValue) && numValue >= 0;
            
            // Additional validation for specific columns
            if (column === 'tdp_watts' && numValue > 2000) {
                isValid = false;
            } else if (column.includes('tflops') && numValue > 10000) {
                isValid = false;
            } else if (column === 'memory_size_gb' && numValue > 1000) {
                isValid = false;
            } else if (column === 'memory_bandwidth_gbps' && numValue > 10000) {
                isValid = false;
            }
        }
        
        if (isValid) {
            input.classList.remove('edit-validation-error');
        } else {
            input.classList.add('edit-validation-error');
        }
        
        return isValid;
    }

    async saveEditing() {
        if (!this.editingCell) return;

        const input = this.editingCell.querySelector('.cell-editor');
        if (!input) return;

        const column = this.editingCell.getAttribute('data-column');
        const gpuId = this.editingCell.getAttribute('data-gpu-id');
        const newValue = input.value.trim();

        // Validate input
        if (!this.validateInput(input, column)) {
            this.showNotification('Invalid value. Please check your input.', 'error');
            input.focus();
            return;
        }

        // Save state for undo
        this.saveState('edit_cell');

        try {
            // Update data locally first
            const gpu = this.gpuData.find(g => g.model_name === gpuId);
            const originalValue = gpu ? gpu[column] : null;
            
            if (gpu) {
                const processedValue = this.processEditedValue(newValue, column);
                gpu[column] = processedValue;
                
                // Update filtered data as well
                const filteredGpu = this.filteredData.find(g => g.model_name === gpuId);
                if (filteredGpu) {
                    filteredGpu[column] = processedValue;
                }
            }

            // Send update to server
            const updateData = {};
            updateData[column] = this.processEditedValue(newValue, column);
            
            const response = await fetch(`/api/gpus/${encodeURIComponent(gpuId)}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            const result = await response.json();

            if (!result.success) {
                // Revert local changes on server error
                if (gpu) {
                    gpu[column] = originalValue;
                    const filteredGpu = this.filteredData.find(g => g.model_name === gpuId);
                    if (filteredGpu) {
                        filteredGpu[column] = originalValue;
                    }
                }
                throw new Error(result.message || 'Failed to update value');
            }

            // Restore cell display
            this.editingCell.classList.remove('editing-cell');
            this.editingCell.innerHTML = this.formatCellValue(column, newValue || '-');
            this.editingCell = null;

            // Save session state
            this.saveSessionState();
            this.showNotification('Value updated successfully', 'success');

        } catch (error) {
            console.error('Error saving edit:', error);
            this.showNotification('Failed to save changes: ' + error.message, 'error');
            
            // Keep editing mode active on error
            input.focus();
        }
    }

    processEditedValue(value, column) {
        if (value === '') return null;
        
        if (this.getInputType(column) === 'number') {
            const numValue = parseFloat(value);
            return isNaN(numValue) ? null : numValue;
        }
        
        return value;
    }

    cancelEditing() {
        if (!this.editingCell) return;

        const column = this.editingCell.getAttribute('data-column');
        const gpuId = this.editingCell.getAttribute('data-gpu-id');
        
        // Find original value
        const gpu = this.gpuData.find(g => g.model_name === gpuId);
        const originalValue = gpu ? gpu[column] : '-';

        // Restore cell display
        this.editingCell.classList.remove('editing-cell');
        this.editingCell.innerHTML = this.formatCellValue(column, originalValue);
        this.editingCell = null;
    }

    setupKeyboardShortcuts() {
        // Store reference for cleanup
        this.keyboardHandler = (e) => {
            // Don't interfere with input fields (except our cell editor)
            if (e.target.tagName === 'INPUT' && !e.target.classList.contains('cell-editor')) {
                return;
            }
            
            // Ctrl+Z for undo
            if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                if (this.undo()) {
                    this.showNotification('Undid last action', 'info');
                }
                this.updateUndoRedoButtons();
            }
            
            // Ctrl+Y or Ctrl+Shift+Z for redo
            if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
                e.preventDefault();
                if (this.redo()) {
                    this.showNotification('Redid last action', 'info');
                }
                this.updateUndoRedoButtons();
            }
            
            // Escape to cancel editing
            if (e.key === 'Escape' && this.editingCell) {
                this.cancelEditing();
            }
            
            // Enter to save editing
            if (e.key === 'Enter' && this.editingCell) {
                e.preventDefault();
                this.saveEditing();
            }
            
            // Ctrl+A to select all visible columns
            if (e.ctrlKey && e.key === 'a' && !this.editingCell) {
                e.preventDefault();
                this.showAllColumns();
            }
            
            // Ctrl+H to hide all columns except model name
            if (e.ctrlKey && e.key === 'h' && !this.editingCell) {
                e.preventDefault();
                this.hideAllColumns();
            }
        };
        
        document.addEventListener('keydown', this.keyboardHandler);
    }

    showAllColumns() {
        this.saveState('show_all_columns');
        Object.values(this.columnCategories).flat().forEach(col => {
            this.visibleColumns.add(col);
        });
        this.setupColumnControls();
        this.renderTable();
        this.saveSessionState();
        this.showNotification('All columns shown (Ctrl+A)', 'success');
    }

    hideAllColumns() {
        this.saveState('hide_all_columns');
        this.visibleColumns.clear();
        // Keep at least the model name column visible
        this.visibleColumns.add('model_name');
        this.setupColumnControls();
        this.renderTable();
        this.saveSessionState();
        this.showNotification('All columns hidden except model name (Ctrl+H)', 'info');
    }

    showValidationError(input, column) {
        // Remove existing error tooltip
        this.hideValidationError(input);
        
        let errorMessage = 'Invalid value';
        if (this.getInputType(column) === 'number') {
            errorMessage = 'Please enter a valid positive number';
            
            if (column === 'tdp_watts') {
                errorMessage += ' (max 2000W)';
            } else if (column.includes('tflops')) {
                errorMessage += ' (max 10000 TFLOPS)';
            } else if (column === 'memory_size_gb') {
                errorMessage += ' (max 1000GB)';
            } else if (column === 'memory_bandwidth_gbps') {
                errorMessage += ' (max 10000 GB/s)';
            }
        }
        
        // Create error tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'validation-tooltip';
        tooltip.textContent = errorMessage;
        tooltip.style.cssText = `
            position: absolute;
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            white-space: nowrap;
            top: -30px;
            left: 0;
            pointer-events: none;
        `;
        
        input.parentElement.style.position = 'relative';
        input.parentElement.appendChild(tooltip);
    }

    hideValidationError(input) {
        const tooltip = input.parentElement.querySelector('.validation-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    reorderColumns(draggedColumn, targetColumn) {
        this.saveState('reorder_columns');
        
        const draggedIndex = this.columnOrder.indexOf(draggedColumn);
        const targetIndex = this.columnOrder.indexOf(targetColumn);
        
        if (draggedIndex !== -1 && targetIndex !== -1) {
            // Remove dragged column and insert at target position
            this.columnOrder.splice(draggedIndex, 1);
            const newTargetIndex = draggedIndex < targetIndex ? targetIndex - 1 : targetIndex;
            this.columnOrder.splice(newTargetIndex, 0, draggedColumn);
            
            this.renderTable();
            this.saveSessionState();
            this.showNotification('Column reordered', 'success');
        }
    }

    reorderRows(draggedRowId, targetRowId) {
        this.saveState('reorder_rows');
        
        const draggedIndex = this.filteredData.findIndex(gpu => gpu.model_name === draggedRowId);
        const targetIndex = this.filteredData.findIndex(gpu => gpu.model_name === targetRowId);
        
        if (draggedIndex !== -1 && targetIndex !== -1) {
            // Reorder in both filteredData and gpuData
            const draggedRow = this.filteredData.splice(draggedIndex, 1)[0];
            this.filteredData.splice(targetIndex, 0, draggedRow);
            
            // Also update the main data array
            const mainDraggedIndex = this.gpuData.findIndex(gpu => gpu.model_name === draggedRowId);
            const mainTargetIndex = this.gpuData.findIndex(gpu => gpu.model_name === targetRowId);
            
            if (mainDraggedIndex !== -1 && mainTargetIndex !== -1) {
                const mainDraggedRow = this.gpuData.splice(mainDraggedIndex, 1)[0];
                this.gpuData.splice(mainTargetIndex, 0, mainDraggedRow);
            }
            
            this.renderTable();
            this.saveSessionState();
            this.showNotification('Row reordered', 'success');
        }
    }

    sortTable(column) {
        if (this.sortConfig.column === column) {
            this.sortConfig.direction = this.sortConfig.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortConfig.column = column;
            this.sortConfig.direction = 'asc';
        }

        this.filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];
            
            // Handle null/undefined values
            if (aVal == null) aVal = '';
            if (bVal == null) bVal = '';
            
            // Convert to numbers if possible
            const aNum = parseFloat(aVal);
            const bNum = parseFloat(bVal);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return this.sortConfig.direction === 'asc' ? aNum - bNum : bNum - aNum;
            }
            
            // String comparison
            const aStr = aVal.toString().toLowerCase();
            const bStr = bVal.toString().toLowerCase();
            
            if (this.sortConfig.direction === 'asc') {
                return aStr.localeCompare(bStr);
            } else {
                return bStr.localeCompare(aStr);
            }
        });

        this.renderTable();
    }

    setupColumnControls() {
        const container = document.getElementById('column-checkboxes');
        container.innerHTML = '';
        
        Object.entries(this.columnCategories).forEach(([category, columns]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'col-md-3 mb-2 column-category';
            
            let checkboxesHTML = `<h6 class="text-muted">${category}</h6>`;
            // Ensure columns is an array
            const columnArray = Array.isArray(columns) ? columns : [];
            columnArray.forEach(column => {
                const displayName = this.getColumnDisplayName(column);
                const checked = this.visibleColumns.has(column) ? 'checked' : '';
                checkboxesHTML += `
                    <div class="form-check form-check-sm">
                        <input class="form-check-input column-toggle" type="checkbox" 
                               value="${column}" id="col-${column}" ${checked}>
                        <label class="form-check-label" for="col-${column}">
                            ${displayName}
                        </label>
                    </div>
                `;
            });
            
            categoryDiv.innerHTML = checkboxesHTML;
            container.appendChild(categoryDiv);
        });
        
        // Setup column toggle listeners
        document.querySelectorAll('.column-toggle').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.saveState('toggle_column');
                const column = e.target.value;
                if (e.target.checked) {
                    this.visibleColumns.add(column);
                } else {
                    this.visibleColumns.delete(column);
                }
                this.renderTable();
                this.saveSessionState();
            });
        });

        // Setup bulk column actions
        document.getElementById('show-all-columns')?.addEventListener('click', () => {
            this.saveState('show_all_columns');
            Object.values(this.columnCategories).flat().forEach(col => {
                this.visibleColumns.add(col);
            });
            this.setupColumnControls();
            this.renderTable();
            this.saveSessionState();
            this.showNotification('All columns shown', 'success');
        });

        document.getElementById('hide-all-columns')?.addEventListener('click', () => {
            this.saveState('hide_all_columns');
            this.visibleColumns.clear();
            // Keep at least the model name column visible
            this.visibleColumns.add('model_name');
            this.setupColumnControls();
            this.renderTable();
            this.saveSessionState();
            this.showNotification('All columns hidden except model name', 'info');
        });
    }

    setupRowControls() {
        const container = document.getElementById('row-checkboxes');
        if (!container) return;
        
        container.innerHTML = '';
        
        // Group rows by some criteria (e.g., architecture or alphabetically)
        const rowsByGroup = this.groupRowsForControls();
        
        Object.entries(rowsByGroup).forEach(([group, gpus]) => {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'col-md-4 mb-2 column-category';
            
            let checkboxesHTML = `<h6 class="text-muted">${group}</h6>`;
            gpus.forEach(gpu => {
                const checked = !this.hiddenRows.has(gpu.model_name) ? 'checked' : '';
                checkboxesHTML += `
                    <div class="form-check form-check-sm">
                        <input class="form-check-input row-toggle" type="checkbox" 
                               value="${gpu.model_name}" id="row-${gpu.model_name.replace(/\s+/g, '-')}" ${checked}>
                        <label class="form-check-label" for="row-${gpu.model_name.replace(/\s+/g, '-')}">
                            ${gpu.model_name}
                        </label>
                    </div>
                `;
            });
            
            groupDiv.innerHTML = checkboxesHTML;
            container.appendChild(groupDiv);
        });
        
        // Setup row toggle listeners
        document.querySelectorAll('.row-toggle').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.saveState('toggle_row');
                const gpuName = e.target.value;
                if (e.target.checked) {
                    this.hiddenRows.delete(gpuName);
                } else {
                    this.hiddenRows.add(gpuName);
                }
                this.applyFilters();
                this.renderTable();
                this.saveSessionState();
            });
        });

        // Setup bulk row actions
        document.getElementById('show-all-rows')?.addEventListener('click', () => {
            this.saveState('show_all_rows');
            this.hiddenRows.clear();
            this.setupRowControls();
            this.applyFilters();
            this.renderTable();
            this.saveSessionState();
            this.showNotification('All rows shown', 'success');
        });

        document.getElementById('hide-all-rows')?.addEventListener('click', () => {
            this.saveState('hide_all_rows');
            this.gpuData.forEach(gpu => {
                this.hiddenRows.add(gpu.model_name);
            });
            this.setupRowControls();
            this.applyFilters();
            this.renderTable();
            this.saveSessionState();
            this.showNotification('All rows hidden', 'info');
        });
    }

    groupRowsForControls() {
        // Group GPUs by architecture for better organization
        const groups = {};
        
        this.gpuData.forEach(gpu => {
            const architecture = gpu.architecture || 'Unknown';
            if (!groups[architecture]) {
                groups[architecture] = [];
            }
            groups[architecture].push(gpu);
        });
        
        // Sort GPUs within each group
        Object.keys(groups).forEach(arch => {
            groups[arch].sort((a, b) => a.model_name.localeCompare(b.model_name));
        });
        
        return groups;
    }

    setupSearch() {
        const searchInput = document.getElementById('gpu-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.filterData();
            });
            
            // Add clear search functionality
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    e.target.value = '';
                    this.searchTerm = '';
                    this.filterData();
                }
            });
        }
    }

    filterData() {
        if (!this.searchTerm) {
            this.filteredData = [...this.gpuData];
        } else {
            this.filteredData = this.gpuData.filter(gpu => {
                return Object.values(gpu).some(value => {
                    if (value == null) return false;
                    return value.toString().toLowerCase().includes(this.searchTerm);
                });
            });
        }
        this.renderTable();
        this.updateSearchResults();
    }

    updateSearchResults() {
        const container = document.getElementById('gpu-data-container');
        if (this.searchTerm && this.filteredData.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-search"></i> No GPU specifications found matching "${this.searchTerm}".
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="gpuTableManager.clearSearch()">
                        Clear Search
                    </button>
                </div>
            `;
        } else if (this.searchTerm && this.filteredData.length > 0) {
            // Add search results indicator
            const searchIndicator = document.createElement('div');
            searchIndicator.className = 'alert alert-info mb-2';
            searchIndicator.innerHTML = `
                <i class="bi bi-info-circle"></i> Showing ${this.filteredData.length} results for "${this.searchTerm}"
                <button class="btn btn-sm btn-outline-info ms-2" onclick="gpuTableManager.clearSearch()">
                    Clear Search
                </button>
            `;
            container.insertBefore(searchIndicator, container.firstChild);
        }
    }

    clearSearch() {
        this.searchTerm = '';
        const searchInput = document.getElementById('gpu-search');
        if (searchInput) {
            searchInput.value = '';
        }
        this.applyFilters();
        this.renderTable();
    }

    setLoadingState(container, isLoading, message = 'Loading...') {
        if (isLoading) {
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">${message}</p>
                </div>
            `;
        }
    }

    showError(message) {
        const container = document.getElementById('gpu-data-container');
        container.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i> ${message}
            </div>
        `;
    }
}

// Global instance
let gpuTableManager;

// Initialize GPU table functionality
function initializeGPUTable() {
    console.log('initializeGPUTable called');
    try {
        gpuTableManager = new GPUTableManager();
        console.log('GPUTableManager created');
        
        // Load initial data
        gpuTableManager.loadGPUData();
        console.log('loadGPUData called');
        
        // Setup search functionality
        gpuTableManager.setupSearch();
        console.log('setupSearch called');
        
        // Setup reset button
        document.getElementById('reset-table-btn')?.addEventListener('click', () => {
            gpuTableManager.resetTableView();
        });
        console.log('Event listeners set up');
    } catch (error) {
        console.error('Error in initializeGPUTable:', error);
    }
    
    // Setup refresh button
    document.getElementById('refresh-data')?.addEventListener('click', async () => {
        try {
            const response = await fetch('/api/refresh', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                gpuTableManager.showNotification('Data refreshed successfully', 'success');
                gpuTableManager.loadGPUData();
            } else {
                gpuTableManager.showNotification('Failed to refresh data: ' + result.message, 'error');
            }
        } catch (error) {
            gpuTableManager.showNotification('Error refreshing data: ' + error.message, 'error');
        }
    });
    
    // Setup export functionality
    document.querySelectorAll('[data-export]').forEach(btn => {
        btn.addEventListener('click', async (e) => {
            e.preventDefault();
            const format = btn.getAttribute('data-export');
            
            try {
                const response = await fetch(`/api/export/${format}`);
                const result = await response.json();
                
                if (result.success) {
                    gpuTableManager.showNotification(`Export functionality ready for ${format}`, 'info');
                } else {
                    gpuTableManager.showNotification('Export failed: ' + result.message, 'error');
                }
            } catch (error) {
                gpuTableManager.showNotification('Export error: ' + error.message, 'error');
            }
        });
    });
}

// Error display helper
function showError(message) {
    const container = document.getElementById('gpu-data-container');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i> ${message}
            </div>
        `;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GPUTableManager, initializeGPUTable };
}