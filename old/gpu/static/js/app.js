// GPU Visualization Tool JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('GPU Visualization Tool loaded');
    
    // Initialize any interactive components here
    initializeComponents();
});

function initializeComponents() {
    // Add any initialization code for interactive components
    console.log('Components initialized');
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
}

function hideAlert() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.remove();
    });
}