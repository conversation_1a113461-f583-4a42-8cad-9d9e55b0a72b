#!/usr/bin/env python3

"""
Debug script to see what's happening in the consolidation process.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    print("=== Consolidation Process Debug ===")
    print(f"Found {len(parsed_data['consolidated_tables'])} tables")
    
    # Test the consolidation process
    gpu_records = []
    
    for i, table in enumerate(parsed_data['consolidated_tables']):
        print(f"\n--- Table {i+1} from {table['source_file']} ---")
        df = table.get('dataframe')
        if df is None or df.empty:
            print("  Skipping: empty dataframe")
            continue
        
        print(f"  DataFrame shape: {df.shape}")
        print(f"  Has original_data: {'original_data' in table}")
        
        # Test the extraction method
        table_records = processor._extract_gpu_records_from_table(df, table)
        print(f"  Extracted {len(table_records)} records")
        
        if table_records:
            print(f"  Sample record model_name: {table_records[0]['model_name']}")
            print(f"  Sample record keys: {list(table_records[0].keys())}")
        
        gpu_records.extend(table_records)
    
    print(f"\n=== Final Results ===")
    print(f"Total GPU records: {len(gpu_records)}")
    if gpu_records:
        print("Model names:")
        for record in gpu_records[:10]:
            print(f"  - {record['model_name']}")

if __name__ == "__main__":
    main()