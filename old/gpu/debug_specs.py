#!/usr/bin/env python3

"""
Debug script to see what specifications are being extracted.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor
import pandas as pd

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    # Look at the first table (nvidia-gpu-comparison-table.md)
    table = parsed_data['consolidated_tables'][0]
    df = table['dataframe']
    
    print("=== NVIDIA GPU Comparison Table ===")
    print(f"Columns: {list(df.columns)}")
    print("\nFirst column (specifications):")
    for i, spec in enumerate(df.iloc[:, 0].head(10)):
        print(f"{i}: '{spec}'")
        standardized = processor._standardize_spec_name(str(spec))
        print(f"   -> Standardized: {standardized}")
    
    print("\n=== Sample GPU Model Column (NVIDIA A100) ===")
    a100_col = 'NVIDIA A100'
    if a100_col in df.columns:
        print(f"Values in {a100_col} column:")
        for i, (spec, value) in enumerate(zip(df.iloc[:, 0].head(10), df[a100_col].head(10))):
            print(f"{i}: '{spec}' = '{value}'")
            standardized_key = processor._standardize_spec_name(str(spec))
            if standardized_key:
                processed_value = processor._process_spec_value(str(value), standardized_key)
                print(f"   -> {standardized_key} = {processed_value}")
    
    # Test the extraction method directly
    print("\n=== Testing extraction method ===")
    records = processor._extract_from_model_columns(df, table)
    print(f"Extracted {len(records)} records")
    if records:
        print("First record:")
        for key, value in records[0].items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    main()