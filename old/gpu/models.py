"""
Data models for GPU specifications and system configurations.
"""
from dataclasses import dataclass
from typing import Optional


@dataclass
class GPUSpecification:
    """Individual GPU specification data model."""
    model_name: str
    architecture: str
    
    # Hardware Specs
    sms: Optional[int] = None
    fp32_cores_per_sm: Optional[int] = None
    fp64_cores_per_sm: Optional[int] = None
    tensor_cores_per_sm: Optional[int] = None
    tensor_cores_total: Optional[int] = None
    
    # Computing Capacity (dense values only)
    fp4_tensor_tflops: Optional[float] = None
    fp8_tensor_tflops: Optional[float] = None
    fp16_tensor_tflops: Optional[float] = None
    fp32_tflops: Optional[float] = None
    fp64_tflops: Optional[float] = None
    
    # Memory Info
    memory_size_gb: Optional[float] = None
    memory_bandwidth_gbps: Optional[float] = None
    memory_type: Optional[str] = None
    l2_cache_mb: Optional[float] = None
    
    # System Info
    tdp_watts: Optional[int] = None
    interconnect: Optional[str] = None
    manufacturing_process: Optional[str] = None


@dataclass
class SystemConfiguration:
    """System configuration data model for multi-GPU systems."""
    system_name: str
    gpu_count: int
    gpu_model: str
    cpu_count: Optional[int] = None
    cpu_model: Optional[str] = None
    
    # Aggregate Computing Capacity
    total_fp4_petaflops: Optional[float] = None
    total_fp8_petaflops: Optional[float] = None
    total_fp16_petaflops: Optional[float] = None
    total_fp32_teraflops: Optional[float] = None
    total_fp64_teraflops: Optional[float] = None
    
    # Aggregate Memory
    total_memory_tb: Optional[float] = None
    total_bandwidth_tbps: Optional[float] = None
    
    # System-level specs
    interconnect_bandwidth_tbps: Optional[float] = None
    total_tdp_kw: Optional[float] = None
    rack_configuration: Optional[str] = None