#!/usr/bin/env python3

"""
Debug script to see what the parser is extracting from the markdown files.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor
import json

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    print("Parsing markdown files...")
    parsed_data = parser.parse_directory('docs')
    
    print(f"Found {len(parsed_data['consolidated_tables'])} tables")
    
    # Look at the first few tables
    for i, table in enumerate(parsed_data['consolidated_tables'][:3]):
        print(f"\n=== Table {i+1} from {table['source_file']} ===")
        print(f"Context: {table.get('context', 'No context')}")
        
        df = table['dataframe']
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print("\nFirst few rows:")
        print(df.head())
        
        # Test GPU model detection
        print(f"\nGPU models in headers: {processor._has_gpu_models_in_headers(df)}")
        print(f"GPU models in first column: {processor._contains_gpu_models_in_first_column(df)}")
        
        # Try extracting records
        records = processor._extract_gpu_records_from_table(df, table)
        print(f"Extracted {len(records)} records")
        if records:
            print("Sample record keys:", list(records[0].keys()))

if __name__ == "__main__":
    main()