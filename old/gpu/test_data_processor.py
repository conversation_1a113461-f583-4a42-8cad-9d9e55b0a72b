"""
Unit tests for data processing and consolidation functionality.
"""

import unittest
import pandas as pd
from unittest.mock import Mock, patch
import tempfile
import os

from data_processor import GPUDataProcessor, SystemDataProcessor
from models import GPUSpecification, SystemConfiguration


class TestGPUDataProcessor(unittest.TestCase):
    """Test cases for GPUDataProcessor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = GPUDataProcessor()
        
        # Sample parsed data structure
        self.sample_parsed_data = {
            'files': {
                'test_file.md': {
                    'file_path': 'test_file.md',
                    'tables': []
                }
            },
            'consolidated_tables': [
                {
                    'dataframe': pd.DataFrame({
                        'GPU Features': ['NVIDIA A100', 'NVIDIA H100 SXM5', 'NVIDIA H100 PCIe'],
                        'SMs': [108, 132, 114],
                        'Peak FP32 TFLOPS': [19.5, 66.9, 51.2],
                        'Memory Size': ['40 GB', '80 GB', '80 GB'],
                        'TDP': ['400 Watts', '700 Watts', '350 Watts']
                    }),
                    'source_file': 'test_file.md',
                    'context': {'title': 'GPU Comparison'}
                }
            ],
            'metadata': {
                'total_files': 1,
                'parsing_errors': []
            }
        }
        
        # Sample table with GPU models as columns
        self.gpu_columns_table = {
            'dataframe': pd.DataFrame({
                'Specification': ['SMs', 'FP32 TFLOPS', 'Memory Size', 'TDP'],
                'NVIDIA A100': [108, 19.5, '40 GB', '400 W'],
                'NVIDIA H100': [132, 66.9, '80 GB', '700 W']
            }),
            'source_file': 'columns_test.md',
            'context': {}
        }
    
    def test_consolidate_gpu_data_empty_input(self):
        """Test consolidation with empty input data."""
        empty_data = {'consolidated_tables': []}
        result = self.processor.consolidate_gpu_data(empty_data)
        
        self.assertTrue(result.empty)
    
    def test_consolidate_gpu_data_valid_input(self):
        """Test consolidation with valid input data."""
        result = self.processor.consolidate_gpu_data(self.sample_parsed_data)
        
        self.assertFalse(result.empty)
        self.assertIn('model_name', result.columns)
        self.assertEqual(len(result), 3)  # Should have 3 GPU models
    
    def test_extract_gpu_records_from_model_columns(self):
        """Test extraction when GPU models are column headers."""
        # Modify sample data to have GPU models as columns
        test_data = self.sample_parsed_data.copy()
        test_data['consolidated_tables'] = [self.gpu_columns_table]
        
        result = self.processor.consolidate_gpu_data(test_data)
        
        self.assertFalse(result.empty)
        self.assertTrue(any('A100' in str(name) for name in result['model_name']))
        self.assertTrue(any('H100' in str(name) for name in result['model_name']))
    
    def test_looks_like_gpu_model(self):
        """Test GPU model detection."""
        # Positive cases
        self.assertTrue(self.processor._looks_like_gpu_model('NVIDIA A100'))
        self.assertTrue(self.processor._looks_like_gpu_model('H100 SXM5'))
        self.assertTrue(self.processor._looks_like_gpu_model('RTX 4090'))
        self.assertTrue(self.processor._looks_like_gpu_model('Tesla V100'))
        
        # Negative cases
        self.assertFalse(self.processor._looks_like_gpu_model('Memory Size'))
        self.assertFalse(self.processor._looks_like_gpu_model('TDP'))
        self.assertFalse(self.processor._looks_like_gpu_model('TFLOPS'))
        self.assertFalse(self.processor._looks_like_gpu_model('Bandwidth'))
    
    def test_standardize_spec_name(self):
        """Test specification name standardization."""
        # Test direct mappings
        self.assertEqual(self.processor._standardize_spec_name('SMs'), 'sms')
        self.assertEqual(self.processor._standardize_spec_name('FP32 Cores / SM'), 'fp32_cores_per_sm')
        self.assertEqual(self.processor._standardize_spec_name('Memory Size'), 'memory_size_gb')
        self.assertEqual(self.processor._standardize_spec_name('TDP'), 'tdp_watts')
        
        # Test fuzzy matching
        self.assertEqual(self.processor._standardize_spec_name('Peak FP16 Tensor TFLOPS'), 'fp16_tensor_tflops')
        self.assertEqual(self.processor._standardize_spec_name('Peak FP32 TFLOPS (non-tensor)'), 'fp32_tflops')
        
        # Test unknown specification
        self.assertIsNone(self.processor._standardize_spec_name('Unknown Spec'))
    
    def test_resolve_conflicts_no_duplicates(self):
        """Test conflict resolution with no duplicate models."""
        df = pd.DataFrame({
            'model_name': ['A100', 'H100'],
            'sms': [108, 132],
            'source_file': ['file1.md', 'file2.md']
        })
        
        result = self.processor._resolve_conflicts(df)
        
        self.assertEqual(len(result), 2)
        self.assertEqual(list(result['model_name']), ['A100', 'H100'])
    
    def test_resolve_conflicts_with_duplicates(self):
        """Test conflict resolution with duplicate models."""
        df = pd.DataFrame({
            'model_name': ['A100', 'A100', 'H100'],
            'sms': [108, 108, 132],
            'fp32_tflops': [19.5, 20.0, 66.9],  # Conflicting values
            'source_file': ['file1.md', 'file2.md', 'file3.md']
        })
        
        result = self.processor._resolve_conflicts(df)
        
        self.assertEqual(len(result), 2)  # Should merge duplicates
        self.assertIn('A100', result['model_name'].values)
        self.assertIn('H100', result['model_name'].values)
    
    def test_choose_best_value_dense_sparse(self):
        """Test choosing best value for dense/sparse specifications."""
        # Test with dense/sparse values
        values = ['19.5/39.0', '20.0']
        result = self.processor._choose_best_value(values, 'fp32_tflops')
        
        # Should prefer the value with higher dense component
        self.assertEqual(result, '20.0')
    
    def test_choose_best_value_text(self):
        """Test choosing best value for text specifications."""
        values = ['HBM2', 'HBM2e 5120-bit']
        result = self.processor._choose_best_value(values, 'memory_type')
        
        # Should prefer longer, more descriptive value
        self.assertEqual(result, 'HBM2e 5120-bit')
    
    def test_categorize_specifications(self):
        """Test specification categorization."""
        df = pd.DataFrame({
            'model_name': ['A100', 'H100'],
            'sms': [108, 132],
            'fp32_tflops': [19.5, 66.9],
            'memory_size_gb': [40, 80],
            'tdp_watts': [400, 700]
        })
        
        result = self.processor.categorize_specifications(df)
        
        self.assertIn('hardware_specs', result)
        self.assertIn('computing_capacity', result)
        self.assertIn('memory_info', result)
        self.assertIn('system_info', result)
        
        # Check that specifications are in correct categories
        self.assertIn('sms', result['hardware_specs'])
        self.assertIn('fp32_tflops', result['computing_capacity'])
        self.assertIn('memory_size_gb', result['memory_info'])
        self.assertIn('tdp_watts', result['system_info'])
    
    def test_get_processing_summary(self):
        """Test processing summary generation."""
        consolidated_df = pd.DataFrame({
            'model_name': ['A100', 'H100'],
            'sms': [108, 132],
            'fp32_tflops': [19.5, 66.9]
        })
        
        result = self.processor.get_processing_summary(self.sample_parsed_data, consolidated_df)
        
        self.assertEqual(result['input_files'], 1)
        self.assertEqual(result['input_tables'], 1)
        self.assertEqual(result['output_gpu_models'], 2)
        self.assertGreater(result['specifications_found'], 0)
        self.assertIn('categories', result)


class TestSystemDataProcessor(unittest.TestCase):
    """Test cases for SystemDataProcessor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = SystemDataProcessor()
        
        # Sample GPU data
        self.gpu_data = pd.DataFrame({
            'model_name': ['NVIDIA A100', 'NVIDIA H100'],
            'sms': [108, 132],
            'fp32_tflops': [19.5, 66.9],
            'memory_size_gb': [40, 80],
            'tdp_watts': [400, 700]
        })
        
        # Sample system configuration data
        self.system_parsed_data = {
            'consolidated_tables': [
                {
                    'dataframe': pd.DataFrame({
                        'System': ['DGX A100', 'DGX H100', 'HGX H100'],
                        'GPUs': ['8x A100', '8x H100', '4x H100'],
                        'Total Memory': ['320 GB', '640 GB', '320 GB'],
                        'Power': ['6.5 kW', '10.2 kW', '5.1 kW']
                    }),
                    'source_file': 'systems.md',
                    'context': {'title': 'System Configurations'}
                }
            ]
        }
    
    def test_process_system_configs_empty_input(self):
        """Test system processing with empty input."""
        empty_data = {'consolidated_tables': []}
        result = self.processor.process_system_configs(empty_data, self.gpu_data)
        
        # Should generate basic configs from GPU data
        self.assertFalse(result.empty)
        self.assertIn('system_name', result.columns)
    
    def test_process_system_configs_with_data(self):
        """Test system processing with valid system data."""
        result = self.processor.process_system_configs(self.system_parsed_data, self.gpu_data)
        
        self.assertFalse(result.empty)
        self.assertIn('system_name', result.columns)
        self.assertIn('gpu_count', result.columns)
        self.assertIn('gpu_model', result.columns)
    
    def test_is_system_configuration_table(self):
        """Test system configuration table detection."""
        # Positive case
        system_df = pd.DataFrame({
            'System': ['DGX A100', 'HGX H100'],
            'GPUs': ['8x A100', '4x H100']
        })
        table_info = {'context': {'title': 'System Configurations'}}
        
        result = self.processor._is_system_configuration_table(system_df, table_info)
        self.assertTrue(result)
        
        # Negative case
        gpu_df = pd.DataFrame({
            'GPU Model': ['A100', 'H100'],
            'TFLOPS': [19.5, 66.9]
        })
        table_info = {'context': {}}
        
        result = self.processor._is_system_configuration_table(gpu_df, table_info)
        self.assertFalse(result)
    
    def test_extract_gpu_info_from_record(self):
        """Test GPU information extraction from system records."""
        record = {'system_name': '8x nvidia h100 dgx system'}
        
        self.processor._extract_gpu_info_from_record(record, self.gpu_data)
        
        self.assertEqual(record['gpu_count'], 8)
        self.assertIn('gpu_model', record)
    
    def test_standardize_system_spec_name(self):
        """Test system specification name standardization."""
        self.assertEqual(self.processor._standardize_system_spec_name('GPUs'), 'gpu_count')
        self.assertEqual(self.processor._standardize_system_spec_name('GPU Count'), 'gpu_count')
        self.assertEqual(self.processor._standardize_system_spec_name('Power'), 'total_tdp_kw')
        self.assertEqual(self.processor._standardize_system_spec_name('System'), 'system_name')
        
        # Unknown spec
        self.assertIsNone(self.processor._standardize_system_spec_name('Unknown'))
    
    def test_generate_basic_system_configs(self):
        """Test basic system configuration generation."""
        result = self.processor._generate_basic_system_configs(self.gpu_data)
        
        self.assertGreater(len(result), 0)
        
        # Should have single GPU systems
        single_gpu_systems = [r for r in result if r['gpu_count'] == 1]
        self.assertEqual(len(single_gpu_systems), len(self.gpu_data))
        
        # Should have multi-GPU systems for popular models
        multi_gpu_systems = [r for r in result if r['gpu_count'] > 1]
        self.assertGreater(len(multi_gpu_systems), 0)
    
    def test_calculate_aggregate_specs(self):
        """Test aggregate specification calculation."""
        system_df = pd.DataFrame({
            'system_name': ['4x A100 System', '8x H100 System'],
            'gpu_count': [4, 8],
            'gpu_model': ['NVIDIA A100', 'NVIDIA H100'],
            'source_file': ['test.md', 'test.md']
        })
        
        result = self.processor._calculate_aggregate_specs(system_df, self.gpu_data)
        
        # Check that aggregate specs were calculated
        self.assertIn('total_fp32_teraflops', result.columns)
        self.assertIn('total_memory_tb', result.columns)
        self.assertIn('total_tdp_kw', result.columns)
        
        # Verify calculations
        a100_row = result[result['gpu_model'] == 'NVIDIA A100'].iloc[0]
        expected_fp32 = 19.5 * 4  # 4 GPUs * 19.5 TFLOPS each
        self.assertAlmostEqual(a100_row['total_fp32_teraflops'], expected_fp32, places=1)
        
        expected_memory = (40 * 4) / 1024  # 4 GPUs * 40 GB each, converted to TB
        self.assertAlmostEqual(a100_row['total_memory_tb'], expected_memory, places=2)
    
    def test_find_fuzzy_gpu_match(self):
        """Test fuzzy GPU model matching."""
        # Exact match
        result = self.processor._find_fuzzy_gpu_match('NVIDIA A100', self.gpu_data)
        self.assertFalse(result.empty)
        
        # Partial match
        result = self.processor._find_fuzzy_gpu_match('A100', self.gpu_data)
        self.assertFalse(result.empty)
        
        # No match
        result = self.processor._find_fuzzy_gpu_match('Unknown GPU', self.gpu_data)
        self.assertTrue(result.empty)
    
    def test_get_normalized_gpu_value(self):
        """Test normalized GPU value extraction."""
        gpu_specs = pd.Series({
            'fp32_tflops': 19.5,
            'fp32_tflops_normalized': 19.5,
            'memory_size_gb': '40 GB'
        })
        
        # Test normalized column
        result = self.processor._get_normalized_gpu_value(gpu_specs, 'fp32_tflops')
        self.assertEqual(result, 19.5)
        
        # Test original column with text
        result = self.processor._get_normalized_gpu_value(gpu_specs, 'memory_size_gb')
        self.assertEqual(result, 40.0)
    
    def test_get_system_processing_summary(self):
        """Test system processing summary generation."""
        system_df = pd.DataFrame({
            'system_name': ['4x A100', '8x H100'],
            'gpu_count': [4, 8],
            'gpu_model': ['A100', 'H100'],
            'total_fp32_teraflops': [78.0, 535.2]
        })
        
        result = self.processor.get_system_processing_summary(system_df)
        
        self.assertEqual(result['total_systems'], 2)
        self.assertEqual(set(result['gpu_models_used']), {'A100', 'H100'})
        self.assertEqual(result['gpu_count_distribution'], {4: 1, 8: 1})
        self.assertEqual(result['aggregate_specs_calculated'], 2)


class TestDataProcessorIntegration(unittest.TestCase):
    """Integration tests for data processor components."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.gpu_processor = GPUDataProcessor()
        self.system_processor = SystemDataProcessor()
        
        # Create comprehensive test data
        self.comprehensive_parsed_data = {
            'files': {
                'gpu_specs.md': {'file_path': 'gpu_specs.md'},
                'system_configs.md': {'file_path': 'system_configs.md'}
            },
            'consolidated_tables': [
                # GPU specifications table
                {
                    'dataframe': pd.DataFrame({
                        'GPU Features': ['NVIDIA A100', 'NVIDIA H100 SXM5', 'NVIDIA H100 PCIe'],
                        'SMs': [108, 132, 114],
                        'Peak FP32 TFLOPS': ['19.5', '66.9', '51.2'],
                        'Peak FP16 Tensor TFLOPS': ['312/624', '989.4/1978.9', '756/1513'],
                        'Memory Size': ['40 GB', '80 GB', '80 GB'],
                        'Memory Bandwidth': ['1555 GB/sec', '3352 GB/sec', '2039 GB/sec'],
                        'TDP': ['400 Watts', '700 Watts', '350 Watts']
                    }),
                    'source_file': 'gpu_specs.md',
                    'context': {'title': 'GPU Comparison Table'}
                },
                # System configurations table
                {
                    'dataframe': pd.DataFrame({
                        'System Configuration': ['DGX A100', 'DGX H100', 'HGX H100 4-GPU'],
                        'GPU Count': ['8x A100', '8x H100', '4x H100'],
                        'Total GPU Memory': ['320 GB', '640 GB', '320 GB'],
                        'System Power': ['6.5 kW', '10.2 kW', '5.1 kW'],
                        'Interconnect': ['NVLink', 'NVLink 4.0', 'NVLink 4.0']
                    }),
                    'source_file': 'system_configs.md',
                    'context': {'title': 'System Configurations'}
                }
            ],
            'metadata': {
                'total_files': 2,
                'parsing_errors': []
            }
        }
    
    def test_full_processing_pipeline(self):
        """Test complete processing pipeline from parsed data to final results."""
        # Process GPU data
        gpu_df = self.gpu_processor.consolidate_gpu_data(self.comprehensive_parsed_data)
        
        self.assertFalse(gpu_df.empty)
        self.assertGreaterEqual(len(gpu_df), 3)  # At least 3 GPU models (may find more from system table)
        self.assertIn('model_name', gpu_df.columns)
        
        # Process system data
        system_df = self.system_processor.process_system_configs(
            self.comprehensive_parsed_data, gpu_df
        )
        
        self.assertFalse(system_df.empty)
        self.assertIn('system_name', system_df.columns)
        self.assertIn('gpu_count', system_df.columns)
        
        # Verify aggregate calculations
        aggregate_cols = [col for col in system_df.columns if col.startswith('total_')]
        self.assertGreater(len(aggregate_cols), 0)
        
        # Check that some systems have calculated aggregates
        systems_with_aggregates = system_df[aggregate_cols].notna().any(axis=1).sum()
        self.assertGreater(systems_with_aggregates, 0)
    
    def test_dense_value_extraction_integration(self):
        """Test that dense values are properly extracted throughout the pipeline."""
        gpu_df = self.gpu_processor.consolidate_gpu_data(self.comprehensive_parsed_data)
        
        # Check that dense values were extracted from sparse/dense format
        fp16_values = gpu_df['fp16_tensor_tflops'].dropna()
        if not fp16_values.empty:
            # Should have extracted dense values (first number from "312/624" format)
            for value in fp16_values:
                if isinstance(value, str) and '/' in value:
                    # Should have been processed to extract dense value
                    self.fail("Dense value extraction failed - still contains sparse format")
    
    def test_conflict_resolution_integration(self):
        """Test conflict resolution across the processing pipeline."""
        # Create data with conflicting GPU specifications
        conflicting_data = self.comprehensive_parsed_data.copy()
        conflicting_data['consolidated_tables'].append({
            'dataframe': pd.DataFrame({
                'GPU Model': ['NVIDIA A100', 'NVIDIA H100 SXM5'],  # Duplicate models
                'SMs': [108, 132],
                'Peak FP32 TFLOPS': ['20.0', '67.0'],  # Slightly different values
                'Memory Size': ['40 GB', '80 GB']
            }),
            'source_file': 'conflicting_specs.md',
            'context': {}
        })
        
        gpu_df = self.gpu_processor.consolidate_gpu_data(conflicting_data)
        
        # Should have resolved conflicts and not have duplicate models
        model_counts = gpu_df['model_name'].value_counts()
        duplicates = model_counts[model_counts > 1]
        self.assertEqual(len(duplicates), 0, "Conflict resolution failed - duplicate models found")
    
    def test_categorization_consistency(self):
        """Test that categorization is consistent across processors."""
        gpu_df = self.gpu_processor.consolidate_gpu_data(self.comprehensive_parsed_data)
        categories = self.gpu_processor.categorize_specifications(gpu_df)
        
        # Verify all expected categories are present
        expected_categories = ['hardware_specs', 'computing_capacity', 'memory_info', 'system_info']
        for category in expected_categories:
            self.assertIn(category, categories)
        
        # Verify specifications are in appropriate categories
        if 'sms' in gpu_df.columns:
            self.assertIn('sms', categories['hardware_specs'])
        
        # Check for TFLOPS specifications in computing capacity
        tflops_specs = [col for col in gpu_df.columns if 'tflops' in col.lower()]
        for spec in tflops_specs:
            if spec in categories['computing_capacity']:
                break
        else:
            if tflops_specs:  # Only fail if there were TFLOPS specs that weren't categorized
                self.fail("TFLOPS specifications not found in computing_capacity category")


if __name__ == '__main__':
    # Set up logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    unittest.main(verbosity=2)