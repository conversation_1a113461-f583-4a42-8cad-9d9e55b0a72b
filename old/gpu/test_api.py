"""
Test suite for GPU Visualization Tool API endpoints.
Tests all RESTful API endpoints for data access and manipulation.
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
import pandas as pd

from app import create_app
from config import Config


class TestAPIEndpoints:
    """Test class for API endpoints."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        # Create temporary directory for test docs
        self.temp_dir = tempfile.mkdtemp()
        self.docs_path = Path(self.temp_dir) / 'docs'
        self.docs_path.mkdir()
        
        # Create sample markdown files
        self._create_sample_markdown_files()
        
        # Override config for testing
        Config.DOCS_PATH = self.docs_path
        Config.DATA_CACHE_TIMEOUT = 0  # Disable caching for tests
        
        app = create_app()
        app.config['TESTING'] = True
        
        yield app
        
        # Cleanup
        shutil.rmtree(self.temp_dir)
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    def _create_sample_markdown_files(self):
        """Create sample markdown files for testing."""
        # GPU specifications file
        gpu_specs = """
# GPU Specifications

| GPU Model | Architecture | Memory Size | FP32 TFLOPS | FP16 Tensor TFLOPS | TDP |
|-----------|--------------|-------------|-------------|-------------------|-----|
| NVIDIA H100 | Hopper | 80 GB | 67 | 1979 | 700W |
| NVIDIA A100 | Ampere | 40 GB | 19.5 | 312 | 400W |
| NVIDIA V100 | Volta | 32 GB | 15.7 | 125 | 300W |
"""
        
        with open(self.docs_path / 'gpu_specs.md', 'w') as f:
            f.write(gpu_specs)
        
        # System configurations file
        system_specs = """
# System Configurations

| System | GPU Count | GPU Model | Total Memory | Interconnect |
|--------|-----------|-----------|--------------|--------------|
| DGX H100 | 8 | NVIDIA H100 | 640 GB | NVLink |
| DGX A100 | 8 | NVIDIA A100 | 320 GB | NVLink |
"""
        
        with open(self.docs_path / 'system_specs.md', 'w') as f:
            f.write(system_specs)
    
    def test_get_gpus_endpoint(self, client):
        """Test GET /api/gpus endpoint."""
        response = client.get('/api/gpus')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['success'] is True
        assert 'data' in data
        assert 'total_count' in data
        assert 'categories' in data
        assert isinstance(data['data'], list)
        
        # Should have some GPU data from sample files
        if data['total_count'] > 0:
            gpu_record = data['data'][0]
            assert 'model_name' in gpu_record
    
    def test_get_systems_endpoint(self, client):
        """Test GET /api/systems endpoint."""
        response = client.get('/api/systems')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['success'] is True
        assert 'data' in data
        assert 'total_count' in data
        assert 'summary' in data
        assert isinstance(data['data'], list)
    
    def test_filter_gpus_endpoint(self, client):
        """Test POST /api/gpus/filter endpoint."""
        # Test with empty filter
        response = client.post('/api/gpus/filter', 
                             json={},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['success'] is True
        assert 'data' in data
        assert 'filters_applied' in data
        assert data['filters_applied'] == {}
        
        # Test with model name filter
        filter_criteria = {'model_name': 'H100'}
        response = client.post('/api/gpus/filter',
                             json=filter_criteria,
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['success'] is True
        assert data['filters_applied'] == filter_criteria
        
        # Test with numerical range filter
        numerical_filter = {
            'min_memory': 30,
            'max_memory': 100,
            'min_fp32_tflops': 10
        }
        response = client.post('/api/gpus/filter',
                             json=numerical_filter,
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
    
    def test_refresh_data_endpoint(self, client):
        """Test POST /api/refresh endpoint."""
        response = client.post('/api/refresh')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['success'] is True
        assert 'message' in data
        assert 'gpu_count' in data
        assert 'system_count' in data
        assert 'last_refresh' in data
        assert 'parsing_summary' in data
    
    def test_export_data_endpoint(self, client):
        """Test GET /api/export/{format} endpoint."""
        # Test valid formats
        for format_type in ['csv', 'json', 'markdown']:
            response = client.get(f'/api/export/{format_type}')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert data['format'] == format_type
        
        # Test invalid format
        response = client.get('/api/export/invalid')
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Invalid export format' in data['error']
    
    def test_api_error_handling(self, client):
        """Test API error handling."""
        # Test 404 for non-existent endpoint
        response = client.get('/api/nonexistent')
        assert response.status_code == 404
        
        # Flask returns HTML for 404 by default, not JSON
        # This is expected behavior
        
        # Test malformed JSON in filter endpoint
        response = client.post('/api/gpus/filter',
                             data='invalid json',
                             content_type='application/json')
        
        # Should handle gracefully (might return 400 or process as empty filter)
        assert response.status_code in [200, 400]
    
    def test_filter_functionality(self, client):
        """Test detailed filtering functionality."""
        # First get all data to understand what we're working with
        response = client.get('/api/gpus')
        all_data = json.loads(response.data)
        
        if all_data['total_count'] == 0:
            pytest.skip("No GPU data available for filtering tests")
        
        original_count = all_data['total_count']
        
        # Test architecture filter
        arch_filter = {'architecture': 'Hopper'}
        response = client.post('/api/gpus/filter',
                             json=arch_filter,
                             content_type='application/json')
        
        filtered_data = json.loads(response.data)
        assert filtered_data['success'] is True
        assert filtered_data['original_count'] == original_count
        
        # Test memory range filter
        memory_filter = {'min_memory': 50}
        response = client.post('/api/gpus/filter',
                             json=memory_filter,
                             content_type='application/json')
        
        filtered_data = json.loads(response.data)
        assert filtered_data['success'] is True
        
        # Test combined filters
        combined_filter = {
            'model_name': 'NVIDIA',
            'min_memory': 30,
            'max_tdp': 500
        }
        response = client.post('/api/gpus/filter',
                             json=combined_filter,
                             content_type='application/json')
        
        filtered_data = json.loads(response.data)
        assert filtered_data['success'] is True
    
    @patch('web_interface.api._load_data')
    def test_data_loading_error_handling(self, mock_load_data, client):
        """Test error handling when data loading fails."""
        # Mock data loading to raise an exception
        mock_load_data.side_effect = Exception("Data loading failed")
        
        response = client.post('/api/refresh')
        assert response.status_code == 500
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Failed to refresh data' in data['error']
    
    def test_empty_data_handling(self, client):
        """Test handling of empty data scenarios."""
        # Create empty docs directory
        empty_docs_path = Path(self.temp_dir) / 'empty_docs'
        empty_docs_path.mkdir()
        
        Config.DOCS_PATH = empty_docs_path
        
        # Clear the cache to force reload from empty directory
        from web_interface.api import _data_cache
        _data_cache['gpu_data'] = None
        _data_cache['system_data'] = None
        _data_cache['last_refresh'] = None
        
        # Test endpoints with no data
        response = client.get('/api/gpus')
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['total_count'] == 0
        assert data['data'] == []
        
        response = client.get('/api/systems')
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['total_count'] == 0
        
        response = client.post('/api/gpus/filter', json={'model_name': 'test'})
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['total_count'] == 0


class TestAPIDataProcessing:
    """Test data processing functionality in API."""
    
    def test_gpu_data_serialization(self):
        """Test GPU data serialization for JSON response."""
        from web_interface.api import _data_cache
        import datetime
        
        # Create sample DataFrame with various data types
        sample_data = pd.DataFrame({
            'model_name': ['Test GPU'],
            'memory_size_gb': [80.0],
            'fp32_tflops': [67.5],
            'tdp_watts': [700],
            'architecture': ['Test Arch'],
            'timestamp': [pd.Timestamp.now()],
            'null_value': [None]
        })
        
        # Test conversion to JSON-serializable format
        records = []
        for _, row in sample_data.iterrows():
            record = {}
            for col in sample_data.columns:
                value = row[col]
                if pd.isna(value):
                    record[col] = None
                elif isinstance(value, (pd.Timestamp, pd.Timedelta)):
                    record[col] = str(value)
                else:
                    record[col] = value
            records.append(record)
        
        # Verify serialization
        assert len(records) == 1
        assert records[0]['model_name'] == 'Test GPU'
        assert records[0]['null_value'] is None
        assert isinstance(records[0]['timestamp'], str)
    
    def test_filter_application(self):
        """Test filter application logic."""
        from web_interface.api import _apply_gpu_filters
        
        # Create sample GPU data
        gpu_data = pd.DataFrame({
            'model_name': ['NVIDIA H100', 'NVIDIA A100', 'AMD MI250'],
            'architecture': ['Hopper', 'Ampere', 'CDNA2'],
            'memory_size_gb': [80.0, 40.0, 128.0],
            'fp32_tflops': [67.0, 19.5, 47.9],
            'tdp_watts': [700, 400, 560]
        })
        
        # Test model name filter
        filtered = _apply_gpu_filters(gpu_data, {'model_name': 'NVIDIA'})
        assert len(filtered) == 2
        assert all('NVIDIA' in name for name in filtered['model_name'])
        
        # Test numerical range filter
        filtered = _apply_gpu_filters(gpu_data, {'min_memory': 50})
        assert len(filtered) == 2  # H100 and MI250
        
        # Test combined filters
        filtered = _apply_gpu_filters(gpu_data, {
            'model_name': 'NVIDIA',
            'min_memory': 50
        })
        assert len(filtered) == 1  # Only H100
        assert filtered.iloc[0]['model_name'] == 'NVIDIA H100'
        
        # Test empty filters
        filtered = _apply_gpu_filters(gpu_data, {})
        assert len(filtered) == len(gpu_data)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])