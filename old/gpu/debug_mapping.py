#!/usr/bin/env python3

"""
Debug script to see the exact row-to-value mapping.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    # Look at the first table (nvidia-gpu-comparison-table.md)
    table = parsed_data['consolidated_tables'][0]
    original_data = table['original_data']
    
    print("=== Original Table Data ===")
    print(f"Headers: {original_data['headers']}")
    print(f"Number of data rows: {len(original_data['data'])}")
    
    # Show first 10 rows with their mappings
    print("\n=== First 10 Rows ===")
    for i, row in enumerate(original_data['data'][:10]):
        spec_name = row[0] if len(row) > 0 else ''
        a100_value = row[1] if len(row) > 1 else ''
        h100_sxm5_value = row[2] if len(row) > 2 else ''
        
        standardized = processor._standardize_spec_name(spec_name)
        print(f"{i}: '{spec_name}' -> {standardized}")
        print(f"   A100: '{a100_value}', H100 SXM5: '{h100_sxm5_value}'")
    
    # Look for specific specifications
    print("\n=== Looking for Memory Size ===")
    for i, row in enumerate(original_data['data']):
        spec_name = row[0] if len(row) > 0 else ''
        if 'memory size' in spec_name.lower():
            print(f"Found at row {i}: '{spec_name}'")
            print(f"Values: {row[1:4]}")
            standardized = processor._standardize_spec_name(spec_name)
            print(f"Standardized: {standardized}")

if __name__ == "__main__":
    main()