"""
GPU data processor for categorizing and consolidating GPU specifications.
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path

from models import GPUSpecification
from data_parser import MarkdownParser


class GPUDataProcessor:
    """Processes and consolidates individual GPU specification data."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.parser = MarkdownParser()
        
        # Define specification mappings for standardization
        self.spec_mappings = {
            # Hardware specifications
            'sms': ['sms'],
            'fp32_cores_per_sm': ['fp32 cores / sm', 'fp32 cores per sm'],
            'fp64_cores_per_sm': ['fp64 cores / sm', 'fp64 cores per sm', 'fp64 cores / sm (excl. tensor)'],
            'tensor_cores_per_sm': ['tensor cores / sm', 'tensor cores per sm'],
            'tensor_cores_total': ['tensor cores / gpu', 'tensor cores total', 'tensor cores'],
            
            # Computing capacity (prioritize dense values)
            'fp4_tensor_tflops': ['peak fp4 tensor tflops', 'fp4 tensor tflops'],
            'fp8_tensor_tflops': ['peak fp8 tensor tflops', 'fp8 tensor tflops'],
            'fp16_tensor_tflops': ['peak fp16 tensor tflops', 'fp16 tensor tflops'],
            'fp32_tflops': ['peak fp32 tflops', 'fp32 tflops', 'peak fp32 tflops (non-tensor)'],
            'fp64_tflops': ['peak fp64 tflops', 'fp64 tflops', 'peak fp64 tensor tflops'],
            
            # Memory information
            'memory_bandwidth_gbps': ['memory bandwidth', 'bandwidth'],
            'memory_type': ['memory interface', 'memory type'],
            'l2_cache_mb': ['l2 cache size', 'l2 cache'],
            
            # System information
            'tdp_watts': ['tdp', 'thermal design power', 'power'],
            'interconnect': ['interconnect', 'nvlink'],
            'manufacturing_process': ['manufacturing process', 'process'],
            'architecture': ['gpu architecture', 'architecture']
        }
        
        # Category groupings for organization
        self.category_groups = {
            'hardware_specs': [
                'sms', 'fp32_cores_per_sm', 'fp64_cores_per_sm', 
                'tensor_cores_per_sm', 'tensor_cores_total'
            ],
            'computing_capacity': [
                'fp4_tensor_tflops', 'fp8_tensor_tflops', 'fp16_tensor_tflops',
                'fp32_tflops', 'fp64_tflops'
            ],
            'memory_info': [
                'memory_size_gb', 'memory_bandwidth_gbps', 'memory_type', 'l2_cache_mb'
            ],
            'system_info': [
                'tdp_watts', 'interconnect', 'manufacturing_process', 'architecture'
            ]
        }
    
    def consolidate_gpu_data(self, parsed_data: Dict[str, Any]) -> pd.DataFrame:
        """
        Consolidate GPU data from multiple parsed markdown files.
        
        Args:
            parsed_data: Output from MarkdownParser.parse_directory()
            
        Returns:
            Consolidated DataFrame with standardized GPU specifications
        """
        self.logger.info("Starting GPU data consolidation")
        
        # Extract all GPU specifications from parsed data
        gpu_records = []
        
        for table in parsed_data.get('consolidated_tables', []):
            df = table.get('dataframe')
            if df is None or df.empty:
                continue
            
            # Process each table based on its structure
            table_records = self._extract_gpu_records_from_table(df, table)
            gpu_records.extend(table_records)
        
        if not gpu_records:
            self.logger.warning("No GPU records found in parsed data")
            return pd.DataFrame()
        
        # Create consolidated DataFrame
        consolidated_df = pd.DataFrame(gpu_records)
        
        # Handle duplicates and conflicts
        consolidated_df = self._resolve_conflicts(consolidated_df)
        
        # Normalize units and categorize
        consolidated_df = self._normalize_and_categorize(consolidated_df)
        
        self.logger.info(f"Consolidated {len(consolidated_df)} GPU records")
        return consolidated_df
    
    def _extract_gpu_records_from_table(self, df: pd.DataFrame, table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract GPU records from a single table."""
        records = []
        
        if df.empty or len(df.columns) < 2:
            return records
        
        # Determine table structure
        first_col = df.columns[0]
        
        # Check different table structures
        if self._contains_gpu_models_in_first_column(df):
            # Table structure: GPU models as rows, specs as columns  
            records = self._extract_from_model_rows(df, table_info)
        elif self._has_gpu_models_in_headers(df):
            # Table structure: GPU models as columns, specs as rows
            records = self._extract_from_model_columns(df, table_info)
        else:
            # Try to detect GPU models in any column
            records = self._extract_from_mixed_structure(df, table_info)
        
        return records
    
    def _is_gpu_model_column(self, column_data: pd.Series) -> bool:
        """Check if a column contains GPU model names."""
        # Look for GPU model indicators in column headers (not data)
        return False  # This checks data, not headers
    
    def _contains_gpu_models_in_first_column(self, df: pd.DataFrame) -> bool:
        """Check if first column contains actual GPU model names (not just GPU-related terms)."""
        first_col_data = df.iloc[:, 0].dropna().astype(str)
        
        # Look for actual GPU model patterns, not just GPU-related terms
        gpu_model_patterns = [
            r'nvidia\s+[a-z]+[0-9]+',  # "NVIDIA A100", "NVIDIA H100"
            r'^[a-z]+[0-9]+$',         # "A100", "H100", "RTX4090" (whole string)
            r'geforce',
            r'quadro',
            r'tesla',
            r'rtx\s*[0-9]+',           # "RTX 4090", "RTX4090"
            r'gtx\s*[0-9]+',           # "GTX 1080"
            r'radeon',
            r'rx\s*[0-9]+',            # "RX 6800"
        ]
        
        import re
        gpu_model_count = 0
        for value in first_col_data.head(10):  # Check first 10 values
            value_lower = value.lower()
            # Check if this looks like an actual GPU model name
            if any(re.search(pattern, value_lower) for pattern in gpu_model_patterns):
                gpu_model_count += 1
        
        return gpu_model_count >= 2  # At least 2 actual GPU model names
    
    def _has_gpu_models_in_headers(self, df: pd.DataFrame) -> bool:
        """Check if column headers contain GPU model names."""
        gpu_count = 0
        for col in df.columns[1:]:  # Skip first column
            col_lower = col.lower()
            # More comprehensive GPU model detection
            gpu_indicators = [
                'nvidia', 'amd', 'intel', 'a100', 'h100', 'v100', 'rtx', 'gtx',
                'hgx', 'b200', 'b300', 'gb200', 'gb300', 'nvl72', 'sxm', 'pcie',
                'blackwell', 'hopper', 'ampere', 'ada', 'lovelace'
            ]
            if any(indicator in col_lower for indicator in gpu_indicators):
                gpu_count += 1
        
        return gpu_count >= 1  # At least 1 GPU model in headers
    
    def _extract_from_model_columns(self, df: pd.DataFrame, table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract records when GPU models are column headers."""
        records = []
        
        # Get the original table data to avoid over-normalization
        original_data = table_info.get('original_data', {})
        if original_data and 'headers' in original_data and 'data' in original_data:
            return self._extract_from_original_table_data(original_data, table_info)
        
        # Fallback to DataFrame extraction
        # Skip first column (specification names), process GPU model columns
        for col in df.columns[1:]:
            col_lower = col.lower()
            # More comprehensive GPU model detection
            gpu_indicators = [
                'nvidia', 'amd', 'intel', 'a100', 'h100', 'v100', 'rtx', 'gtx',
                'hgx', 'b200', 'b300', 'gb200', 'gb300', 'nvl72', 'sxm', 'pcie',
                'blackwell', 'hopper', 'ampere', 'ada', 'lovelace'
            ]
            
            if not any(indicator in col_lower for indicator in gpu_indicators):
                continue
            
            record = {
                'model_name': col,
                'source_file': table_info.get('source_file', 'unknown'),
                'table_context': table_info.get('context', '')
            }
            
            # Map specifications from first column to values in this column
            for idx, spec_name in enumerate(df.iloc[:, 0]):
                if pd.isna(spec_name) or idx >= len(df):
                    continue
                
                value = df.iloc[idx, df.columns.get_loc(col)]
                if pd.notna(value) and str(value).strip():
                    standardized_key = self._standardize_spec_name(str(spec_name))
                    if standardized_key:
                        # Clean and process the value
                        processed_value = self._process_spec_value(str(value), standardized_key)
                        if processed_value is not None:
                            record[standardized_key] = processed_value
            
            if len(record) > 3:  # Must have some specifications beyond metadata
                records.append(record)
        
        return records
    
    def _extract_from_original_table_data(self, original_data: Dict[str, Any], table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract records from original table data to preserve text values."""
        records = []
        
        headers = original_data['headers']
        data_rows = original_data['data']
        
        if not headers or not data_rows:
            return records
        
        # Identify GPU model columns (skip first column which is specifications)
        gpu_model_columns = []
        for i, header in enumerate(headers[1:], 1):  # Start from index 1
            header_lower = header.lower()
            gpu_indicators = [
                'nvidia', 'amd', 'intel', 'a100', 'h100', 'v100', 'rtx', 'gtx',
                'hgx', 'b200', 'b300', 'gb200', 'gb300', 'nvl72', 'sxm', 'pcie',
                'blackwell', 'hopper', 'ampere', 'ada', 'lovelace'
            ]
            
            if any(indicator in header_lower for indicator in gpu_indicators):
                gpu_model_columns.append((i, header))
        
        # Extract records for each GPU model
        for col_idx, model_name in gpu_model_columns:
            record = {
                'model_name': model_name,
                'source_file': table_info.get('source_file', 'unknown'),
                'table_context': table_info.get('context', '')
            }
            
            # Process each specification row
            for row in data_rows:
                if len(row) <= col_idx:
                    continue
                
                spec_name = row[0] if len(row) > 0 else ''
                value = row[col_idx] if len(row) > col_idx else ''
                
                if not spec_name or not value or str(value).strip() == '':
                    continue
                
                standardized_key = self._standardize_spec_name(str(spec_name))
                if standardized_key:
                    # Process the value appropriately
                    processed_value = self._process_spec_value_from_original(str(value), standardized_key)
                    if processed_value is not None:
                        record[standardized_key] = processed_value
            
            if len(record) > 3:  # Must have some specifications beyond metadata
                records.append(record)
        
        return records
    
    def _extract_from_model_rows(self, df: pd.DataFrame, table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract records when GPU models are in rows."""
        records = []
        
        # Process each row as a potential GPU model
        for idx, row in df.iterrows():
            first_value = row.iloc[0]
            if not self._looks_like_gpu_model(str(first_value)):
                continue
            
            record = {
                'model_name': str(first_value),
                'source_file': table_info.get('source_file', 'unknown'),
                'table_context': table_info.get('context', {})
            }
            
            # Map column headers to values in this row
            for col_idx, col_name in enumerate(df.columns[1:], 1):
                value = row.iloc[col_idx]
                if pd.notna(value):
                    standardized_key = self._standardize_spec_name(str(col_name))
                    if standardized_key:
                        record[standardized_key] = value
            
            records.append(record)
        
        return records
    
    def _extract_from_mixed_structure(self, df: pd.DataFrame, table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract records from tables with mixed or unclear structure."""
        records = []
        
        # Try to find GPU models anywhere in the table
        for col in df.columns:
            for idx, value in enumerate(df[col]):
                if pd.isna(value):
                    continue
                
                if self._looks_like_gpu_model(str(value)):
                    # Found a GPU model, try to extract its specifications
                    record = self._extract_specs_for_model(df, col, idx, str(value), table_info)
                    if record:
                        records.append(record)
        
        return records
    
    def _extract_specs_for_model(self, df: pd.DataFrame, model_col: str, model_row: int, 
                                model_name: str, table_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract specifications for a specific GPU model found in the table."""
        record = {
            'model_name': model_name,
            'source_file': table_info.get('source_file', 'unknown'),
            'table_context': table_info.get('context', {})
        }
        
        # Strategy 1: If model is in a column header, get values from that column
        if model_col in df.columns and model_row == 0:
            col_idx = df.columns.get_loc(model_col)
            for row_idx, spec_name in enumerate(df.iloc[:, 0]):
                if pd.isna(spec_name) or row_idx >= len(df):
                    continue
                value = df.iloc[row_idx, col_idx]
                if pd.notna(value):
                    standardized_key = self._standardize_spec_name(str(spec_name))
                    if standardized_key:
                        record[standardized_key] = value
        
        # Strategy 2: If model is in a row, get values from that row
        else:
            for col_idx, col_name in enumerate(df.columns):
                if col_idx == df.columns.get_loc(model_col):
                    continue  # Skip the column with the model name
                value = df.iloc[model_row, col_idx]
                if pd.notna(value):
                    standardized_key = self._standardize_spec_name(str(col_name))
                    if standardized_key:
                        record[standardized_key] = value
        
        # Only return record if it has some specifications
        return record if len(record) > 3 else None  # More than just basic metadata
    
    def _looks_like_gpu_model(self, text: str) -> bool:
        """Check if text looks like a GPU model name."""
        text_lower = text.lower().strip()
        
        # GPU model indicators
        gpu_indicators = [
            'nvidia', 'amd', 'intel', 'gpu',
            'h100', 'a100', 'v100', 'h200', 'a40', 'a30',
            'rtx', 'gtx', 'quadro', 'tesla', 'titan',
            'radeon', 'rx', 'vega', 'navi',
            'arc', 'xe', 'iris'
        ]
        
        # Check for GPU indicators
        has_gpu_indicator = any(indicator in text_lower for indicator in gpu_indicators)
        
        # Check for model-like patterns (letters + numbers)
        import re
        has_model_pattern = bool(re.search(r'[a-z]+\d+|[a-z]+\s+\d+', text_lower))
        
        # Exclude common specification names
        spec_exclusions = [
            'cores', 'memory', 'cache', 'bandwidth', 'tflops', 'ops',
            'mhz', 'ghz', 'gb', 'tb', 'watts', 'nm', 'process'
        ]
        has_spec_exclusion = any(exclusion in text_lower for exclusion in spec_exclusions)
        
        return (has_gpu_indicator or has_model_pattern) and not has_spec_exclusion
    
    def _standardize_spec_name(self, spec_name: str) -> Optional[str]:
        """Standardize specification name to match our data model."""
        if not spec_name or pd.isna(spec_name):
            return None
            
        spec_lower = spec_name.lower().strip()
        
        # Direct mapping lookup - check for exact matches first
        for standard_key, variations in self.spec_mappings.items():
            for variation in variations:
                if variation == spec_lower:
                    return standard_key
        
        # Then check for partial matches
        for standard_key, variations in self.spec_mappings.items():
            for variation in variations:
                if variation in spec_lower:
                    return standard_key
        
        # Additional comprehensive mappings for GPU specifications
        # Order matters - more specific patterns should come first
        additional_mappings = {
            # More specific memory-related mappings first
            'shared_memory_per_sm': ['shared memory size / sm'],
            'register_file_per_sm': ['register file size / sm'],
            'register_file_per_gpu': ['register file size / gpu'],
            'memory_bandwidth_gbps': ['memory bandwidth', 'aggregate memory bandwidth'],
            'memory_data_rate': ['memory data rate'],
            'memory_type': ['memory interface'],
            'memory_size_gb': ['memory size'],  # This should come after more specific memory patterns
            
            # Core specifications
            'fp32_cores_gpu': ['fp32 cores / gpu'],
            'fp64_cores_gpu': ['fp64 cores / gpu (excl. tensor)'],
            'int32_cores_gpu': ['int32 cores / gpu'],
            'tensor_cores_gpu': ['tensor cores / gpu'],
            
            # Performance specifications
            'fp32_tflops': ['peak fp32 tflops (non-tensor)'],
            'fp64_tflops': ['peak fp64 tflops (non-tensor)'],
            'fp16_tensor_tflops': ['peak fp16 tensor tflops with fp32 accumulate'],
            'fp8_tensor_tflops': ['peak fp8 tensor tflops with fp16 accumulate'],
            'tf32_tensor_tflops': ['peak tf32 tensor tflops'],
            'int8_tensor_tops': ['peak int8 tensor tops'],
            
            # Other specifications
            'manufacturing_process': ['tsmc manufacturing process'],
            'gpu_boost_clock': ['gpu boost clock'],
            'l2_cache_mb': ['l2 cache size'],
            'architecture': ['gpu architecture'],
            'form_factor': ['gpu board form factor'],
            'transistors': ['transistors'],
            'die_size': ['gpu die size'],
            'texture_units': ['texture units'],
            'tdp_watts': ['tdp']
        }
        
        # Check for exact matches first
        for standard_key, variations in additional_mappings.items():
            for variation in variations:
                if variation == spec_lower:
                    return standard_key
        
        # Then check for partial matches, but prioritize longer patterns
        # Create a list of all (key, variation) pairs sorted by variation length (longest first)
        all_variations = []
        for standard_key, variations in additional_mappings.items():
            for variation in variations:
                all_variations.append((standard_key, variation))
        
        # Sort by variation length (longest first) to prioritize more specific matches
        all_variations.sort(key=lambda x: len(x[1]), reverse=True)
        
        for standard_key, variation in all_variations:
            if variation in spec_lower:
                return standard_key
        
        # Fuzzy matching for common patterns
        if 'tensor' in spec_lower and ('tflops' in spec_lower or 'petaflops' in spec_lower):
            if 'fp8' in spec_lower or 'fp6' in spec_lower:
                return 'fp8_tensor_tflops'
            elif 'fp16' in spec_lower or 'bf16' in spec_lower:
                return 'fp16_tensor_tflops'
            elif 'fp4' in spec_lower:
                return 'fp4_tensor_tflops'
            elif 'tf32' in spec_lower:
                return 'tf32_tensor_tflops'
            elif 'fp64' in spec_lower:
                return 'fp64_tflops'
        
        if ('tflops' in spec_lower or 'teraflops' in spec_lower) and 'non-tensor' in spec_lower:
            if 'fp32' in spec_lower:
                return 'fp32_tflops'
            elif 'fp64' in spec_lower:
                return 'fp64_tflops'
        
        # Handle dense/sparse specifications
        if 'dense' in spec_lower or 'sparse' in spec_lower:
            if 'fp4' in spec_lower:
                return 'fp4_tensor_tflops'
            elif 'fp8' in spec_lower:
                return 'fp8_tensor_tflops'
            elif 'fp16' in spec_lower:
                return 'fp16_tensor_tflops'
            elif 'tf32' in spec_lower:
                return 'tf32_tensor_tflops'
            elif 'int8' in spec_lower:
                return 'int8_tensor_tops'
        
        return None
    
    def _process_spec_value(self, value: str, spec_key: str) -> Any:
        """Process and clean specification values."""
        if not value or pd.isna(value):
            return None
        
        value = str(value).strip()
        
        # Handle "NA" or "N/A" values
        if value.upper() in ['NA', 'N/A', 'NOT AVAILABLE', '-']:
            return None
        
        # Extract numeric values for performance specifications
        if any(keyword in spec_key for keyword in ['tflops', 'tops', 'ghz', 'mhz', 'gb', 'tb', 'watts', 'mb']):
            return self._extract_numeric_value(value, spec_key)
        
        # For non-numeric specifications, return cleaned string
        return value
    
    def _extract_numeric_value(self, value: str, spec_key: str) -> Optional[float]:
        """Extract numeric values from specification strings."""
        import re
        
        # Handle ranges (e.g., "312/624¹") - take the first value for dense performance
        if '/' in value:
            value = value.split('/')[0]
        
        # Handle values with units (e.g., "40 GB", "1555 GB/sec")
        # Extract just the numeric part
        numeric_match = re.search(r'[\d,]+\.?\d*', value)
        if not numeric_match:
            return None
        
        cleaned = numeric_match.group()
        
        try:
            # Handle comma as thousands separator
            if ',' in cleaned and '.' in cleaned:
                # Format like "1,555.5"
                cleaned = cleaned.replace(',', '')
            elif ',' in cleaned and cleaned.count(',') == 1 and len(cleaned.split(',')[1]) == 3:
                # Format like "1,555" (thousands separator)
                cleaned = cleaned.replace(',', '')
            
            numeric_value = float(cleaned)
            
            # Convert units based on specification type and value content
            value_lower = value.lower()
            
            if 'petaflops' in spec_key.lower() or 'petaops' in spec_key.lower():
                # Convert petaFLOPS to teraFLOPS for consistency
                numeric_value *= 1000
            elif 'kb' in value_lower and ('memory' in spec_key or 'cache' in spec_key):
                # Convert KB to MB for memory/cache specs
                numeric_value /= 1024
            elif 'gb/sec' in value_lower or 'gb/s' in value_lower:
                # Memory bandwidth in GB/s - keep as is
                pass
            elif 'tb/s' in value_lower:
                # Convert TB/s to GB/s
                numeric_value *= 1000
            elif 'mhz' in value_lower:
                # Clock speeds in MHz - keep as is
                pass
            elif 'ghz' in value_lower:
                # Convert GHz to MHz
                numeric_value *= 1000
            
            return numeric_value
            
        except (ValueError, TypeError):
            return None
    
    def _process_spec_value_from_original(self, value: str, spec_key: str) -> Any:
        """Process specification values from original table data, preserving text when appropriate."""
        if not value or pd.isna(value):
            return None
        
        value = str(value).strip()
        
        # Handle "NA" or "N/A" values
        if value.upper() in ['NA', 'N/A', 'NOT AVAILABLE', '-', '']:
            return None
        
        # For text specifications, preserve as strings
        text_specs = ['architecture', 'form_factor', 'memory_type', 'interconnect', 'manufacturing_process']
        if any(text_spec in spec_key for text_spec in text_specs):
            return value
        
        # For numeric specifications, extract numeric values
        if any(keyword in spec_key for keyword in ['tflops', 'tops', 'ghz', 'mhz', 'gb', 'tb', 'watts', 'mb', 'cores', 'sms']):
            numeric_value = self._extract_numeric_value(value, spec_key)
            return numeric_value if numeric_value is not None else value
        
        # For other specifications, try numeric first, then fall back to string
        numeric_value = self._extract_numeric_value(value, spec_key)
        return numeric_value if numeric_value is not None else value
    
    def _resolve_conflicts(self, df: pd.DataFrame) -> pd.DataFrame:
        """Resolve conflicts when the same GPU model appears multiple times."""
        if df.empty or 'model_name' not in df.columns:
            return df
        
        # Group by model name and resolve conflicts
        resolved_records = []
        
        for model_name, group in df.groupby('model_name'):
            if len(group) == 1:
                resolved_records.append(group.iloc[0].to_dict())
            else:
                # Multiple entries for same model - merge them
                resolved_record = self._merge_conflicting_records(group, model_name)
                resolved_records.append(resolved_record)
        
        return pd.DataFrame(resolved_records)
    
    def _merge_conflicting_records(self, group: pd.DataFrame, model_name: str) -> Dict[str, Any]:
        """Merge multiple records for the same GPU model."""
        merged_record = {'model_name': model_name}
        
        # Collect all source files
        source_files = group['source_file'].dropna().unique().tolist()
        merged_record['source_files'] = source_files
        merged_record['source_file'] = source_files[0] if source_files else 'unknown'
        
        # For each specification, choose the best value
        all_columns = set()
        for _, record in group.iterrows():
            all_columns.update(record.index)
        
        for col in all_columns:
            if col in ['model_name', 'source_file', 'source_files', 'table_context']:
                continue
            
            values = group[col].dropna().tolist()
            if not values:
                continue
            
            # Choose the best value based on priority rules
            best_value = self._choose_best_value(values, col)
            if best_value is not None:
                merged_record[col] = best_value
        
        self.logger.info(f"Merged {len(group)} conflicting records for {model_name}")
        return merged_record
    
    def _choose_best_value(self, values: List[Any], spec_name: str) -> Any:
        """Choose the best value from conflicting values for a specification."""
        if not values:
            return None
        
        if len(values) == 1:
            return values[0]
        
        # For numerical specifications, prefer dense values over sparse
        if spec_name in ['fp8_tensor_tflops', 'fp16_tensor_tflops', 'fp32_tflops', 'fp64_tflops']:
            dense_values = []
            for value in values:
                dense_val = self.parser.extract_dense_values(str(value))
                if dense_val is not None:
                    dense_values.append((dense_val, value))
            
            if dense_values:
                # Return the original value with the highest dense value
                return max(dense_values, key=lambda x: x[0])[1]
        
        # For text specifications, prefer non-empty, more specific values
        if isinstance(values[0], str):
            non_empty = [v for v in values if v and str(v).strip()]
            if non_empty:
                # Prefer longer, more descriptive values
                return max(non_empty, key=len)
        
        # Default: return first non-null value
        return values[0]
    
    def _normalize_and_categorize(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize units and add category information."""
        if df.empty:
            return df
        
        normalized_df = df.copy()
        
        # Add category information for each specification
        for col in df.columns:
            if col in ['model_name', 'source_file', 'source_files', 'table_context']:
                continue
            
            # Determine category
            category = self._get_spec_category(col)
            normalized_df[f'{col}_category'] = category
            
            # Normalize numerical values and extract dense values
            if col in self.spec_mappings:
                normalized_values = []
                for value in df[col]:
                    if pd.isna(value):
                        normalized_values.append(None)
                    else:
                        # Extract dense value and normalize units
                        dense_val = self.parser.extract_dense_values(str(value))
                        normalized_values.append(dense_val)
                
                # Replace original column with normalized values for dense extraction
                normalized_df[col] = normalized_values
                normalized_df[f'{col}_normalized'] = normalized_values
        
        return normalized_df
    
    def _get_spec_category(self, spec_name: str) -> str:
        """Get the category for a specification."""
        for category, specs in self.category_groups.items():
            if spec_name in specs:
                return category
        return 'other'
    
    def categorize_specifications(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """
        Categorize specifications into hardware, computing, memory, and system groups.
        
        Args:
            df: DataFrame with GPU specifications
            
        Returns:
            Dictionary organized by categories
        """
        if df.empty:
            return {}
        
        categorized = {
            'hardware_specs': {},
            'computing_capacity': {},
            'memory_info': {},
            'system_info': {},
            'other': {}
        }
        
        # Group specifications by category
        for col in df.columns:
            if col.endswith('_category') or col.endswith('_normalized'):
                continue
            if col in ['model_name', 'source_file', 'source_files', 'table_context']:
                continue
            
            category = self._get_spec_category(col)
            
            # Get sample values for this specification
            sample_values = df[col].dropna().head(5).tolist()
            
            categorized[category][col] = {
                'sample_values': sample_values,
                'total_values': df[col].notna().sum(),
                'unique_values': df[col].nunique()
            }
        
        return categorized
    
    def get_processing_summary(self, original_data: Dict[str, Any], 
                             consolidated_df: pd.DataFrame) -> Dict[str, Any]:
        """Get a summary of the data processing results."""
        return {
            'input_files': len(original_data.get('files', {})),
            'input_tables': len(original_data.get('consolidated_tables', [])),
            'output_gpu_models': len(consolidated_df) if not consolidated_df.empty else 0,
            'specifications_found': len([col for col in consolidated_df.columns 
                                       if not col.endswith(('_category', '_normalized')) 
                                       and col not in ['model_name', 'source_file', 'source_files', 'table_context']]),
            'categories': self.categorize_specifications(consolidated_df),
            'parsing_errors': original_data.get('metadata', {}).get('parsing_errors', [])
        }