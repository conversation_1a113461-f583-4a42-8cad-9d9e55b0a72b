"""
System data processor for handling multi-GPU system configurations.
"""

import pandas as pd
from typing import Dict, List, Any, Optional
import logging
import re

from models import SystemConfiguration, GPUSpecification


class SystemDataProcessor:
    """Processes system-level configurations with multiple GPUs."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # System-level specification mappings
        self.system_spec_mappings = {
            'system_name': ['system', 'configuration', 'platform', 'server'],
            'gpu_count': ['gpus', 'gpu count', 'number of gpus', 'gpu quantity'],
            'gpu_model': ['gpu model', 'gpu type', 'accelerator'],
            'cpu_count': ['cpus', 'cpu count', 'processors', 'cpu sockets'],
            'cpu_model': ['cpu model', 'cpu type', 'processor'],
            'interconnect_bandwidth_tbps': ['interconnect bandwidth', 'nvlink bandwidth', 'fabric bandwidth'],
            'total_tdp_kw': ['total power', 'system power', 'power consumption', 'tdp'],
            'rack_configuration': ['rack', 'form factor', 'chassis']
        }
        
        # Patterns for detecting system configurations
        self.system_indicators = [
            r'nvl\d+', r'dgx', r'hgx', r'server', r'system', r'rack',
            r'node', r'cluster', r'supercomputer', r'platform'
        ]
        self.system_pattern = re.compile('|'.join(self.system_indicators), re.IGNORECASE)
        
        # GPU count extraction patterns
        self.gpu_count_patterns = [
            r'(\d+)x?\s*gpu',
            r'(\d+)\s*x\s*\w+',  # More flexible pattern for "8x nvidia"
            r'(\d+)\s*gpus?',
            r'(\d+)\s*accelerators?',
            r'(\d+)x',  # Simple pattern for "8x"
        ]
    
    def process_system_configs(self, parsed_data: Dict[str, Any], 
                             gpu_data: pd.DataFrame) -> pd.DataFrame:
        """
        Process system configuration data from parsed markdown files.
        
        Args:
            parsed_data: Output from MarkdownParser.parse_directory()
            gpu_data: Consolidated GPU specifications DataFrame
            
        Returns:
            DataFrame with system-level configurations
        """
        self.logger.info("Starting system configuration processing")
        
        # Extract system configurations from parsed data
        system_records = []
        
        for table in parsed_data.get('consolidated_tables', []):
            df = table.get('dataframe')
            if df is None or df.empty:
                continue
            
            # Check if table contains system-level data
            if self._is_system_configuration_table(df, table):
                table_records = self._extract_system_records_from_table(df, table, gpu_data)
                system_records.extend(table_records)
        
        if not system_records:
            self.logger.info("No system configuration records found, generating from GPU data")
            # Generate basic system configs from individual GPU data
            system_records = self._generate_basic_system_configs(gpu_data)
        
        if not system_records:
            self.logger.warning("No system configuration data available")
            return pd.DataFrame()
        
        # Create system configurations DataFrame
        system_df = pd.DataFrame(system_records)
        
        # Calculate aggregate specifications
        system_df = self._calculate_aggregate_specs(system_df, gpu_data)
        
        self.logger.info(f"Processed {len(system_df)} system configurations")
        return system_df
    
    def _is_system_configuration_table(self, df: pd.DataFrame, table_info: Dict[str, Any]) -> bool:
        """Check if a table contains system-level configuration data."""
        if df.empty:
            return False
        
        # Check table context and content for system indicators
        context = table_info.get('context', {})
        if isinstance(context, dict):
            context_text = ' '.join(str(v) for v in context.values()).lower()
        else:
            context_text = str(context).lower()
        
        # Check for system indicators in context
        context_matches = len(self.system_pattern.findall(context_text))
        
        # Check column headers for system-related terms
        headers_text = ' '.join(df.columns).lower()
        header_matches = len(self.system_pattern.findall(headers_text))
        
        # Check first column data for system names
        first_col_text = ' '.join(df.iloc[:, 0].dropna().astype(str)).lower()
        data_matches = len(self.system_pattern.findall(first_col_text))
        
        # Check for GPU count indicators
        all_text = headers_text + ' ' + first_col_text + ' ' + context_text
        gpu_count_matches = sum(len(re.findall(pattern, all_text, re.IGNORECASE)) 
                               for pattern in self.gpu_count_patterns)
        
        # Consider it a system table if we have multiple indicators
        total_indicators = context_matches + header_matches + data_matches + gpu_count_matches
        return total_indicators >= 2
    
    def _extract_system_records_from_table(self, df: pd.DataFrame, table_info: Dict[str, Any],
                                         gpu_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Extract system configuration records from a table."""
        records = []
        
        # Try different extraction strategies based on table structure
        if self._has_system_names_in_first_column(df):
            records = self._extract_from_system_rows(df, table_info, gpu_data)
        elif self._has_system_names_in_headers(df):
            records = self._extract_from_system_columns(df, table_info, gpu_data)
        else:
            records = self._extract_from_mixed_system_structure(df, table_info, gpu_data)
        
        return records
    
    def _has_system_names_in_first_column(self, df: pd.DataFrame) -> bool:
        """Check if first column contains system names."""
        first_col_data = df.iloc[:, 0].dropna().astype(str)
        system_count = 0
        
        for value in first_col_data.head(10):
            if self.system_pattern.search(value.lower()):
                system_count += 1
        
        return system_count >= 2
    
    def _has_system_names_in_headers(self, df: pd.DataFrame) -> bool:
        """Check if column headers contain system names."""
        header_matches = 0
        for col in df.columns[1:]:  # Skip first column
            if self.system_pattern.search(col.lower()):
                header_matches += 1
        
        return header_matches >= 2
    
    def _extract_from_system_rows(self, df: pd.DataFrame, table_info: Dict[str, Any],
                                gpu_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Extract system configs when system names are in rows."""
        records = []
        
        for idx, row in df.iterrows():
            system_name = str(row.iloc[0])
            if not self.system_pattern.search(system_name.lower()):
                continue
            
            record = {
                'system_name': system_name,
                'source_file': table_info.get('source_file', 'unknown')
            }
            
            # Extract specifications from other columns
            for col_idx, col_name in enumerate(df.columns[1:], 1):
                value = row.iloc[col_idx]
                if pd.notna(value):
                    standardized_key = self._standardize_system_spec_name(col_name)
                    if standardized_key:
                        record[standardized_key] = value
            
            # Try to extract GPU information from system name or specifications
            self._extract_gpu_info_from_record(record, gpu_data)
            
            # Ensure we have at least basic GPU info
            if 'gpu_count' not in record or 'gpu_model' not in record:
                # Try to extract from any column value that might contain GPU info
                for col_idx, col_name in enumerate(df.columns[1:], 1):
                    value = row.iloc[col_idx]
                    if pd.notna(value):
                        value_str = str(value).lower()
                        
                        # Check for GPU count patterns
                        if 'gpu_count' not in record:
                            for pattern in self.gpu_count_patterns:
                                match = re.search(pattern, value_str, re.IGNORECASE)
                                if match:
                                    try:
                                        record['gpu_count'] = int(match.group(1))
                                        break
                                    except (ValueError, IndexError):
                                        continue
                        
                        # Check for GPU model names
                        if 'gpu_model' not in record and not gpu_data.empty:
                            for gpu_model in gpu_data['model_name'].dropna():
                                if any(part.lower() in value_str for part in gpu_model.split() if len(part) > 2):
                                    record['gpu_model'] = gpu_model
                                    break
            
            records.append(record)
        
        return records
    
    def _extract_from_system_columns(self, df: pd.DataFrame, table_info: Dict[str, Any],
                                   gpu_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Extract system configs when system names are in column headers."""
        records = []
        
        for col in df.columns[1:]:  # Skip first column (spec names)
            if not self.system_pattern.search(col.lower()):
                continue
            
            record = {
                'system_name': col,
                'source_file': table_info.get('source_file', 'unknown')
            }
            
            # Extract specifications from rows
            for idx, spec_name in enumerate(df.iloc[:, 0]):
                if pd.isna(spec_name) or idx >= len(df):
                    continue
                
                value = df.iloc[idx, df.columns.get_loc(col)]
                if pd.notna(value):
                    standardized_key = self._standardize_system_spec_name(str(spec_name))
                    if standardized_key:
                        record[standardized_key] = value
            
            # Try to extract GPU information
            self._extract_gpu_info_from_record(record, gpu_data)
            
            records.append(record)
        
        return records
    
    def _extract_from_mixed_system_structure(self, df: pd.DataFrame, table_info: Dict[str, Any],
                                           gpu_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Extract system configs from tables with mixed structure."""
        records = []
        
        # Look for system names anywhere in the table
        for col in df.columns:
            for idx, value in enumerate(df[col]):
                if pd.isna(value):
                    continue
                
                if self.system_pattern.search(str(value).lower()):
                    record = self._extract_system_specs_for_name(
                        df, col, idx, str(value), table_info, gpu_data
                    )
                    if record:
                        records.append(record)
        
        return records
    
    def _extract_system_specs_for_name(self, df: pd.DataFrame, name_col: str, name_row: int,
                                     system_name: str, table_info: Dict[str, Any],
                                     gpu_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Extract specifications for a specific system name."""
        record = {
            'system_name': system_name,
            'source_file': table_info.get('source_file', 'unknown')
        }
        
        # Extract specs from the same row
        for col_idx, col_name in enumerate(df.columns):
            if col_idx == df.columns.get_loc(name_col):
                continue  # Skip the column with system name
            
            value = df.iloc[name_row, col_idx]
            if pd.notna(value):
                standardized_key = self._standardize_system_spec_name(str(col_name))
                if standardized_key:
                    record[standardized_key] = value
        
        # Try to extract GPU information
        self._extract_gpu_info_from_record(record, gpu_data)
        
        return record if len(record) > 2 else None  # Must have some specs
    
    def _standardize_system_spec_name(self, spec_name: str) -> Optional[str]:
        """Standardize system specification name."""
        spec_lower = spec_name.lower().strip()
        
        # Direct mapping lookup
        for standard_key, variations in self.system_spec_mappings.items():
            for variation in variations:
                if variation in spec_lower:
                    return standard_key
        
        # Pattern-based matching
        if 'gpu' in spec_lower and ('count' in spec_lower or 'number' in spec_lower or spec_lower == 'gpus'):
            return 'gpu_count'
        
        if 'power' in spec_lower or 'tdp' in spec_lower:
            return 'total_tdp_kw'
        
        return None
    
    def _extract_gpu_info_from_record(self, record: Dict[str, Any], gpu_data: pd.DataFrame):
        """Extract GPU model and count information from system record."""
        system_name = str(record.get('system_name', '')).lower()
        
        # Try to extract GPU count from system name
        if 'gpu_count' not in record:
            for pattern in self.gpu_count_patterns:
                match = re.search(pattern, system_name, re.IGNORECASE)
                if match:
                    try:
                        record['gpu_count'] = int(match.group(1))
                        break
                    except (ValueError, IndexError):
                        continue
        
        # Try to identify GPU model from system name or existing data
        if 'gpu_model' not in record and not gpu_data.empty:
            # Look for GPU model names in system name
            for gpu_model in gpu_data['model_name'].dropna():
                gpu_model_clean = re.sub(r'[^\w\d]', '', gpu_model.lower())
                system_name_clean = re.sub(r'[^\w\d]', '', system_name)
                
                if gpu_model_clean in system_name_clean or any(
                    part in system_name_clean for part in gpu_model_clean.split()
                    if len(part) > 2
                ):
                    record['gpu_model'] = gpu_model
                    break
    
    def _generate_basic_system_configs(self, gpu_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate basic system configurations from individual GPU data."""
        if gpu_data.empty:
            return []
        
        records = []
        
        # Create single-GPU systems for each GPU model
        for _, gpu_row in gpu_data.iterrows():
            model_name = gpu_row.get('model_name')
            if not model_name:
                continue
            
            record = {
                'system_name': f"Single {model_name} System",
                'gpu_count': 1,
                'gpu_model': model_name,
                'source_file': 'generated_from_gpu_data'
            }
            
            records.append(record)
        
        # Create some multi-GPU configurations for popular models
        popular_models = ['H100', 'A100', 'V100']  # Add more as needed
        multi_gpu_configs = [2, 4, 8]
        
        for gpu_model in gpu_data['model_name'].dropna():
            if any(popular in gpu_model for popular in popular_models):
                for gpu_count in multi_gpu_configs:
                    record = {
                        'system_name': f"{gpu_count}x {gpu_model} System",
                        'gpu_count': gpu_count,
                        'gpu_model': gpu_model,
                        'source_file': 'generated_multi_gpu_config'
                    }
                    records.append(record)
        
        return records
    
    def _calculate_aggregate_specs(self, system_df: pd.DataFrame, 
                                 gpu_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate aggregate specifications for multi-GPU systems."""
        if system_df.empty or gpu_data.empty:
            return system_df
        
        enhanced_df = system_df.copy()
        
        # Specifications to aggregate (multiply by GPU count)
        aggregate_specs = {
            'total_fp4_petaflops': 'fp4_tensor_tflops',
            'total_fp8_petaflops': 'fp8_tensor_tflops', 
            'total_fp16_petaflops': 'fp16_tensor_tflops',
            'total_fp32_teraflops': 'fp32_tflops',
            'total_fp64_teraflops': 'fp64_tflops',
            'total_memory_tb': 'memory_size_gb',
            'total_bandwidth_tbps': 'memory_bandwidth_gbps'
        }
        
        for idx, system_row in enhanced_df.iterrows():
            gpu_model = system_row.get('gpu_model')
            gpu_count = system_row.get('gpu_count', 1)
            
            # Ensure gpu_count is an integer
            if isinstance(gpu_count, str):
                # Try to extract number from string like "8x A100"
                import re
                match = re.search(r'(\d+)', str(gpu_count))
                if match:
                    gpu_count = int(match.group(1))
                else:
                    gpu_count = 1
            
            if not gpu_model or gpu_count <= 0:
                continue
            
            # Find matching GPU data
            gpu_match = gpu_data[gpu_data['model_name'] == gpu_model]
            if gpu_match.empty:
                # Try fuzzy matching
                gpu_match = self._find_fuzzy_gpu_match(gpu_model, gpu_data)
            
            if gpu_match.empty:
                continue
            
            gpu_specs = gpu_match.iloc[0]
            
            # Calculate aggregate specifications
            for system_spec, gpu_spec in aggregate_specs.items():
                gpu_value = self._get_normalized_gpu_value(gpu_specs, gpu_spec)
                if gpu_value is not None:
                    if system_spec.endswith('_petaflops'):
                        # Convert TFLOPS to petaFLOPS
                        aggregate_value = (gpu_value * gpu_count) / 1000
                    elif system_spec == 'total_memory_tb':
                        # Convert GB to TB
                        aggregate_value = (gpu_value * gpu_count) / 1024
                    elif system_spec == 'total_bandwidth_tbps':
                        # Convert GB/s to TB/s
                        aggregate_value = (gpu_value * gpu_count) / 1024
                    else:
                        aggregate_value = gpu_value * gpu_count
                    
                    enhanced_df.at[idx, system_spec] = aggregate_value
            
            # Calculate total TDP if individual GPU TDP is available
            gpu_tdp = self._get_normalized_gpu_value(gpu_specs, 'tdp_watts')
            if gpu_tdp is not None:
                total_tdp_kw = (gpu_tdp * gpu_count) / 1000  # Convert to kW
                enhanced_df.at[idx, 'total_tdp_kw'] = total_tdp_kw
        
        return enhanced_df
    
    def _find_fuzzy_gpu_match(self, gpu_model: str, gpu_data: pd.DataFrame) -> pd.DataFrame:
        """Find GPU data using fuzzy matching."""
        if not gpu_model or pd.isna(gpu_model):
            return pd.DataFrame()
        gpu_model_clean = re.sub(r'[^\w\d]', '', str(gpu_model).lower())
        
        for idx, row in gpu_data.iterrows():
            candidate_model = row.get('model_name', '')
            candidate_clean = re.sub(r'[^\w\d]', '', candidate_model.lower())
            
            # Check for substantial overlap
            if (gpu_model_clean in candidate_clean or 
                candidate_clean in gpu_model_clean or
                self._calculate_string_similarity(gpu_model_clean, candidate_clean) > 0.7):
                return gpu_data.iloc[[idx]]
        
        return pd.DataFrame()
    
    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """Calculate simple string similarity score."""
        if not str1 or not str2:
            return 0.0
        
        # Simple character overlap ratio
        set1, set2 = set(str1), set(str2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _get_normalized_gpu_value(self, gpu_specs: pd.Series, spec_name: str) -> Optional[float]:
        """Get normalized numerical value for a GPU specification."""
        # Try normalized column first
        normalized_col = f'{spec_name}_normalized'
        if normalized_col in gpu_specs and pd.notna(gpu_specs[normalized_col]):
            return float(gpu_specs[normalized_col])
        
        # Try original column
        if spec_name in gpu_specs and pd.notna(gpu_specs[spec_name]):
            value_str = str(gpu_specs[spec_name])
            # Extract numerical value
            import re
            match = re.search(r'(\d+(?:\.\d+)?)', value_str)
            if match:
                return float(match.group(1))
        
        return None
    
    def get_system_processing_summary(self, system_df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary of system processing results."""
        if system_df.empty:
            return {
                'total_systems': 0,
                'gpu_models_used': [],
                'gpu_count_distribution': {},
                'aggregate_specs_calculated': 0
            }
        
        # GPU count distribution
        gpu_counts = system_df['gpu_count'].dropna()
        gpu_count_dist = gpu_counts.value_counts().to_dict()
        
        # GPU models used
        gpu_models = system_df['gpu_model'].dropna().unique().tolist()
        
        # Count systems with calculated aggregate specs
        aggregate_cols = [col for col in system_df.columns if col.startswith('total_')]
        systems_with_aggregates = system_df[aggregate_cols].notna().any(axis=1).sum()
        
        return {
            'total_systems': len(system_df),
            'gpu_models_used': gpu_models,
            'gpu_count_distribution': gpu_count_dist,
            'aggregate_specs_calculated': systems_with_aggregates,
            'available_aggregate_specs': aggregate_cols
        }