{% extends "base.html" %}

{% block title %}GPU Visualization Tool - Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-2">GPU Visualization Tool</h1>
                <p class="lead mb-0">Consolidate and visualize GPU specifications from multiple sources</p>
            </div>
            <div class="d-flex align-items-center">
                <div class="badge bg-primary me-2" id="data-status-badge">
                    <i class="bi bi-database"></i> <span id="data-count">Loading...</span>
                </div>
                <button type="button" class="btn btn-outline-primary btn-sm" id="global-refresh-btn" title="Refresh all data">
                    <i class="bi bi-arrow-clockwise"></i> Refresh All
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tab Navigation -->
<ul class="nav nav-tabs nav-tabs-enhanced" id="mainTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="individual-gpus-tab" data-bs-toggle="tab" 
                data-bs-target="#individual-gpus" type="button" role="tab" 
                aria-controls="individual-gpus" aria-selected="true">
            <i class="bi bi-cpu me-2"></i>
            <span class="tab-title">Individual GPUs</span>
            <span class="badge bg-light text-dark ms-2" id="gpu-count-badge">0</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="system-configs-tab" data-bs-toggle="tab" 
                data-bs-target="#system-configs" type="button" role="tab" 
                aria-controls="system-configs" aria-selected="false">
            <i class="bi bi-server me-2"></i>
            <span class="tab-title">System Configurations</span>
            <span class="badge bg-light text-dark ms-2" id="system-count-badge">0</span>
        </button>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="mainTabsContent">
    <!-- Individual GPUs Tab -->
    <div class="tab-pane fade show active" id="individual-gpus" role="tabpanel" 
         aria-labelledby="individual-gpus-tab">
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>Individual GPU Specifications</h3>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="refresh-gpu-data">
                            <i class="bi bi-arrow-clockwise"></i> Refresh Data
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-export="csv">CSV</a></li>
                                <li><a class="dropdown-item" href="#" data-export="json">JSON</a></li>
                                <li><a class="dropdown-item" href="#" data-export="markdown">Markdown</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Table Controls -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="gpu-search" placeholder="Search GPU models...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="column-controls-btn" 
                                    data-bs-toggle="collapse" data-bs-target="#column-controls" aria-expanded="false">
                                <i class="bi bi-columns-gap"></i> Column Controls
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="reset-table-btn">
                                <i class="bi bi-arrow-counterclockwise"></i> Reset View
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Column Controls Panel -->
                <div class="collapse mb-3" id="column-controls">
                    <div class="card card-body">
                        <h6 class="card-title">Show/Hide Columns</h6>
                        <div class="row" id="column-checkboxes">
                            <!-- Column checkboxes will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- GPU Data Container -->
                <div class="table-container">
                    <div id="gpu-data-container">
                        <div class="alert alert-info" role="alert">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                Loading GPU specifications...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Configurations Tab -->
    <div class="tab-pane fade" id="system-configs" role="tabpanel" 
         aria-labelledby="system-configs-tab">
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>System Configurations</h3>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="refresh-system-data">
                            <i class="bi bi-arrow-clockwise"></i> Refresh Data
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-export="csv">CSV</a></li>
                                <li><a class="dropdown-item" href="#" data-export="json">JSON</a></li>
                                <li><a class="dropdown-item" href="#" data-export="markdown">Markdown</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- System Data Loading Placeholder -->
                <div id="system-data-container">
                    <div class="alert alert-info" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            Loading system configurations...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Messages -->
<div id="status-messages" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <!-- Toast messages will be inserted here -->
</div>
{% endblock %}

{% block scripts %}
<script src="{{ request.url_for('static', path='/js/gpu-table.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab functionality
    initializeTabs();
    
    // Initialize GPU table when Individual GPUs tab is active
    try {
        console.log('About to initialize GPU table...');
        initializeGPUTable();
        console.log('GPU table initialized successfully');
    } catch (error) {
        console.error('Error initializing GPU table:', error);
    }
    
    // Load system data for System Configurations tab
    loadSystemData();
    
    // Set up event listeners
    setupEventListeners();
});

function initializeTabs() {
    // Tab switching functionality is handled by Bootstrap
    const tabElements = document.querySelectorAll('#mainTabs button[data-bs-toggle="tab"]');
    tabElements.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            console.log('Switched to tab:', event.target.id);
            
            // Update tab state tracking
            window.GPUVisualizationTool.currentTab = event.target.id.replace('-tab', '');
            
            // Handle tab-specific initialization
            if (event.target.id === 'individual-gpus-tab') {
                // Reinitialize GPU table when switching to Individual GPUs tab
                setTimeout(() => {
                    if (typeof gpuTableManager !== 'undefined') {
                        gpuTableManager.loadGPUData();
                    }
                }, 100);
            } else if (event.target.id === 'system-configs-tab') {
                // Load system data when switching to System Configurations tab
                setTimeout(() => {
                    loadSystemData();
                }, 100);
            }
        });
        
        // Add enhanced visual feedback for tab switching
        tab.addEventListener('click', function(event) {
            // Update active states (Bootstrap handles this, but we add custom logic)
            updateTabActiveStates(event.target);
        });
        
        // Add hover effects
        tab.addEventListener('mouseenter', function(event) {
            if (!event.target.classList.contains('active')) {
                event.target.style.transform = 'translateY(-2px)';
            }
        });
        
        tab.addEventListener('mouseleave', function(event) {
            event.target.style.transform = '';
        });
    });
}

function updateTabActiveStates(activeTab) {
    const allTabs = document.querySelectorAll('#mainTabs button[data-bs-toggle="tab"]');
    allTabs.forEach(tab => {
        if (tab === activeTab) {
            tab.setAttribute('aria-selected', 'true');
        } else {
            tab.setAttribute('aria-selected', 'false');
        }
    });
}

function loadSystemData() {
    fetch('/api/systems')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('system-data-container');
            if (data.success) {
                container.innerHTML = `
                    <div class="alert alert-success" role="alert">
                        ${data.message} - Ready for system data implementation in next tasks
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        Error loading system data: ${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading system data:', error);
            document.getElementById('system-data-container').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    Failed to load system data: ${error.message}
                </div>
            `;
        });
}

function setupEventListeners() {
    // System data refresh button
    document.getElementById('refresh-system-data')?.addEventListener('click', function() {
        showToast('Refreshing system data...', 'info');
        loadSystemData();
    });
    
    // Export buttons for system data
    const systemTab = document.getElementById('system-configs');
    if (systemTab) {
        systemTab.querySelectorAll('[data-export]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const format = this.getAttribute('data-export');
                exportSystemData(format);
            });
        });
    }
}

function exportSystemData(format) {
    showToast(`Exporting system data as ${format.toUpperCase()}...`, 'info');
    
    fetch(`/api/export/systems/${format}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`System data exported as ${format.toUpperCase()}`, 'success');
            } else {
                showToast(`Export failed: ${data.error}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Export error:', error);
            showToast(`Export failed: ${error.message}`, 'danger');
        });
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('status-messages');
    const toastId = 'toast-' + Date.now();
    
    const toastHTML = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" 
             aria-live="assertive" aria-atomic="true" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}
</script>
{% endblock %}