{% extends "base.html" %}

{% block title %}Individual GPUs - GPU Visualization Tool{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Individual GPU Specifications</h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refresh-data">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                            data-bs-toggle="dropdown" aria-expanded="false">
                        Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-export="csv">CSV</a></li>
                        <li><a class="dropdown-item" href="#" data-export="json">JSON</a></li>
                        <li><a class="dropdown-item" href="#" data-export="markdown">Markdown</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Table Controls -->
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="gpu-search" placeholder="Search GPU models...">
                </div>
            </div>
            <div class="col-md-4">
                <div class="btn-group w-100" role="group">
                    <button type="button" class="btn btn-outline-secondary" id="column-controls-btn" 
                            data-bs-toggle="collapse" data-bs-target="#column-controls" aria-expanded="false">
                        <i class="bi bi-columns-gap"></i> Columns
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="row-controls-btn" 
                            data-bs-toggle="collapse" data-bs-target="#row-controls" aria-expanded="false">
                        <i class="bi bi-list-ul"></i> Rows
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="btn-group w-100" role="group">
                    <div class="undo-redo-controls">
                        <button type="button" class="btn btn-outline-secondary" id="undo-btn" disabled title="Undo (Ctrl+Z)">
                            <i class="bi bi-arrow-counterclockwise"></i> Undo
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="redo-btn" disabled title="Redo (Ctrl+Y)">
                            <i class="bi bi-arrow-clockwise"></i> Redo
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="reset-table-btn" title="Reset all customizations">
                            <i class="bi bi-arrow-counterclockwise"></i> Reset
                        </button>
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="collapse" 
                                data-bs-target="#keyboard-shortcuts" aria-expanded="false" title="Show keyboard shortcuts">
                            <i class="bi bi-question-circle"></i> Help
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Column Controls Panel -->
        <div class="collapse mb-3" id="column-controls">
            <div class="card card-body column-controls-panel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0">Column Visibility</h6>
                    <div class="bulk-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="show-all-columns">
                            Show All
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="hide-all-columns">
                            Hide All
                        </button>
                    </div>
                </div>
                <div class="row" id="column-checkboxes">
                    <!-- Column checkboxes will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Row Controls Panel -->
        <div class="collapse mb-3" id="row-controls">
            <div class="card card-body column-controls-panel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0">Row Visibility</h6>
                    <div class="bulk-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="show-all-rows">
                            Show All
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="hide-all-rows">
                            Hide All
                        </button>
                    </div>
                </div>
                <div class="row" id="row-checkboxes">
                    <!-- Row checkboxes will be populated by JavaScript -->
                </div>
            </div>
        </div>
        
        <!-- Keyboard Shortcuts Help -->
        <div class="collapse mb-3" id="keyboard-shortcuts">
            <div class="card card-body">
                <h6 class="card-title">Keyboard Shortcuts</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><kbd>Ctrl+Z</kbd> - Undo last action</li>
                            <li><kbd>Ctrl+Y</kbd> - Redo last action</li>
                            <li><kbd>Ctrl+A</kbd> - Show all columns</li>
                            <li><kbd>Ctrl+H</kbd> - Hide all columns (except model name)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><kbd>Double-click</kbd> - Edit cell value</li>
                            <li><kbd>Enter</kbd> - Save cell edit</li>
                            <li><kbd>Escape</kbd> - Cancel cell edit</li>
                            <li><kbd>Drag & Drop</kbd> - Reorder columns/rows</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- GPU Table Container -->
        <div class="table-container">
            <div id="gpu-data-container">
                <!-- GPU table will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ request.url_for('static', path='/js/gpu-table.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeGPUTable();
});
</script>
{% endblock %}