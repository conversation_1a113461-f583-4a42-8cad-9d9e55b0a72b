{% extends "base.html" %}

{% block title %}System Configurations - GPU Visualization Tool{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1>System Configurations</h1>
                <p class="text-muted">View and manage multi-GPU system configurations and aggregate specifications</p>
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refresh-data">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="bi bi-house"></i> Dashboard
                </a>
            </div>
        </div>
        
        <!-- System Table Container -->
        <div class="card">
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <h5 class="alert-heading">
                        <i class="bi bi-info-circle"></i> Coming Soon
                    </h5>
                    <p class="mb-0">
                        System configurations table will be implemented in upcoming tasks. 
                        This will include aggregate calculations, multi-GPU system specifications, and system-level performance metrics.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set up refresh button
    document.getElementById('refresh-data')?.addEventListener('click', function() {
        window.GPUVisualizationTool.utils.showNotification('Data refresh functionality will be implemented in upcoming tasks', 'info');
    });
});
</script>
{% endblock %}