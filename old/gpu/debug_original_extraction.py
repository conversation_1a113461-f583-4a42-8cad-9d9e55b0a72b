#!/usr/bin/env python3

"""
Debug script to test the original table data extraction.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    # Test the first table (nvidia-gpu-comparison-table.md)
    table = parsed_data['consolidated_tables'][0]
    original_data = table['original_data']
    
    print("=== Testing Original Table Data Extraction ===")
    print(f"Headers: {original_data['headers']}")
    
    # Test GPU model detection
    gpu_model_columns = []
    for i, header in enumerate(original_data['headers'][1:], 1):  # Start from index 1
        header_lower = header.lower()
        gpu_indicators = [
            'nvidia', 'amd', 'intel', 'a100', 'h100', 'v100', 'rtx', 'gtx',
            'hgx', 'b200', 'b300', 'gb200', 'gb300', 'nvl72', 'sxm', 'pcie',
            'blackwell', 'hopper', 'ampere', 'ada', 'lovelace'
        ]
        
        matches = [indicator for indicator in gpu_indicators if indicator in header_lower]
        if matches:
            gpu_model_columns.append((i, header, matches))
            print(f"GPU model found at column {i}: '{header}' (matches: {matches})")
    
    print(f"\nFound {len(gpu_model_columns)} GPU model columns")
    
    # Test the extraction method directly
    records = processor._extract_from_original_table_data(original_data, table)
    print(f"\nExtracted {len(records)} records:")
    
    for record in records:
        print(f"\nModel: {record['model_name']}")
        # Show a few key specifications
        key_specs = ['architecture', 'memory_size_gb', 'fp32_tflops', 'tdp_watts']
        for spec in key_specs:
            if spec in record:
                print(f"  {spec}: {record[spec]}")

if __name__ == "__main__":
    main()