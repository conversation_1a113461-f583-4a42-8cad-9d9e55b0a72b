#!/usr/bin/env python3

"""
Debug script to test the GPU model detection methods.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    # Test the first table
    table = parsed_data['consolidated_tables'][0]
    df = table['dataframe']
    
    print("=== GPU Model Detection Debug ===")
    print(f"DataFrame columns: {list(df.columns)}")
    print(f"DataFrame shape: {df.shape}")
    
    # Test detection methods
    has_models_in_first_col = processor._contains_gpu_models_in_first_column(df)
    has_models_in_headers = processor._has_gpu_models_in_headers(df)
    
    print(f"\n_contains_gpu_models_in_first_column: {has_models_in_first_col}")
    print(f"_has_gpu_models_in_headers: {has_models_in_headers}")
    
    # Show first column data
    print(f"\nFirst column data (first 10 rows):")
    for i, value in enumerate(df.iloc[:, 0].head(10)):
        print(f"  {i}: '{value}'")
    
    # Show headers
    print(f"\nHeaders:")
    for i, header in enumerate(df.columns):
        print(f"  {i}: '{header}'")
    
    # Test which extraction method would be called
    if has_models_in_first_col:
        print("\nWould call: _extract_from_model_rows")
    elif has_models_in_headers:
        print("\nWould call: _extract_from_model_columns")
    else:
        print("\nWould call: _extract_from_mixed_structure")

if __name__ == "__main__":
    main()