#!/usr/bin/env python3

"""
Debug script to test the standardization function directly.
"""

from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    processor = GPUDataProcessor()
    
    test_specs = [
        "Memory Size",
        "Shared Memory Size / SM",
        "memory size",
        "shared memory size / sm"
    ]
    
    for spec in test_specs:
        result = processor._standardize_spec_name(spec)
        print(f"'{spec}' -> {result}")

if __name__ == "__main__":
    main()