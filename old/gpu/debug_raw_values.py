#!/usr/bin/env python3

"""
Debug script to see the raw values before normalization.
"""

from data_parser.markdown_parser import MarkdownParser
import pandas as pd

def main():
    parser = MarkdownParser()
    
    # Parse just the GPU comparison table
    parsed_data = parser.parse_directory('docs')
    
    # Look at the first table (nvidia-gpu-comparison-table.md)
    table = parsed_data['consolidated_tables'][0]
    df = table['dataframe']
    
    print("=== Raw Values from NVIDIA GPU Comparison Table ===")
    print(f"Shape: {df.shape}")
    
    # Look at just the core columns (not the normalized ones)
    core_columns = [col for col in df.columns if not col.endswith('_original_unit') and not col.endswith('_normalized_unit') and not col.endswith('_category')]
    
    print(f"Core columns: {core_columns}")
    
    # Show some key specifications
    key_specs = [
        'GPU Architecture',
        'Memory Size', 
        'Memory Interface',
        'Memory Bandwidth (Not Finalized for H100)¹',
        'TDP¹',
        'Peak FP32 TFLOPS (non-Tensor)',
        'Peak FP16 Tensor TFLOPS with FP32 Accumulate'
    ]
    
    for spec in key_specs:
        if spec in df['GPU Features'].values:
            row_idx = df[df['GPU Features'] == spec].index[0]
            print(f"\n{spec}:")
            for col in core_columns[1:]:  # Skip 'GPU Features' column
                value = df.loc[row_idx, col]
                print(f"  {col}: '{value}' (type: {type(value)})")

if __name__ == "__main__":
    main()