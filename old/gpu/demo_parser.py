#!/usr/bin/env python3
"""
Demonstration script for the GPU specification markdown parser.
"""

import json
from data_parser import Mark<PERSON><PERSON>ars<PERSON>


def main():
    """Demonstrate the markdown parser functionality."""
    parser = MarkdownParser()
    
    print("=== GPU Specification Markdown Parser Demo ===\n")
    
    # Parse the entire docs directory
    print("1. Parsing docs directory...")
    result = parser.parse_directory('docs')
    
    print(f"   - Total files processed: {result['metadata']['total_files']}")
    print(f"   - Files with GPU tables: {result['metadata']['files_with_tables']}")
    print(f"   - Total tables found: {result['metadata']['total_tables']}")
    
    if result['metadata']['parsing_errors']:
        print(f"   - Parsing errors: {len(result['metadata']['parsing_errors'])}")
    
    print()
    
    # Show details for each file
    print("2. File-by-file breakdown:")
    for file_path, file_data in result['files'].items():
        print(f"   📄 {file_data['file_name']}")
        print(f"      Tables: {len(file_data['tables'])}")
        
        for i, table in enumerate(file_data['tables']):
            df = table['dataframe']
            categories = table['categories']
            print(f"      Table {i+1}: {df.shape[0]} rows × {df.shape[1]} columns")
            print(f"      Categories: {list(categories.keys())}")
            
            # Show some sample specifications
            for category, specs in categories.items():
                if category != 'other' and specs:
                    print(f"        {category}: {specs[:3]}{'...' if len(specs) > 3 else ''}")
        print()
    
    # Create consolidated DataFrame
    print("3. Consolidated data:")
    consolidated_df = parser.get_consolidated_dataframe(result)
    if not consolidated_df.empty:
        print(f"   - Total GPU models/systems: {len(consolidated_df)}")
        print(f"   - Total specifications: {len(consolidated_df.columns)}")
        
        # Show first few rows and columns
        print("\n   Sample data (first 3 rows, first 4 columns):")
        sample_df = consolidated_df.iloc[:3, :4]
        print(sample_df.to_string())
    
    print()
    
    # Validation
    print("4. Data validation:")
    validation = parser.validate_parsed_data(result)
    print(f"   - Valid: {validation['is_valid']}")
    print(f"   - Categories found: {validation['statistics']['categories_found']}")
    
    if validation['warnings']:
        print(f"   - Warnings: {len(validation['warnings'])}")
        for warning in validation['warnings'][:3]:
            print(f"     • {warning}")
    
    print()
    
    # Demonstrate dense value extraction
    print("5. Dense value extraction examples:")
    test_values = [
        "105/144 petaFLOPS",
        "72/144 petaFLOPS", 
        "1.1/2.2 TFLOPS",
        "80 GB",
        "576 TB/s"
    ]
    
    for value in test_values:
        dense_val = parser.extract_dense_values(value)
        normalized = parser.spec_normalizer.normalize_unit(value)
        print(f"   '{value}' → Dense: {dense_val}, Normalized: {normalized['normalized_value']} {normalized['normalized_unit']}")
    
    print("\n=== Demo Complete ===")


if __name__ == '__main__':
    main()