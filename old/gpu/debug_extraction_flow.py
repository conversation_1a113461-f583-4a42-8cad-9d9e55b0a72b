#!/usr/bin/env python3

"""
Debug script to trace the extraction flow.
"""

from data_parser.markdown_parser import Markdown<PERSON>arser
from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    # Test the first table
    table = parsed_data['consolidated_tables'][0]
    df = table['dataframe']
    
    print("=== Extraction Flow Debug ===")
    print(f"Table info keys: {table.keys()}")
    print(f"Has 'original_data': {'original_data' in table}")
    
    if 'original_data' in table:
        original_data = table['original_data']
        print(f"Original data keys: {original_data.keys()}")
        print(f"Has 'headers': {'headers' in original_data}")
        print(f"Has 'data': {'data' in original_data}")
        
        if 'headers' in original_data and 'data' in original_data:
            print("Should call _extract_from_original_table_data")
        else:
            print("Will fall back to DataFrame extraction")
    
    # Test the main extraction method
    print("\n=== Testing _extract_gpu_records_from_table ===")
    records = processor._extract_gpu_records_from_table(df, table)
    print(f"Extracted {len(records)} records")
    
    if records:
        print(f"First record model_name: {records[0]['model_name']}")
        print(f"First record keys: {list(records[0].keys())}")

if __name__ == "__main__":
    main()