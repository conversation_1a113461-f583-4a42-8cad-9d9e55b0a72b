#!/usr/bin/env python3

"""
Debug script to test the actual method step by step.
"""

from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    processor = GPUDataProcessor()
    
    spec_name = "Shared Memory Size / SM"
    print(f"Testing: '{spec_name}'")
    
    # Step 1: Check if it's None or NaN
    if not spec_name or spec_name == 'nan':
        print("Step 1: Failed - None or NaN")
        return
    print("Step 1: Passed - not None or NaN")
    
    # Step 2: Convert to lowercase
    spec_lower = spec_name.lower().strip()
    print(f"Step 2: spec_lower = '{spec_lower}'")
    
    # Step 3: Check direct mapping lookup
    print("Step 3: Checking direct mapping...")
    for standard_key, variations in processor.spec_mappings.items():
        for variation in variations:
            if variation == spec_lower:
                print(f"  Found exact match in spec_mappings: {standard_key}")
                return
    print("  No exact match in spec_mappings")
    
    # Step 4: Check partial matches in spec_mappings
    print("Step 4: Checking partial matches in spec_mappings...")
    for standard_key, variations in processor.spec_mappings.items():
        for variation in variations:
            if variation in spec_lower:
                print(f"  Found partial match in spec_mappings: {standard_key} ('{variation}')")
                return
    print("  No partial match in spec_mappings")
    
    # Step 5: Check additional mappings
    print("Step 5: This is where additional_mappings should be checked...")
    result = processor._standardize_spec_name(spec_name)
    print(f"Final result: {result}")

if __name__ == "__main__":
    main()