#!/usr/bin/env python3

"""
Debug script to see what's matching in the first column.
"""

from data_parser.markdown_parser import MarkdownParser
from data_processor.gpu_data_processor import GPUDataProcessor
import re

def main():
    parser = MarkdownParser()
    processor = GPUDataProcessor()
    
    # Parse the directory
    parsed_data = parser.parse_directory('docs')
    
    # Test the first table
    table = parsed_data['consolidated_tables'][0]
    df = table['dataframe']
    
    print("=== Model Detection Debug ===")
    
    first_col_data = df.iloc[:, 0].dropna().astype(str)
    
    gpu_model_patterns = [
        r'nvidia\s+[a-z]+[0-9]+',  # "NVIDIA A100", "NVIDIA H100"
        r'^[a-z]+[0-9]+$',         # "A100", "H100", "RTX4090" (whole string)
        r'geforce',
        r'quadro',
        r'tesla',
        r'rtx\s*[0-9]+',           # "RTX 4090", "RTX4090"
        r'gtx\s*[0-9]+',           # "GTX 1080"
        r'radeon',
        r'rx\s*[0-9]+',            # "RX 6800"
    ]
    
    print("Testing first column values:")
    gpu_model_count = 0
    for i, value in enumerate(first_col_data.head(10)):
        value_lower = value.lower()
        matches = []
        for pattern in gpu_model_patterns:
            if re.search(pattern, value_lower):
                matches.append(pattern)
        
        if matches:
            gpu_model_count += 1
            print(f"  {i}: '{value}' -> MATCHES: {matches}")
        else:
            print(f"  {i}: '{value}' -> no match")
    
    print(f"\nTotal GPU model matches: {gpu_model_count}")
    print(f"Would return: {gpu_model_count >= 2}")

if __name__ == "__main__":
    main()