#!/usr/bin/env python3

"""
Debug script to see what's happening with memory size extraction.
"""

from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    processor = GPUDataProcessor()
    
    # Test the memory size extraction
    test_values = [
        "40 GB",
        "80 GB", 
        "1555 GB/sec",
        "Configurable up to 164 KB"
    ]
    
    for value in test_values:
        print(f"Input: '{value}'")
        
        # Test memory size
        result_gb = processor._extract_numeric_value(value, "memory_size_gb")
        print(f"  memory_size_gb: {result_gb}")
        
        # Test memory bandwidth
        result_bw = processor._extract_numeric_value(value, "memory_bandwidth_gbps")
        print(f"  memory_bandwidth_gbps: {result_bw}")
        
        # Test shared memory
        result_shared = processor._extract_numeric_value(value, "shared_memory_per_sm")
        print(f"  shared_memory_per_sm: {result_shared}")
        
        print()

if __name__ == "__main__":
    main()