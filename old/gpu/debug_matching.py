#!/usr/bin/env python3

"""
Debug script to see exactly what's matching.
"""

from data_processor.gpu_data_processor import GPUDataProcessor

def main():
    processor = GPUDataProcessor()
    
    spec_lower = "shared memory size / sm"
    
    # Test the additional mappings
    additional_mappings = {
        'shared_memory_per_sm': ['shared memory size / sm'],
        'memory_size_gb': ['memory size'],
    }
    
    print(f"Testing spec: '{spec_lower}'")
    
    # Check exact matches
    print("\nExact matches:")
    for standard_key, variations in additional_mappings.items():
        for variation in variations:
            if variation == spec_lower:
                print(f"  {standard_key}: '{variation}' == '{spec_lower}' -> True")
            else:
                print(f"  {standard_key}: '{variation}' == '{spec_lower}' -> False")
    
    # Check partial matches
    print("\nPartial matches:")
    all_variations = []
    for standard_key, variations in additional_mappings.items():
        for variation in variations:
            all_variations.append((standard_key, variation))
    
    all_variations.sort(key=lambda x: len(x[1]), reverse=True)
    
    for standard_key, variation in all_variations:
        if variation in spec_lower:
            print(f"  {standard_key}: '{variation}' in '{spec_lower}' -> True (length: {len(variation)})")
        else:
            print(f"  {standard_key}: '{variation}' in '{spec_lower}' -> False")

if __name__ == "__main__":
    main()