# Design Document

## Overview

This design document outlines the implementation of advanced memory visualization capabilities for the LLM modeling metrics package. The enhancement focuses on providing users with better control over memory calculations, KV cache dtype selection, and interactive visualizations that demonstrate memory efficiency differences between attention mechanisms (MHA, GQA, MLA) as sequence length varies.

The design builds upon the existing architecture while maintaining backward compatibility and following established patterns in the codebase.

## Architecture

### High-Level Architecture

The memory visualization enhancements will be implemented across three main layers:

1. **Backend Layer**: Enhanced memory calculation utilities and new API endpoints
2. **API Layer**: Extended FastAPI endpoints for memory-specific operations
3. **Frontend Layer**: Interactive web components for memory visualization and control

### Component Integration

The enhancements integrate with existing components:

- **MemoryCalculator**: Extended with KV cache dtype support and sequence length analysis
- **BaseModel/DenseModel**: Enhanced memory calculation methods
- **Web API**: New endpoints for memory visualization data
- **Frontend Dashboard**: New UI components for memory controls and visualizations

## Components and Interfaces

### 1. Enhanced Memory Calculator

**Location**: `llm_modeling_metrics/metrics/memory_calculator.py`

**New Methods**:
```python
class MemoryCalculator:
    @staticmethod
    def compute_kv_cache_memory_by_dtype(
        model_config: Dict[str, Any], 
        sequence_length: int,
        batch_size: int = 1,
        dtype: str = 'fp16'
    ) -> Dict[str, int]:
        """Compute KV cache memory with configurable dtype."""
    
    @staticmethod
    def analyze_memory_growth_by_sequence_length(
        model_config: Dict[str, Any],
        total_params: int,
        sequence_lengths: List[int],
        batch_size: int = 1,
        dtype: str = 'fp16'
    ) -> Dict[str, Any]:
        """Analyze memory growth patterns across sequence lengths."""
    
    @staticmethod
    def get_attention_mechanism_info(model_config: Dict[str, Any]) -> Dict[str, str]:
        """Determine attention mechanism type (MHA, GQA, MLA)."""
```

**Design Rationale**: Extending the existing MemoryCalculator maintains consistency with the current architecture while providing the new functionality needed for dtype-specific calculations and sequence length analysis.

### 2. Enhanced Model Classes

**Location**: `llm_modeling_metrics/models/dense_model.py`, `llm_modeling_metrics/models/moe_model.py`

**New Methods**:
```python
class BaseModel:
    def get_memory_breakdown_by_dtype(
        self, 
        sequence_length: int = 2048,
        batch_size: int = 1,
        dtype: str = 'fp16',
        include_kv_cache: bool = True
    ) -> Dict[str, Any]:
        """Get detailed memory breakdown with dtype-specific calculations."""
    
    def get_attention_mechanism_type(self) -> str:
        """Return the attention mechanism type for this model."""
```

**Design Rationale**: Adding methods to the base model classes ensures consistent interface across all model types while leveraging the existing inheritance structure.

### 3. New API Endpoints

**Location**: `llm_modeling_metrics/web/app.py`

**New Endpoints**:
```python
@app.post("/api/memory/analyze")
async def analyze_memory_requirements(request: MemoryAnalysisRequest):
    """Analyze memory requirements with dtype and sequence length variations."""

@app.post("/api/memory/kv-growth")
async def analyze_kv_growth(request: KVGrowthAnalysisRequest):
    """Analyze KV cache memory growth across sequence lengths."""

@app.get("/api/memory/dtypes")
async def get_supported_dtypes():
    """Get list of supported data types for memory calculations."""
```

**Design Rationale**: Separate endpoints for memory-specific operations provide focused functionality while maintaining the existing API structure and patterns.

### 4. Frontend Memory Controls

**Location**: `llm_modeling_metrics/web/static/js/memory-controls.js` (new file)

**New Components**:
- Memory toggle switch
- KV cache dtype selector
- Sequence length range sliders
- Memory growth visualization chart

**Design Rationale**: Creating a separate JavaScript module for memory controls maintains code organization and allows for focused testing and maintenance.

## Data Models

### Memory Analysis Request Model

```python
class MemoryAnalysisRequest(BaseModel):
    model_names: List[str]
    sequence_length: int = 2048
    batch_size: int = 1
    dtype: str = 'fp16'
    include_total_memory: bool = False
    include_kv_cache: bool = True
    
class KVGrowthAnalysisRequest(BaseModel):
    model_names: List[str]
    min_sequence_length: int = 512
    max_sequence_length: int = 32768
    sequence_length_step: int = 1024
    batch_size: int = 1
    dtype: str = 'fp16'
```

### Memory Analysis Response Model

```python
class MemoryBreakdown(BaseModel):
    parameters: int
    kv_cache: int
    activations: int
    total: int
    dtype: str
    attention_mechanism: str

class KVGrowthPoint(BaseModel):
    sequence_length: int
    memory_bytes: int
    memory_human: str

class MemoryAnalysisResponse(BaseModel):
    model_results: Dict[str, MemoryBreakdown]
    kv_growth_data: Optional[Dict[str, List[KVGrowthPoint]]]
    execution_time: float
```

## Error Handling

### Backend Error Handling

1. **Invalid dtype validation**: Validate dtype parameters against supported types
2. **Sequence length bounds**: Ensure sequence lengths are within reasonable bounds
3. **Model configuration errors**: Handle missing or invalid model configurations gracefully
4. **Memory calculation overflow**: Handle large memory calculations that might overflow

### Frontend Error Handling

1. **API request failures**: Display user-friendly error messages for failed requests
2. **Chart rendering errors**: Fallback to table view if chart rendering fails
3. **Invalid input validation**: Client-side validation for sequence length ranges and dtype selection

## Testing Strategy

### Unit Tests

1. **MemoryCalculator Tests**:
   - Test dtype-specific memory calculations
   - Test sequence length analysis functions
   - Test attention mechanism detection

2. **Model Class Tests**:
   - Test enhanced memory breakdown methods
   - Test attention mechanism type detection
   - Test integration with MemoryCalculator

3. **API Endpoint Tests**:
   - Test new memory analysis endpoints
   - Test request/response validation
   - Test error handling scenarios

### Integration Tests

1. **End-to-End Memory Analysis**:
   - Test complete memory analysis workflow
   - Test KV growth analysis across multiple models
   - Test dtype switching functionality

2. **Frontend Integration**:
   - Test memory control interactions
   - Test chart rendering with real data
   - Test responsive behavior

### Performance Tests

1. **Memory Calculation Performance**:
   - Test performance with large sequence lengths
   - Test performance with multiple models
   - Test memory usage of calculations themselves

## Implementation Details

### KV Cache Dtype Support

The implementation will support the following data types:
- `fp32`: 4 bytes per element
- `fp16`: 2 bytes per element  
- `bf16`: 2 bytes per element
- `int8`: 1 byte per element

**Design Decision**: Using a simple mapping approach for dtype byte sizes provides flexibility while maintaining performance. The mapping is centralized in the MemoryCalculator class for consistency.

### Attention Mechanism Detection

The system will detect attention mechanisms based on model configuration:

```python
def get_attention_mechanism_type(model_config: Dict[str, Any]) -> str:
    num_attention_heads = model_config.get('num_attention_heads', 0)
    num_key_value_heads = model_config.get('num_key_value_heads', num_attention_heads)
    
    # Check for MLA indicators
    if 'kv_lora_rank' in model_config or 'qk_rope_head_dim' in model_config:
        return 'MLA'
    
    # Check for GQA
    if num_key_value_heads < num_attention_heads:
        return 'GQA'
    
    # Default to MHA
    return 'MHA'
```

**Design Decision**: Using configuration-based detection provides accurate mechanism identification while being extensible for future attention types.

### Memory Growth Visualization

The visualization will use Chart.js for interactive line charts showing memory growth:

- X-axis: Sequence length
- Y-axis: Memory usage (with human-readable units)
- Multiple lines: One per model
- Interactive tooltips: Show exact values and attention mechanism type
- Legend: Color-coded by attention mechanism

**Design Decision**: Chart.js provides good performance and interactivity while being lightweight and well-supported.

### UI State Management

The frontend will maintain state for:
- Memory toggle switch state
- Selected dtype
- Sequence length range
- Selected models for comparison

**Design Decision**: Using simple JavaScript state management keeps the implementation lightweight while providing the necessary functionality.

## Security Considerations

1. **Input Validation**: All sequence length and dtype inputs will be validated on both client and server sides
2. **Rate Limiting**: Memory analysis endpoints will be subject to the existing rate limiting
3. **Resource Limits**: Large sequence length ranges will be limited to prevent resource exhaustion
4. **Error Information**: Error messages will not expose internal system details

## Performance Considerations

1. **Caching**: Memory calculations for common configurations will be cached
2. **Batch Processing**: Multiple sequence length calculations will be optimized for batch processing
3. **Progressive Loading**: Large datasets will be loaded progressively in the frontend
4. **Chart Optimization**: Chart rendering will be optimized for large datasets using data sampling

## Backward Compatibility

The enhancements maintain full backward compatibility:

1. **Existing API endpoints**: No changes to existing endpoints
2. **Default behavior**: Memory calculations default to existing behavior when new parameters are not specified
3. **Model interfaces**: New methods are additive, existing methods unchanged
4. **Frontend**: New UI components are additive, existing functionality preserved

## Migration Strategy

The implementation will be rolled out in phases:

1. **Phase 1**: Backend memory calculation enhancements
2. **Phase 2**: New API endpoints and data models
3. **Phase 3**: Frontend memory controls and basic visualization
4. **Phase 4**: Advanced visualization features and optimization

This phased approach allows for incremental testing and validation while maintaining system stability.