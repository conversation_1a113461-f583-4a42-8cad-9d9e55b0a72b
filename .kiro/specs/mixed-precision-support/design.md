# Design Document

## Overview

The mixed precision support feature extends the existing LLM modeling metrics system to handle different data types for different model components independently. This design maintains backward compatibility while adding granular control over precision settings for activations, KV cache, expert parameters, and attention parameters.

## Architecture

### Core Components

The mixed precision support will be implemented across three main layers:

1. **Operator Layer** (`operators.py`): Base operators will be enhanced to accept and use component-specific precision settings
2. **Model Layer** (`dense_model.py`, `moe_model.py`): Model classes will support mixed precision parameters in their computation methods
3. **Memory Calculator Layer**: Enhanced to handle mixed precision memory calculations

### Precision Type Mapping

The system will support the following precision types with their corresponding byte sizes:

```python
PRECISION_BYTES = {
    'fp32': 4,
    'fp16': 2,
    'bf16': 2,
    'int8': 1,
    'fp8': 1,
    'fp4': 0.5  # Handled as packed format
}
```

## Components and Interfaces

### Enhanced BaseOperator

The `BaseOperator` class will be extended to support mixed precision:

```python
class BaseOperator(ABC):
    def __init__(self, name: str, precision: str = 'bf16', 
                 weight_precision: str = None, 
                 activation_precision: str = None):
        self.name = name
        self.precision = precision  # Default/legacy precision
        self.weight_precision = weight_precision or precision
        self.activation_precision = activation_precision or precision
    
    def get_bytes_per_element(self, precision: str) -> float:
        """Get bytes per element for given precision."""
        return PRECISION_BYTES.get(precision, 2)
```

### Enhanced AttentionOperator

The `AttentionOperator` will support separate precisions for different components:

```python
class AttentionOperator(BaseOperator):
    def __init__(self, hidden_size: int, num_heads: int, 
                 num_kv_heads: int = None, head_dim: int = None,
                 precision: str = 'bf16',
                 weight_precision: str = None,
                 activation_precision: str = None, 
                 kv_cache_precision: str = None,
                 batch_size: int = 1, sequence_length: int = 1):
        super().__init__(name="Attention", precision=precision,
                        weight_precision=weight_precision,
                        activation_precision=activation_precision)
        self.kv_cache_precision = kv_cache_precision or activation_precision or precision
        # ... existing initialization
```

### Enhanced MatMulOperator

The `MatMulOperator` will handle mixed precision for input, weight, and output:

```python
class MatMulOperator(BaseOperator):
    def __init__(self, M: int, N: int, K: int, 
                 precision: str = 'bf16',
                 input_precision: str = None,
                 weight_precision: str = None,
                 output_precision: str = None):
        super().__init__(name="MatMul", precision=precision)
        self.input_precision = input_precision or precision
        self.weight_precision = weight_precision or precision  
        self.output_precision = output_precision or precision
        # ... existing initialization
```

### Enhanced Model Classes

Both `DenseModel` and `MoEModel` will support mixed precision parameters:

```python
def compute_memory_requirements(self, sequence_length: int = 2048, 
                              batch_size: int = 1, 
                              # Legacy parameter for backward compatibility
                              dtype: str = 'bf16',
                              # New mixed precision parameters
                              weight_dtype: str = None,
                              activation_dtype: str = None,
                              kv_cache_dtype: str = None,
                              expert_parameter_dtype: str = None,  # MoE only
                              attention_parameter_dtype: str = None,
                              grad_dtype: str = None,
                              optimizer_dtype: str = None,
                              training: bool = False,
                              include_kv_cache: bool = False) -> Dict[str, int]:
```

## Data Models

### Mixed Precision Configuration

```python
@dataclass
class MixedPrecisionConfig:
    """Configuration for mixed precision settings."""
    weight_dtype: str = 'bf16'
    activation_dtype: str = 'bf16' 
    kv_cache_dtype: str = 'bf16'
    expert_parameter_dtype: str = 'fp8'  # For MoE models
    attention_parameter_dtype: str = 'bf16'
    grad_dtype: str = 'fp16'
    optimizer_dtype: str = 'fp32'
    
    def __post_init__(self):
        """Validate precision types."""
        valid_precisions = {'fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4'}
        for field_name, value in self.__dict__.items():
            if value not in valid_precisions:
                raise ValueError(f"Invalid precision '{value}' for {field_name}")
```

### Enhanced Memory Breakdown

```python
@dataclass
class MixedPrecisionMemoryBreakdown:
    """Memory breakdown with mixed precision details."""
    total: int
    by_component: Dict[str, int]  # 'parameters', 'activations', 'kv_cache', etc.
    by_precision: Dict[str, int]  # 'fp16': bytes, 'fp8': bytes, etc.
    precision_config: MixedPrecisionConfig
    efficiency_metrics: Dict[str, float]  # Memory savings, etc.
```

## Error Handling

### Validation Strategy

1. **Precision Type Validation**: Ensure all specified precisions are supported
2. **Compatibility Validation**: Check that precision combinations are valid for the target hardware
3. **Parameter Validation**: Validate that mixed precision parameters are consistent

### Error Types

```python
class UnsupportedPrecisionError(ValueError):
    """Raised when an unsupported precision type is specified."""
    pass

class IncompatiblePrecisionError(ValueError):
    """Raised when precision combinations are incompatible."""
    pass
```

## Testing Strategy

### Unit Tests

1. **Operator Tests**: Test each operator with different precision combinations
2. **Memory Calculation Tests**: Verify accurate memory calculations for mixed precision
3. **Model Tests**: Test both dense and MoE models with mixed precision configurations
4. **Backward Compatibility Tests**: Ensure existing code continues to work

### Integration Tests

1. **End-to-End Tests**: Test complete workflows with mixed precision
2. **API Tests**: Test web API endpoints with mixed precision parameters
3. **Performance Tests**: Validate that mixed precision calculations are efficient

### Test Data

```python
MIXED_PRECISION_TEST_CASES = [
    {
        'name': 'all_bf16',
        'config': {'weight_dtype': 'bf16', 'activation_dtype': 'bf16', 'kv_cache_dtype': 'bf16'}
    },
    {
        'name': 'mixed_precision_optimal',
        'config': {'weight_dtype': 'bf16', 'activation_dtype': 'bf16', 'kv_cache_dtype': 'fp8', 'expert_parameter_dtype': 'fp8'}
    },
    {
        'name': 'aggressive_quantization',
        'config': {'weight_dtype': 'int8', 'activation_dtype': 'bf16', 'kv_cache_dtype': 'fp8', 'expert_parameter_dtype': 'fp4'}
    }
]
```

## Implementation Phases

### Phase 1: Core Infrastructure
- Enhance `BaseOperator` with mixed precision support
- Update `PRECISION_BYTES` mapping
- Add validation utilities

### Phase 2: Operator Updates
- Update `MatMulOperator` for mixed precision
- Update `AttentionOperator` with KV cache precision
- Update `MLPOperator` and `MoEOperator`

### Phase 3: Model Integration
- Update `DenseModel.compute_memory_requirements()`
- Update `MoEModel.compute_memory_requirements()`
- Ensure backward compatibility

### Phase 4: Memory Calculator Enhancement
- Update `MemoryCalculator` for mixed precision
- Add mixed precision memory breakdown methods
- Update efficiency calculations

### Phase 5: API and Testing
- Update web API endpoints
- Add comprehensive test coverage
- Performance optimization

## Backward Compatibility

The design ensures backward compatibility through:

1. **Default Parameters**: All new parameters have sensible defaults
2. **Legacy Parameter Support**: The existing `dtype` parameter continues to work
3. **Graceful Fallback**: When mixed precision parameters are not specified, the system falls back to legacy behavior
4. **API Versioning**: Web API maintains existing endpoints while adding new mixed precision endpoints