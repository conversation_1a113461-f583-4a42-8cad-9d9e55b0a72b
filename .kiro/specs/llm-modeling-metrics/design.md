# Design Document

## Overview

The LLM Modeling Metrics package is designed as a comprehensive Python library for analyzing computational requirements and performance characteristics of Large Language Models. The system builds upon the existing foundation in `modeling_llm` and extends it into a full-featured package with web interface capabilities.

The package follows a modular architecture with clear separation between model analysis, parallel strategy computation, comparison utilities, and web interface components. It supports both dense models (like Llama) and Mixture of Experts (MoE) models (like DeepSeek V3) with extensible architecture for adding new model types.

## Architecture

### Core Components

```
llm_modeling_metrics/
├── core/
│   ├── base_model.py          # Abstract base class for all models
│   ├── model_factory.py       # Factory for creating model instances
│   ├── config_manager.py      # Configuration fetching and caching
│   └── parallel_strategies.py # Tensor parallelism calculations
├── models/
│   ├── dense_model.py         # Dense model implementations (Llama, etc.)
│   ├── moe_model.py          # MoE model implementations (DeepSeek, etc.)
│   └── custom_models/        # Directory for custom model extensions
├── metrics/
│   ├── flops_calculator.py    # FLOP computation utilities
│   ├── memory_calculator.py   # Memory requirement calculations
│   └── shape_analyzer.py      # Matrix shape analysis
├── comparison/
│   ├── comparator.py          # Model comparison engine
│   └── exporters.py           # Export utilities (JSON, CSV, Excel)
├── web/
│   ├── app.py                 # FastAPI web application
│   ├── api/                   # REST API endpoints
│   └── static/                # Frontend assets
└── utils/
    ├── caching.py             # Caching utilities
    └── validation.py          # Input validation
```

### Data Flow

1. **Model Configuration**: AutoConfig fetches model configurations from HuggingFace
2. **Model Instantiation**: Factory creates appropriate model class based on architecture
3. **Metric Computation**: Model classes compute FLOPs, memory, and shapes
4. **Parallel Analysis**: Tensor parallel strategies modify computations
5. **Comparison**: Multiple models are analyzed and compared
6. **Export/Visualization**: Results are exported or displayed via web interface

## Components and Interfaces

### Base Model Interface

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

@dataclass
class ModelMetrics:
    total_params: int
    flops_forward: int
    flops_per_token: int
    memory_params: int
    memory_activations: int
    attention_shapes: Dict[str, Tuple[int, ...]]
    mlp_shapes: Dict[str, Tuple[int, ...]]

@dataclass
class ParallelConfig:
    tensor_parallel_size: int = 1
    pipeline_parallel_size: int = 1
    data_parallel_size: int = 1

class BaseModel(ABC):
    def __init__(self, model_name: str, config: Optional[Any] = None):
        self.model_name = model_name
        self.config = config or self._fetch_config()
        self._parse_config()
    
    @abstractmethod
    def compute_attention_params(self) -> int:
        """Compute parameters in attention layers"""
        pass
    
    @abstractmethod
    def compute_mlp_params(self) -> int:
        """Compute parameters in MLP layers"""
        pass
    
    @abstractmethod
    def compute_flops(self, sequence_length: int = 2048) -> Dict[str, int]:
        """Compute FLOPs for forward pass"""
        pass
    
    @abstractmethod
    def get_matrix_shapes(self, parallel_config: ParallelConfig) -> Dict[str, Any]:
        """Get matrix shapes under parallel configuration"""
        pass
    
    def get_metrics(self, sequence_length: int = 2048, 
                   parallel_config: Optional[ParallelConfig] = None) -> ModelMetrics:
        """Get comprehensive model metrics"""
        pass
```

### Model Factory

```python
class ModelFactory:
    _model_registry = {
        'llama': DenseModel,
        'deepseek': MoEModel,
        'qwen': DenseModel,
        # Extensible registry
    }
    
    @classmethod
    def create_model(cls, model_name: str, config: Optional[Any] = None) -> BaseModel:
        """Create appropriate model instance based on architecture"""
        pass
    
    @classmethod
    def register_model(cls, architecture: str, model_class: type):
        """Register new model architecture"""
        pass
```

### Configuration Manager

```python
class ConfigManager:
    def __init__(self, cache_dir: str = "~/.modeling_llm/model_configs", 
                 token: Optional[str] = None):
        self.cache_dir = cache_dir
        self.token = token
        self.proxies = self._get_proxies()
    
    def fetch_config(self, model_name: str, force_refresh: bool = False) -> Dict[str, Any]:
        """Fetch and cache model configuration"""
        pass
    
    def fetch_modeling_file(self, model_name: str) -> Optional[str]:
        """Fetch modeling file from HuggingFace"""
        pass
    
    def get_cached_config(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration from cache"""
        pass
```

### Parallel Strategy Calculator

```python
class ParallelStrategyCalculator:
    @staticmethod
    def compute_tensor_parallel_shapes(base_shapes: Dict[str, Tuple[int, ...]], 
                                     tp_size: int) -> Dict[str, Tuple[int, ...]]:
        """Compute matrix shapes under tensor parallelism"""
        pass
    
    @staticmethod
    def validate_parallel_config(config: ParallelConfig, model_config: Dict[str, Any]) -> bool:
        """Validate that parallel configuration is feasible"""
        pass
    
    @staticmethod
    def compute_communication_volume(model: BaseModel, parallel_config: ParallelConfig) -> Dict[str, int]:
        """Compute communication requirements for parallel execution"""
        pass
```

## Data Models

### Model Configuration Schema

```python
@dataclass
class ModelConfig:
    model_name: str
    architecture: str
    hidden_size: int
    num_layers: int
    num_attention_heads: int
    num_key_value_heads: Optional[int]
    intermediate_size: int
    vocab_size: int
    max_position_embeddings: int
    
    # MoE specific
    num_experts: Optional[int] = None
    experts_per_token: Optional[int] = None
    shared_experts: Optional[int] = None
    
    # Architecture specific parameters
    extra_params: Dict[str, Any] = None
```

### Comparison Results Schema

```python
@dataclass
class ComparisonResult:
    models: List[str]
    metrics: Dict[str, List[Any]]  # metric_name -> [values for each model]
    parallel_configs: List[ParallelConfig]
    timestamp: datetime
    
    def to_dataframe(self) -> pd.DataFrame:
        """Convert to pandas DataFrame for analysis"""
        pass
    
    def export_excel(self, filename: str):
        """Export to Excel with multiple sheets"""
        pass
    
    def export_json(self, filename: str):
        """Export to JSON format"""
        pass
```

## Error Handling

### Exception Hierarchy

```python
class LLMModelingError(Exception):
    """Base exception for LLM modeling package"""
    pass

class ConfigurationError(LLMModelingError):
    """Raised when model configuration is invalid or unavailable"""
    pass

class ParallelConfigError(LLMModelingError):
    """Raised when parallel configuration is invalid"""
    pass

class ModelNotSupportedError(LLMModelingError):
    """Raised when model architecture is not supported"""
    pass

class ComputationError(LLMModelingError):
    """Raised when metric computation fails"""
    pass
```

### Error Handling Strategy

1. **Configuration Errors**: Graceful fallback to cached configurations, clear error messages
2. **Network Errors**: Retry logic with exponential backoff for HuggingFace API calls
3. **Computation Errors**: Validate inputs before computation, provide detailed error context
4. **Parallel Configuration Errors**: Validate divisibility constraints, suggest valid alternatives

## Testing Strategy

### Unit Testing

```python
# Test structure
tests/
├── test_base_model.py         # Base model interface tests
├── test_dense_model.py        # Dense model implementation tests
├── test_moe_model.py          # MoE model implementation tests
├── test_parallel_strategies.py # Parallel computation tests
├── test_comparator.py         # Comparison engine tests
├── test_web_api.py           # Web API endpoint tests
└── fixtures/
    ├── mock_configs.py        # Mock model configurations
    └── test_data.py          # Test data generators
```

### Integration Testing

1. **End-to-End Model Analysis**: Test complete workflow from model name to metrics
2. **Parallel Strategy Validation**: Test various parallel configurations
3. **Web Interface Testing**: API endpoint testing with realistic payloads
4. **Export Functionality**: Test all export formats with sample data

### Performance Testing

1. **Memory Usage**: Monitor memory consumption during large model analysis
2. **Computation Speed**: Benchmark metric calculation performance
3. **Caching Efficiency**: Test cache hit rates and performance improvements
4. **Concurrent Access**: Test web interface under concurrent load

### Test Data Strategy

```python
# Mock configurations for testing
MOCK_LLAMA_CONFIG = {
    "model_type": "llama",
    "hidden_size": 4096,
    "num_hidden_layers": 32,
    "num_attention_heads": 32,
    "intermediate_size": 11008,
    "vocab_size": 32000
}

MOCK_DEEPSEEK_CONFIG = {
    "model_type": "deepseek_v3",
    "hidden_size": 4096,
    "num_hidden_layers": 28,
    "n_routed_experts": 64,
    "num_experts_per_tok": 6,
    "n_shared_experts": 2
}
```

## Web Interface Architecture

### Backend API Design

```python
# FastAPI application structure
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

class ModelAnalysisRequest(BaseModel):
    model_names: List[str]
    sequence_length: int = 2048
    parallel_config: Optional[ParallelConfig] = None

class ModelAnalysisResponse(BaseModel):
    results: Dict[str, ModelMetrics]
    comparison: ComparisonResult
    execution_time: float

@app.post("/api/analyze")
async def analyze_models(request: ModelAnalysisRequest) -> ModelAnalysisResponse:
    """Analyze multiple models and return metrics"""
    pass

@app.get("/api/models/supported")
async def get_supported_models() -> List[str]:
    """Get list of supported model architectures"""
    pass
```

### Frontend Components

1. **Model Selection Interface**: Multi-select dropdown with search functionality
2. **Configuration Panel**: Parallel strategy and sequence length configuration
3. **Results Dashboard**: Interactive charts and tables for metric comparison
4. **Export Controls**: Buttons for downloading results in various formats

### Real-time Features

1. **Progress Tracking**: WebSocket connection for analysis progress updates
2. **Caching Status**: Display cache hit/miss information
3. **Error Notifications**: Real-time error reporting with suggested fixes

## Performance Considerations

### Caching Strategy

1. **Configuration Caching**: Cache model configurations with TTL
2. **Computation Caching**: Cache computed metrics for common configurations
3. **Result Caching**: Cache comparison results for frequently requested model sets

### Memory Management

1. **Lazy Loading**: Load model configurations only when needed
2. **Memory Pooling**: Reuse computation objects across analyses
3. **Garbage Collection**: Explicit cleanup of large intermediate results

### Scalability

1. **Async Processing**: Use asyncio for concurrent model analysis
2. **Worker Processes**: Distribute computation across multiple processes
3. **Database Integration**: Optional database backend for large-scale deployments

## Security Considerations

1. **API Token Management**: Secure storage and rotation of HuggingFace tokens
2. **Input Validation**: Comprehensive validation of all user inputs
3. **Rate Limiting**: Prevent abuse of HuggingFace API through rate limiting
4. **CORS Configuration**: Proper CORS setup for web interface

## Deployment Architecture

### Package Distribution

```python
# setup.py configuration
setup(
    name="llm-modeling-metrics",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "transformers>=4.30.0",
        "torch>=2.0.0",
        "fastapi>=0.100.0",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "requests>=2.31.0",
        "openpyxl>=3.1.0"
    ],
    extras_require={
        "web": ["uvicorn>=0.23.0", "jinja2>=3.1.0"],
        "dev": ["pytest>=7.0.0", "black>=23.0.0", "mypy>=1.5.0"]
    }
)
```

### Docker Configuration

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "llm_modeling_metrics.web.app:app", "--host", "0.0.0.0", "--port", "8000"]
```

This design provides a solid foundation for building the comprehensive LLM modeling metrics package while maintaining extensibility and performance.