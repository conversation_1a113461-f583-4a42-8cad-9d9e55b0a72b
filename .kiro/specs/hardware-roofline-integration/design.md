# Design Document

## Overview

The hardware roofline integration feature extends the existing LLM modeling metrics web interface with comprehensive hardware-aware performance analysis. The design integrates the GPU module's hardware specifications and roofline plotting capabilities with the operator timing analysis from the core operators module. This creates a unified platform for understanding both theoretical hardware limits and actual operator performance characteristics.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Web Interface Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  Hardware Selector │ Roofline Visualizer │ Timing Dashboard    │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    API Layer                                    │
├─────────────────────────────────────────────────────────────────┤
│  /hardware/*      │ /roofline/*        │ /operator-timing/*    │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Service Layer                                │
├─────────────────────────────────────────────────────────────────┤
│ HardwareService   │ RooflineService    │ OperatorTimingService │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Integration Layer                            │
├─────────────────────────────────────────────────────────────────┤
│ HardwareAdapter   │ RooflinePlotter    │ TimingCalculator      │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Existing Modules                             │
├─────────────────────────────────────────────────────────────────┤
│ gpu/hardware.py   │ gpu/plot_roofline.py │ core/operators.py   │
└─────────────────────────────────────────────────────────────────┘
```

### Component Integration Strategy

The design follows an adapter pattern to integrate existing modules without breaking changes:

1. **HardwareAdapter**: Wraps gpu/hardware.py classes to provide web-friendly interfaces
2. **RooflinePlotter**: Adapts gpu/plot_roofline.py for web-based interactive visualization
3. **TimingCalculator**: Extends core/operators.py timing methods with hardware-specific optimizations

## Components and Interfaces

### 1. Hardware Service Layer

#### HardwareService
```python
class HardwareService:
    """Service for managing hardware specifications and selection."""
    
    def get_available_hardware(self) -> Dict[str, List[HardwareSpec]]
    def get_hardware_specs(self, hardware_id: str) -> HardwareSpec
    def validate_hardware_compatibility(self, hardware_id: str, operators: List[BaseOperator]) -> ValidationResult
    def get_hardware_recommendations(self, workload_profile: WorkloadProfile) -> List[HardwareRecommendation]
```

#### HardwareSpec (Data Model)
```python
@dataclass
class HardwareSpec:
    id: str
    name: str
    type: str  # 'gpu' or 'npu'
    peak_flops: Dict[str, float]  # precision -> TFLOPS
    memory_bandwidth_gbps: float
    memory_size_gb: int
    tensor_cores: Optional[int]
    l2_cache_mb: Optional[int]
    supported_precisions: List[str]
    roofline_params: RooflineParams
```

### 2. Roofline Visualization Service

#### RooflineService
```python
class RooflineService:
    """Service for generating roofline visualizations."""
    
    def generate_roofline_data(self, hardware_specs: List[HardwareSpec], 
                              precisions: List[str]) -> RooflineData
    def plot_operators_on_roofline(self, operators: List[BaseOperator], 
                                  hardware: HardwareSpec) -> RooflinePlotData
    def compare_hardware_rooflines(self, hardware_list: List[HardwareSpec], 
                                  operators: List[BaseOperator]) -> ComparisonPlotData
    def calculate_knee_points(self, hardware: HardwareSpec) -> Dict[str, KneePoint]
```

#### RooflineData (Data Model)
```python
@dataclass
class RooflineData:
    operational_intensity_range: np.ndarray
    performance_curves: Dict[str, Dict[str, np.ndarray]]  # hardware_id -> precision -> performance
    knee_points: Dict[str, Dict[str, KneePoint]]  # hardware_id -> precision -> knee_point
    operator_points: List[OperatorPoint]
```

### 3. Operator Timing Service

#### OperatorTimingService
```python
class OperatorTimingService:
    """Service for computing hardware-aware operator timing."""
    
    def compute_operator_timing(self, operator: BaseOperator, 
                               hardware: HardwareSpec, **kwargs) -> OperatorTiming
    def analyze_bottlenecks(self, operators: List[BaseOperator], 
                           hardware: HardwareSpec) -> BottleneckAnalysis
    def suggest_optimizations(self, timing_results: List[OperatorTiming], 
                             hardware: HardwareSpec) -> List[OptimizationSuggestion]
    def compare_across_hardware(self, operators: List[BaseOperator], 
                               hardware_list: List[HardwareSpec]) -> TimingComparison
```

#### OperatorTiming (Data Model)
```python
@dataclass
class OperatorTiming:
    operator_name: str
    hardware_id: str
    compute_time_ms: float
    memory_time_ms: float
    execution_time_ms: float  # max(compute_time, memory_time)
    bottleneck_type: str  # 'compute' or 'memory'
    utilization_percent: float
    operational_intensity: float
    flops: int
    memory_movement_bytes: int
    precision_overhead_factor: float
    optimization_opportunities: List[str]
```

### 4. Web API Endpoints

#### Hardware Endpoints
```python
# GET /api/hardware/list
# Response: List[HardwareSpec]

# GET /api/hardware/{hardware_id}/specs
# Response: HardwareSpec

# POST /api/hardware/validate
# Request: {"hardware_id": str, "operators": List[OperatorConfig]}
# Response: ValidationResult
```

#### Roofline Endpoints
```python
# POST /api/roofline/generate
# Request: {"hardware_ids": List[str], "precisions": List[str]}
# Response: RooflineData

# POST /api/roofline/plot-operators
# Request: {"operators": List[OperatorConfig], "hardware_id": str}
# Response: RooflinePlotData

# POST /api/roofline/compare
# Request: {"hardware_ids": List[str], "operators": List[OperatorConfig]}
# Response: ComparisonPlotData
```

#### Timing Analysis Endpoints
```python
# POST /api/timing/analyze
# Request: {"operators": List[OperatorConfig], "hardware_id": str}
# Response: List[OperatorTiming]

# POST /api/timing/bottlenecks
# Request: {"operators": List[OperatorConfig], "hardware_id": str}
# Response: BottleneckAnalysis

# POST /api/timing/compare-hardware
# Request: {"operators": List[OperatorConfig], "hardware_ids": List[str]}
# Response: TimingComparison
```

## Data Models

### Core Data Structures

#### WorkloadProfile
```python
@dataclass
class WorkloadProfile:
    model_type: str  # 'dense', 'moe'
    batch_size: int
    sequence_length: int
    precision_requirements: List[str]
    memory_constraints: Optional[int]  # GB
    latency_requirements: Optional[float]  # ms
```

#### KneePoint
```python
@dataclass
class KneePoint:
    operational_intensity: float  # FLOP/Byte
    performance_tflops: float
    precision: str
    hardware_id: str
```

#### OperatorPoint
```python
@dataclass
class OperatorPoint:
    operator_name: str
    operational_intensity: float
    achieved_performance_tflops: float
    utilization_percent: float
    is_compute_bound: bool
```

#### OptimizationSuggestion
```python
@dataclass
class OptimizationSuggestion:
    operator_name: str
    suggestion_type: str  # 'precision', 'tensor_core', 'memory_layout', 'batching'
    description: str
    expected_improvement_percent: float
    implementation_complexity: str  # 'low', 'medium', 'high'
```

## Error Handling

### Hardware Validation Errors
- **UnsupportedHardwareError**: When hardware specifications are incomplete or invalid
- **PrecisionCompatibilityError**: When requested precision is not supported by hardware
- **MemoryConstraintError**: When operator memory requirements exceed hardware capacity

### Timing Calculation Errors
- **InsufficientHardwareDataError**: When hardware specs lack required performance metrics
- **OperatorConfigurationError**: When operator parameters are invalid for timing calculation
- **PrecisionOverheadError**: When mixed precision overhead cannot be calculated

### Visualization Errors
- **RooflineGenerationError**: When roofline data cannot be generated due to missing specs
- **PlotDataError**: When operator points cannot be positioned on roofline due to invalid metrics

## Testing Strategy

### Unit Testing
1. **Hardware Service Tests**: Validate hardware spec loading, validation, and recommendation logic
2. **Timing Calculation Tests**: Verify operator timing calculations against known benchmarks
3. **Roofline Generation Tests**: Test roofline curve generation and knee point calculations
4. **API Endpoint Tests**: Validate request/response handling and error cases

### Integration Testing
1. **Hardware-Operator Integration**: Test timing calculations with real hardware specs and operators
2. **Roofline-Timing Integration**: Verify operator positioning on roofline plots
3. **Web Interface Integration**: Test end-to-end workflows from hardware selection to visualization

### Performance Testing
1. **Large Model Analysis**: Test with models containing hundreds of operators
2. **Multiple Hardware Comparison**: Validate performance with 10+ hardware platforms
3. **Real-time Updates**: Test interactive controls with sub-second response times

### Validation Testing
1. **Hardware Spec Accuracy**: Compare calculated metrics with published hardware benchmarks
2. **Roofline Model Validation**: Verify roofline curves match theoretical expectations
3. **Timing Accuracy**: Validate operator timing estimates against actual measurements where possible

## Implementation Phases

### Phase 1: Core Integration (Foundation)
- Implement HardwareAdapter to wrap existing gpu/hardware.py classes
- Create HardwareService with basic hardware spec management
- Add hardware selection API endpoints
- Integrate hardware specs loading into web interface

### Phase 2: Timing Analysis (Core Feature)
- Extend OperatorTimingService with hardware-aware calculations
- Implement bottleneck analysis and optimization suggestions
- Add timing analysis API endpoints
- Create timing dashboard in web interface

### Phase 3: Roofline Visualization (Advanced Feature)
- Adapt gpu/plot_roofline.py for web-based interactive visualization
- Implement RooflineService with operator positioning
- Add roofline visualization API endpoints
- Create interactive roofline plots in web interface

### Phase 4: Comparison and Optimization (Enhancement)
- Implement multi-hardware comparison features
- Add optimization suggestion engine
- Create comparative visualization dashboards
- Add export capabilities for analysis results

Each phase builds upon the previous one, ensuring incremental value delivery while maintaining system stability.