"""
Comprehensive unit tests for HardwareService with mock hardware specifications.
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from dataclasses import dataclass
from typing import List, Dict, Any

from llm_modeling_metrics.hardware.service import HardwareService
from llm_modeling_metrics.hardware.models import (
    HardwareSpec, HardwareType, WorkloadProfile, HardwareRecommendation
)
from llm_modeling_metrics.hardware.validation import Val<PERSON><PERSON><PERSON><PERSON>ult
from llm_modeling_metrics.core.operators import BaseOperator


@pytest.fixture
def comprehensive_hardware_specs():
    """Create comprehensive hardware specifications for testing."""
    return {
        "gpus": {
            "h100": {
                "name": "NVIDIA H100",
                "architecture": "Hopper",
                "memory_size_gb": 80,
                "memory_bandwidth_gbps": 3350,
                "tensor_performance": {
                    "fp16_tensor": 1979.0,
                    "bf16_tensor": 1979.0,
                    "fp8_tensor": 3958.0,
                    "int8_tensor": 3958.0
                },
                "vector_performance": {
                    "fp32": 67.0,
                    "fp16": 134.0,
                    "bf16": 134.0
                },
                "tensor_cores": 528,
                "l2_cache_mb": 50,
                "tdp_watts": 700,
                "manufacturing_process": "4nm",
                "pcie_generation": 5,
                "nvlink_bandwidth_gbps": 900
            },
            "h20": {
                "name": "NVIDIA H20",
                "architecture": "Hopper",
                "memory_size_gb": 96,
                "memory_bandwidth_gbps": 4000,
                "tensor_performance": {
                    "fp16_tensor": 1200.0,
                    "bf16_tensor": 1200.0,
                    "fp8_tensor": 2400.0
                },
                "vector_performance": {
                    "fp32": 40.0,
                    "fp16": 80.0,
                    "bf16": 80.0
                },
                "tensor_cores": 456,
                "l2_cache_mb": 60,
                "tdp_watts": 500
            },
            "rtx4090": {
                "name": "NVIDIA RTX 4090",
                "architecture": "Ada Lovelace",
                "memory_size_gb": 24,
                "memory_bandwidth_gbps": 1008,
                "tensor_performance": {
                    "fp16_tensor": 165.0,
                    "bf16_tensor": 165.0
                },
                "vector_performance": {
                    "fp32": 83.0,
                    "fp16": 166.0,
                    "bf16": 166.0
                },
                "tensor_cores": 128,
                "l2_cache_mb": 72,
                "tdp_watts": 450
            }
        },
        "npus": {
            "ascend_910b": {
                "name": "Huawei Ascend 910B",
                "architecture": "Da Vinci 3.0",
                "memory_size_gb": 64,
                "memory_bandwidth_gbps": 2400,
                "tensor_performance": {
                    "fp16_tensor": 640.0,
                    "bf16_tensor": 640.0,
                    "int8_tensor": 1280.0,
                    "int4_tensor": 2560.0
                },
                "vector_performance": {
                    "fp32": 32.0,
                    "fp16": 64.0,
                    "bf16": 64.0
                },
                "tdp_watts": 310,
                "ai_cores": 32
            },
            "ascend_310p": {
                "name": "Huawei Ascend 310P",
                "architecture": "Da Vinci 2.0",
                "memory_size_gb": 8,
                "memory_bandwidth_gbps": 200,
                "tensor_performance": {
                    "fp16_tensor": 22.0,
                    "int8_tensor": 44.0,
                    "int4_tensor": 88.0
                },
                "vector_performance": {
                    "fp32": 5.5,
                    "fp16": 11.0
                },
                "tdp_watts": 20,
                "ai_cores": 8
            }
        }
    }


@pytest.fixture
def temp_comprehensive_specs_file(comprehensive_hardware_specs):
    """Create temporary comprehensive hardware specs file."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(comprehensive_hardware_specs, f)
        temp_file = f.name
    
    yield temp_file
    
    # Cleanup
    Path(temp_file).unlink()


@pytest.fixture
def mock_operators():
    """Create mock operators for testing."""
    class MockOperator(BaseOperator):
        def __init__(self, name: str, operator_type: str, precision: str = 'bf16', 
                     parameters: int = 1000000, memory_usage: int = 4000000):
            super().__init__(name, precision)
            self.operator_type = operator_type
            self.parameters = parameters
            self.memory_usage_bytes = memory_usage
        
        def compute_flops(self, **kwargs) -> int:
            return self.parameters * 2  # Simple FLOP calculation
        
        def compute_memory_capacity_bytes(self, **kwargs) -> int:
            return self.memory_usage_bytes
        
        def compute_memory_movement_bytes(self, **kwargs) -> int:
            return self.memory_usage_bytes
        
        def compute_params(self, **kwargs) -> int:
            return self.parameters
    
    return [
        MockOperator("attention_1", "attention", "bf16", 100000000, 400000000),
        MockOperator("mlp_1", "mlp", "bf16", 200000000, 800000000),
        MockOperator("moe_gate", "moe", "fp16", 50000000, 200000000),
        MockOperator("embedding", "embedding", "fp32", 300000000, 1200000000)
    ]


class TestHardwareServiceComprehensive:
    """Comprehensive test cases for HardwareService."""
    
    def test_initialization_success(self, temp_comprehensive_specs_file):
        """Test successful service initialization with comprehensive specs."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        assert service is not None
        assert service.adapter is not None
        assert service.specs_file_path == temp_comprehensive_specs_file
        
        # Verify hardware was loaded
        hardware = service.get_available_hardware()
        assert len(hardware["gpu"]) == 3
        assert len(hardware["npu"]) == 2
    
    def test_initialization_file_not_found(self):
        """Test initialization with non-existent specs file."""
        with pytest.raises(RuntimeError, match="Failed to load hardware specifications"):
            HardwareService("non_existent_file.yaml")
    
    def test_initialization_invalid_yaml(self):
        """Test initialization with invalid YAML file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_file = f.name
        
        try:
            with pytest.raises(RuntimeError, match="Failed to load hardware specifications"):
                HardwareService(temp_file)
        finally:
            Path(temp_file).unlink()
    
    def test_get_available_hardware_structure(self, temp_comprehensive_specs_file):
        """Test structure of available hardware response."""
        service = HardwareService(temp_comprehensive_specs_file)
        hardware = service.get_available_hardware()
        
        # Check structure
        assert isinstance(hardware, dict)
        assert "gpu" in hardware
        assert "npu" in hardware
        assert isinstance(hardware["gpu"], list)
        assert isinstance(hardware["npu"], list)
        
        # Check GPU specs
        gpu_specs = hardware["gpu"]
        assert len(gpu_specs) == 3
        
        h100_spec = next((spec for spec in gpu_specs if spec.id == "h100"), None)
        assert h100_spec is not None
        assert h100_spec.name == "NVIDIA H100"
        assert h100_spec.type == HardwareType.GPU
        assert h100_spec.memory_size_gb == 80
        assert h100_spec.memory_bandwidth_gbps == 3350
        assert h100_spec.tensor_cores == 528
        assert "fp16" in h100_spec.supported_precisions
        assert "bf16" in h100_spec.supported_precisions
        assert "fp8" in h100_spec.supported_precisions
        
        # Check NPU specs
        npu_specs = hardware["npu"]
        assert len(npu_specs) == 2
        
        ascend_spec = next((spec for spec in npu_specs if spec.id == "ascend_910b"), None)
        assert ascend_spec is not None
        assert ascend_spec.name == "Huawei Ascend 910B"
        assert ascend_spec.type == HardwareType.NPU
        assert ascend_spec.memory_size_gb == 64
        assert ascend_spec.memory_bandwidth_gbps == 2400
    
    def test_get_hardware_specs_all_variants(self, temp_comprehensive_specs_file):
        """Test getting specs for all hardware variants."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test all GPU variants
        gpu_ids = ["h100", "h20", "rtx4090"]
        for gpu_id in gpu_ids:
            spec = service.get_hardware_specs(gpu_id)
            assert spec is not None
            assert spec.id == gpu_id
            assert spec.type == HardwareType.GPU
            assert spec.memory_size_gb > 0
            assert spec.memory_bandwidth_gbps > 0
            assert len(spec.supported_precisions) > 0
        
        # Test all NPU variants
        npu_ids = ["ascend_910b", "ascend_310p"]
        for npu_id in npu_ids:
            spec = service.get_hardware_specs(npu_id)
            assert spec is not None
            assert spec.id == npu_id
            assert spec.type == HardwareType.NPU
            assert spec.memory_size_gb > 0
            assert spec.memory_bandwidth_gbps > 0
    
    def test_get_hardware_specs_performance_metrics(self, temp_comprehensive_specs_file):
        """Test hardware specs performance metrics calculation."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test H100 performance metrics
        h100_spec = service.get_hardware_specs("h100")
        assert h100_spec.get_peak_flops("fp16") == 134.0  # Vector performance
        assert h100_spec.get_peak_flops("bf16") == 134.0
        assert h100_spec.get_peak_flops("fp32") == 67.0
        assert h100_spec.get_tensor_flops("fp16") == 1979.0
        assert h100_spec.get_tensor_flops("bf16") == 1979.0
        assert h100_spec.get_tensor_flops("fp8") == 3958.0
        
        # Test memory bandwidth
        assert h100_spec.get_memory_bandwidth_bps() == 3350 * 1e9
        
        # Test tensor core capability
        assert h100_spec.is_tensor_core_capable()
        assert h100_spec.tensor_cores == 528
    
    def test_validate_hardware_compatibility_success_scenarios(self, temp_comprehensive_specs_file, mock_operators):
        """Test successful hardware compatibility validation scenarios."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test with H100 (high-end GPU)
        result = service.validate_hardware_compatibility("h100", mock_operators[:2])  # Small subset
        assert result.is_valid
        assert len(result.errors) == 0
        
        # Test with Ascend 910B (high-end NPU)
        result = service.validate_hardware_compatibility("ascend_910b", mock_operators[:2])
        assert result.is_valid
        assert len(result.errors) == 0
        
        # Test with empty operators list
        result = service.validate_hardware_compatibility("h100", [])
        assert result.is_valid
    
    def test_validate_hardware_compatibility_memory_constraints(self, temp_comprehensive_specs_file, mock_operators):
        """Test hardware compatibility validation with memory constraints."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Create operators with very high memory usage
        high_memory_operators = []
        for i in range(5):
            op = Mock()
            op.operator_type = "attention"
            op.precision = "fp32"
            op.parameters = 10000000000  # 10B parameters
            op.memory_usage_bytes = 40000000000  # 40GB per operator
            high_memory_operators.append(op)
        
        # Test with RTX 4090 (24GB memory) - should have memory warnings/errors
        result = service.validate_hardware_compatibility("rtx4090", high_memory_operators)
        assert len(result.errors) > 0 or len(result.warnings) > 0
        
        # Test with H100 (80GB memory) - might still have issues with 5x40GB operators
        result = service.validate_hardware_compatibility("h100", high_memory_operators)
        # Should have at least warnings about memory usage
        assert len(result.warnings) > 0 or len(result.errors) > 0
    
    def test_validate_hardware_compatibility_precision_support(self, temp_comprehensive_specs_file):
        """Test hardware compatibility validation for precision support."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Create operator with unsupported precision
        unsupported_op = Mock()
        unsupported_op.operator_type = "attention"
        unsupported_op.precision = "fp64"  # Not typically supported by accelerators
        unsupported_op.parameters = 1000000
        unsupported_op.memory_usage_bytes = 4000000
        
        result = service.validate_hardware_compatibility("h100", [unsupported_op])
        # Should have warnings about precision support
        assert len(result.warnings) > 0 or len(result.errors) > 0
    
    def test_validate_hardware_compatibility_error_cases(self, temp_comprehensive_specs_file):
        """Test hardware compatibility validation error cases."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test with non-existent hardware
        result = service.validate_hardware_compatibility("nonexistent_gpu", [])
        assert not result.is_valid
        assert len(result.errors) > 0
        assert any("not found" in error.lower() for error in result.errors)
        
        # Test with empty hardware ID
        result = service.validate_hardware_compatibility("", [])
        assert not result.is_valid
        assert len(result.errors) > 0
        
        # Test with None hardware ID
        result = service.validate_hardware_compatibility(None, [])
        assert not result.is_valid
        assert len(result.errors) > 0
    
    def test_get_hardware_recommendations_dense_workload(self, temp_comprehensive_specs_file):
        """Test hardware recommendations for dense model workload."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        workload = WorkloadProfile(
            model_type="dense",
            batch_size=32,
            sequence_length=2048,
            precision_requirements=["bf16", "fp16"],
            memory_constraints=40,  # 40GB memory requirement
            latency_requirements=100.0  # 100ms latency requirement
        )
        
        recommendations = service.get_hardware_recommendations(workload)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # Check recommendation structure
        for rec in recommendations:
            assert isinstance(rec, HardwareRecommendation)
            assert rec.hardware_id in ["h100", "h20", "rtx4090", "ascend_910b", "ascend_310p"]
            assert 0 <= rec.score <= 100
            assert isinstance(rec.reasons, list)
            assert len(rec.reasons) > 0
        
        # Recommendations should be sorted by score (descending)
        scores = [rec.score for rec in recommendations]
        assert scores == sorted(scores, reverse=True)
        
        # H100 should be highly recommended for this workload
        h100_rec = next((rec for rec in recommendations if rec.hardware_id == "h100"), None)
        assert h100_rec is not None
        assert h100_rec.score >= 80  # Should have high score
    
    def test_get_hardware_recommendations_moe_workload(self, temp_comprehensive_specs_file):
        """Test hardware recommendations for MoE model workload."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        workload = WorkloadProfile(
            model_type="moe",
            batch_size=8,
            sequence_length=4096,
            precision_requirements=["bf16"],
            memory_constraints=80,  # High memory requirement for MoE
            latency_requirements=200.0
        )
        
        recommendations = service.get_hardware_recommendations(workload)
        
        assert len(recommendations) > 0
        
        # For MoE workloads, high-memory hardware should be preferred
        high_memory_recs = [rec for rec in recommendations if rec.hardware_id in ["h100", "h20", "ascend_910b"]]
        assert len(high_memory_recs) > 0
        
        # Check that memory considerations are mentioned in reasons
        for rec in high_memory_recs:
            memory_mentioned = any("memory" in reason.lower() for reason in rec.reasons)
            assert memory_mentioned
    
    def test_get_hardware_recommendations_inference_workload(self, temp_comprehensive_specs_file):
        """Test hardware recommendations for inference workload."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        workload = WorkloadProfile(
            model_type="dense",
            batch_size=1,  # Single batch inference
            sequence_length=1024,
            precision_requirements=["fp16", "int8"],
            memory_constraints=20,  # Lower memory for inference
            latency_requirements=10.0  # Low latency requirement
        )
        
        recommendations = service.get_hardware_recommendations(workload)
        
        assert len(recommendations) > 0
        
        # For low-latency inference, recommendations should consider latency
        for rec in recommendations:
            latency_mentioned = any("latency" in reason.lower() or "inference" in reason.lower() 
                                  for reason in rec.reasons)
            # At least some recommendations should mention latency/inference considerations
        
        # RTX 4090 might be good for inference due to good performance/cost ratio
        rtx_rec = next((rec for rec in recommendations if rec.hardware_id == "rtx4090"), None)
        if rtx_rec:
            assert rtx_rec.score > 0
    
    def test_get_hardware_recommendations_edge_cases(self, temp_comprehensive_specs_file):
        """Test hardware recommendations edge cases."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test with very high memory requirement (exceeds all hardware)
        high_memory_workload = WorkloadProfile(
            model_type="dense",
            batch_size=1,
            sequence_length=1024,
            precision_requirements=["fp32"],
            memory_constraints=200,  # 200GB - exceeds all hardware
            latency_requirements=1000.0
        )
        
        recommendations = service.get_hardware_recommendations(high_memory_workload)
        
        # Should still return recommendations but with warnings
        assert len(recommendations) > 0
        for rec in recommendations:
            # Should mention memory limitations
            memory_warning = any("memory" in reason.lower() and 
                               ("insufficient" in reason.lower() or "exceeds" in reason.lower())
                               for reason in rec.reasons)
            # At least some should have memory warnings
    
    def test_reload_hardware_specifications(self, temp_comprehensive_specs_file):
        """Test hardware specifications reload functionality."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Get initial hardware count
        initial_hardware = service.get_available_hardware()
        initial_gpu_count = len(initial_hardware["gpu"])
        
        # Modify the specs file to add new hardware
        with open(temp_comprehensive_specs_file, 'r') as f:
            specs = yaml.safe_load(f)
        
        specs["gpus"]["new_gpu"] = {
            "name": "New Test GPU",
            "memory_size_gb": 32,
            "memory_bandwidth_gbps": 1000,
            "tensor_performance": {"fp16_tensor": 100},
            "vector_performance": {"fp32": 50}
        }
        
        with open(temp_comprehensive_specs_file, 'w') as f:
            yaml.dump(specs, f)
        
        # Reload specifications
        service.reload_hardware_specifications()
        
        # Verify new hardware is loaded
        updated_hardware = service.get_available_hardware()
        assert len(updated_hardware["gpu"]) == initial_gpu_count + 1
        
        new_gpu_spec = service.get_hardware_specs("new_gpu")
        assert new_gpu_spec is not None
        assert new_gpu_spec.name == "New Test GPU"
    
    def test_reload_hardware_specifications_error_handling(self, temp_comprehensive_specs_file):
        """Test hardware specifications reload error handling."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Delete the specs file
        Path(temp_comprehensive_specs_file).unlink()
        
        # Reload should handle missing file gracefully
        with pytest.raises(RuntimeError, match="Failed to reload hardware specifications"):
            service.reload_hardware_specifications()
    
    def test_caching_behavior(self, temp_comprehensive_specs_file):
        """Test caching behavior of hardware service."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # First call should load from file
        hardware1 = service.get_available_hardware()
        
        # Second call should return cached result
        hardware2 = service.get_available_hardware()
        
        # Should be the same objects (cached)
        assert hardware1 is hardware2
        
        # Individual spec calls should also be cached
        spec1 = service.get_hardware_specs("h100")
        spec2 = service.get_hardware_specs("h100")
        assert spec1 is spec2
    
    def test_error_handling_robustness(self, temp_comprehensive_specs_file):
        """Test error handling robustness."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test with malformed operator
        malformed_op = Mock()
        malformed_op.operator_type = None
        malformed_op.precision = None
        malformed_op.parameters = None
        
        # Should handle gracefully without crashing
        result = service.validate_hardware_compatibility("h100", [malformed_op])
        assert isinstance(result, ValidationResult)
        # Should have errors or warnings about malformed operator
        assert len(result.errors) > 0 or len(result.warnings) > 0
    
    def test_performance_metrics_accuracy(self, temp_comprehensive_specs_file):
        """Test accuracy of performance metrics calculations."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test H100 metrics
        h100 = service.get_hardware_specs("h100")
        
        # Test FLOPS calculations
        assert h100.get_peak_flops("fp32") == 67.0
        assert h100.get_peak_flops("fp16") == 134.0
        assert h100.get_peak_flops("bf16") == 134.0
        
        # Test tensor FLOPS
        assert h100.get_tensor_flops("fp16") == 1979.0
        assert h100.get_tensor_flops("bf16") == 1979.0
        assert h100.get_tensor_flops("fp8") == 3958.0
        
        # Test memory bandwidth conversion
        expected_bandwidth_bps = 3350 * 1e9  # Convert GB/s to B/s
        assert h100.get_memory_bandwidth_bps() == expected_bandwidth_bps
        
        # Test precision support
        assert h100.supports_precision("fp32")
        assert h100.supports_precision("fp16")
        assert h100.supports_precision("bf16")
        assert h100.supports_precision("fp8")
        assert not h100.supports_precision("fp64")  # Not in specs
    
    def test_workload_profile_validation(self, temp_comprehensive_specs_file):
        """Test WorkloadProfile validation and edge cases."""
        service = HardwareService(temp_comprehensive_specs_file)
        
        # Test valid workload profiles
        valid_profiles = [
            WorkloadProfile(
                model_type="dense",
                batch_size=1,
                sequence_length=512,
                precision_requirements=["fp16"]
            ),
            WorkloadProfile(
                model_type="moe",
                batch_size=32,
                sequence_length=4096,
                precision_requirements=["bf16", "fp8"],
                memory_constraints=80,
                latency_requirements=100.0
            )
        ]
        
        for profile in valid_profiles:
            recommendations = service.get_hardware_recommendations(profile)
            assert isinstance(recommendations, list)
            assert len(recommendations) > 0
    
    def test_concurrent_access_safety(self, temp_comprehensive_specs_file):
        """Test thread safety of hardware service operations."""
        import threading
        import time
        
        service = HardwareService(temp_comprehensive_specs_file)
        results = []
        errors = []
        
        def worker():
            try:
                # Perform various operations
                hardware = service.get_available_hardware()
                spec = service.get_hardware_specs("h100")
                workload = WorkloadProfile(
                    model_type="dense",
                    batch_size=1,
                    sequence_length=1024,
                    precision_requirements=["fp16"]
                )
                recommendations = service.get_hardware_recommendations(workload)
                results.append((hardware, spec, recommendations))
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == 5
        
        # All results should be consistent
        first_result = results[0]
        for result in results[1:]:
            assert len(result[0]["gpu"]) == len(first_result[0]["gpu"])
            assert result[1].id == first_result[1].id
            assert len(result[2]) == len(first_result[2])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])