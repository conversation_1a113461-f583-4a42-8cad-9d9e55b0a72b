"""
Pytest configuration and fixtures for LLM modeling metrics tests.
"""

import pytest
import json
from pathlib import Path
from typing import Dict, Any
from unittest.mock import Mock, MagicMock

from llm_modeling_metrics.core.base_model import ParallelConfig, ModelMetrics
from llm_modeling_metrics.core.config_manager import ConfigManager
from llm_modeling_metrics.core.model_factory import ModelFactory


class MockConfig:
    """Mock configuration for testing."""
    
    def __init__(self, config_dict: Dict[str, Any]):
        for key, value in config_dict.items():
            setattr(self, key, value)


@pytest.fixture
def mock_llama_config():
    """Mock Llama model configuration."""
    return MockConfig({
        'model_type': 'llama',
        'hidden_size': 4096,
        'num_hidden_layers': 32,
        'num_attention_heads': 32,
        'num_key_value_heads': 32,
        'intermediate_size': 11008,
        'vocab_size': 32000,
        'max_position_embeddings': 2048,
        'tie_word_embeddings': False,
        'rms_norm_eps': 1e-6,
        'architectures': ['LlamaForCausalLM']
    })


@pytest.fixture
def mock_deepseek_v3_config():
    """Mock DeepSeek V3 MoE model configuration."""
    return MockConfig({
        'model_type': 'deepseek_v3',
        'hidden_size': 7168,
        'num_hidden_layers': 61,
        'num_attention_heads': 128,
        'num_key_value_heads': 128,
        'intermediate_size': 18432,
        'vocab_size': 129280,
        'max_position_embeddings': 163840,
        'n_routed_experts': 256,
        'num_experts_per_tok': 8,
        'n_shared_experts': 2,
        'expert_layer_period': 1,
        'expert_layer_offset': 0,
        'tie_word_embeddings': False,
        'architectures': ['DeepseekV3ForCausalLM']
    })


@pytest.fixture
def mock_deepseek_v2_config():
    """Mock DeepSeek V2 MoE model configuration."""
    return MockConfig({
        'model_type': 'deepseek_v2',
        'hidden_size': 5120,
        'num_hidden_layers': 27,
        'num_attention_heads': 128,
        'num_key_value_heads': 16,
        'qk_nope_head_dim': 128,
        'qk_rope_head_dim': 64,
        'v_head_dim': 128,
        'intermediate_size': 12288,
        'vocab_size': 102400,
        'max_position_embeddings': 163840,
        'n_routed_experts': 160,
        'num_experts_per_tok': 6,
        'n_shared_experts': 2,
        'tie_word_embeddings': False,
        'architectures': ['DeepseekV2ForCausalLM']
    })


@pytest.fixture
def parallel_configs():
    """Various parallel configurations for testing."""
    return {
        'no_parallel': ParallelConfig(),
        'tensor_parallel_2': ParallelConfig(tensor_parallel_size=2),
        'tensor_parallel_4': ParallelConfig(tensor_parallel_size=4),
        'pipeline_parallel_2': ParallelConfig(pipeline_parallel_size=2),
        'data_parallel_4': ParallelConfig(data_parallel_size=4),
        'mixed_parallel': ParallelConfig(
            tensor_parallel_size=2,
            pipeline_parallel_size=2,
            data_parallel_size=2
        ),
        'moe_parallel': ParallelConfig(
            tensor_parallel_size=2,
            expert_parallel_size=4,
            expert_data_parallel_size=2
        )
    }


@pytest.fixture
def mock_config_manager():
    """Mock ConfigManager for testing."""
    config_manager = Mock(spec=ConfigManager)
    
    # Mock fetch_config method
    def mock_fetch_config(model_name: str) -> Dict[str, Any]:
        if 'llama' in model_name.lower():
            return {
                'model_type': 'llama',
                'hidden_size': 4096,
                'num_hidden_layers': 32,
                'num_attention_heads': 32,
                'num_key_value_heads': 32,
                'intermediate_size': 11008,
                'vocab_size': 32000,
                'architectures': ['LlamaForCausalLM']
            }
        elif 'deepseek' in model_name.lower():
            return {
                'model_type': 'deepseek_v3',
                'hidden_size': 7168,
                'num_hidden_layers': 61,
                'n_routed_experts': 256,
                'num_experts_per_tok': 8,
                'architectures': ['DeepseekV3ForCausalLM']
            }
        else:
            raise ValueError(f"Unknown model: {model_name}")
    
    config_manager.fetch_config.side_effect = mock_fetch_config
    return config_manager


@pytest.fixture
def sample_model_metrics():
    """Sample ModelMetrics for testing."""
    return ModelMetrics(
        model_name="test-model",
        architecture="llama",
        total_params=7000000000,
        attention_params=2000000000,
        mlp_params=4500000000,
        embedding_params=500000000,
        flops_forward=14000000000000,
        flops_per_token=7000000000,
        memory_params=28000000000,
        memory_activations=2000000000,
        memory_total=30000000000,
        attention_shapes={
            'q_proj': (4096, 4096),
            'k_proj': (4096, 4096),
            'v_proj': (4096, 4096),
            'o_proj': (4096, 4096)
        },
        mlp_shapes={
            'gate_proj': (4096, 11008),
            'up_proj': (4096, 11008),
            'down_proj': (11008, 4096)
        },
        sequence_length=2048,
        batch_size=1
    )


@pytest.fixture(autouse=True)
def clear_model_factory():
    """Clear ModelFactory registry before each test."""
    ModelFactory.clear_registry()
    yield
    ModelFactory.clear_registry()


@pytest.fixture
def temp_cache_dir(tmp_path):
    """Temporary directory for cache testing."""
    cache_dir = tmp_path / "cache"
    cache_dir.mkdir()
    return cache_dir


# Test data constants
TEST_SEQUENCE_LENGTHS = [512, 1024, 2048, 4096]
TEST_BATCH_SIZES = [1, 2, 4, 8]
TEST_TENSOR_PARALLEL_SIZES = [1, 2, 4, 8]


def create_mock_model_config(model_type: str = "llama", **overrides) -> Dict[str, Any]:
    """Create a mock model configuration with optional overrides."""
    base_configs = {
        "llama": {
            'model_type': 'llama',
            'hidden_size': 4096,
            'num_hidden_layers': 32,
            'num_attention_heads': 32,
            'num_key_value_heads': 32,
            'intermediate_size': 11008,
            'vocab_size': 32000,
            'max_position_embeddings': 2048,
            'tie_word_embeddings': False,
            'architectures': ['LlamaForCausalLM']
        },
        "deepseek_v3": {
            'model_type': 'deepseek_v3',
            'hidden_size': 7168,
            'num_hidden_layers': 61,
            'num_attention_heads': 128,
            'num_key_value_heads': 128,
            'intermediate_size': 18432,
            'moe_intermediate_size': 18432,
            'vocab_size': 129280,
            'n_routed_experts': 256,
            'num_experts_per_tok': 8,
            'n_shared_experts': 2,
            'architectures': ['DeepseekV3ForCausalLM']
        }
    }
    
    config = base_configs.get(model_type, base_configs["llama"]).copy()
    config.update(overrides)
    return config


def assert_metrics_valid(metrics: ModelMetrics):
    """Assert that ModelMetrics object has valid values."""
    assert metrics.model_name is not None
    assert metrics.architecture is not None
    assert metrics.total_params > 0
    assert metrics.attention_params >= 0
    assert metrics.mlp_params >= 0
    assert metrics.embedding_params >= 0
    assert metrics.flops_forward > 0
    assert metrics.flops_per_token > 0
    assert metrics.memory_params > 0
    assert metrics.memory_activations >= 0
    assert metrics.memory_total > 0
    assert metrics.sequence_length > 0
    assert metrics.batch_size > 0
    
    # Check that total params equals sum of components
    assert metrics.total_params == (
        metrics.attention_params + 
        metrics.mlp_params + 
        metrics.embedding_params
    )
    
    # Check that total memory includes params
    assert metrics.memory_total >= metrics.memory_params


def assert_parallel_config_valid(config: ParallelConfig):
    """Assert that ParallelConfig has valid values."""
    assert config.tensor_parallel_size >= 1
    assert config.pipeline_parallel_size >= 1
    assert config.data_parallel_size >= 1
    assert config.expert_parallel_size >= 1
    assert config.expert_data_parallel_size >= 1


def assert_flops_breakdown_valid(flops: Dict[str, int]):
    """Assert that FLOP breakdown has valid structure and values."""
    assert 'total' in flops
    assert flops['total'] > 0
    
    # Check that all values are non-negative
    for key, value in flops.items():
        assert value >= 0, f"FLOP value for {key} should be non-negative: {value}"
    
    # Check that total equals sum of components (if breakdown is provided)
    component_keys = [k for k in flops.keys() if k != 'total' and not k.endswith('_per_layer')]
    if component_keys:
        component_sum = sum(flops[k] for k in component_keys)
        # Allow for small rounding differences
        assert abs(flops['total'] - component_sum) <= 1, \
            f"Total FLOPs {flops['total']} should equal sum of components {component_sum}"


def assert_memory_breakdown_valid(memory: Dict[str, int]):
    """Assert that memory breakdown has valid structure and values."""
    # Check that all values are non-negative
    for key, value in memory.items():
        assert value >= 0, f"Memory value for {key} should be non-negative: {value}"
    
    # Check for expected keys
    expected_keys = ['parameters', 'activations']
    for key in expected_keys:
        if key in memory:
            assert memory[key] >= 0


def assert_shapes_valid(shapes: Dict[str, Any]):
    """Assert that matrix shapes have valid structure."""
    for component, component_shapes in shapes.items():
        assert isinstance(component_shapes, dict), f"Component {component} should have dict of shapes"
        
        for name, shape in component_shapes.items():
            assert isinstance(shape, tuple), f"Shape {name} should be a tuple"
            assert len(shape) >= 2, f"Shape {name} should have at least 2 dimensions"
            assert all(dim > 0 for dim in shape), f"All dimensions in {name} should be positive"