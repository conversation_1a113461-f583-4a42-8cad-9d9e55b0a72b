"""Tests for hardware integration infrastructure."""

import pytest
import tempfile
import yaml
from pathlib import Path

from llm_modeling_metrics.hardware import HardwareAdapter, HardwareService, HardwareSpec, ValidationResult
from llm_modeling_metrics.hardware.models import HardwareType, WorkloadProfile
from llm_modeling_metrics.hardware.validation import HardwareConfigValidator


class TestHardwareAdapter:
    """Test hardware adapter functionality."""
    
    def test_hardware_adapter_initialization(self):
        """Test hardware adapter can be initialized."""
        # This test will pass even if the specs file is not found
        # as the adapter should handle missing files gracefully
        try:
            adapter = HardwareAdapter()
            assert adapter is not None
        except FileNotFoundError:
            # Expected if gpu_specs.yaml is not found
            pass
    
    def test_hardware_spec_creation(self):
        """Test HardwareSpec creation and validation."""
        spec = HardwareSpec(
            id="test_gpu",
            name="Test GPU",
            type=HardwareType.GPU,
            memory_size_gb=32,
            memory_bandwidth_gbps=1000,
            tensor_performance={"fp16_tensor": 100, "bf16_tensor": 100},
            vector_performance={"fp32": 50, "fp16": 100}
        )
        
        assert spec.id == "test_gpu"
        assert spec.name == "Test GPU"
        assert spec.type == HardwareType.GPU
        assert spec.memory_size_gb == 32
        assert spec.memory_bandwidth_gbps == 1000
        assert "fp16" in spec.supported_precisions
        assert "bf16" in spec.supported_precisions
        assert "fp32" in spec.supported_precisions
        assert spec.get_peak_flops("fp16") == 100
        assert spec.supports_precision("fp16")
        assert spec.is_tensor_core_capable()
    
    def test_hardware_spec_validation_errors(self):
        """Test HardwareSpec validation catches errors."""
        with pytest.raises(ValueError, match="Hardware ID is required"):
            HardwareSpec(
                id="",
                name="Test GPU",
                type=HardwareType.GPU,
                memory_size_gb=32,
                memory_bandwidth_gbps=1000
            )
        
        with pytest.raises(ValueError, match="Memory size must be positive"):
            HardwareSpec(
                id="test_gpu",
                name="Test GPU", 
                type=HardwareType.GPU,
                memory_size_gb=0,
                memory_bandwidth_gbps=1000
            )
    
    def test_convert_gpu_spec(self):
        """Test conversion of raw GPU spec to HardwareSpec."""
        adapter = HardwareAdapter.__new__(HardwareAdapter)  # Create without __init__
        
        raw_spec = {
            "name": "Test GPU",
            "architecture": "Test Arch",
            "memory_size_gb": 32,
            "memory_bandwidth_gbps": 1000,
            "tensor_performance": {"fp16_tensor": 100},
            "vector_performance": {"fp32": 50}
        }
        
        spec = adapter._convert_gpu_spec("test_gpu", raw_spec)
        
        assert spec.id == "test_gpu"
        assert spec.name == "Test GPU"
        assert spec.type == HardwareType.GPU
        assert spec.architecture == "Test Arch"
        assert spec.memory_size_gb == 32
        assert spec.memory_bandwidth_gbps == 1000


class TestHardwareService:
    """Test hardware service functionality."""
    
    def test_hardware_service_initialization(self):
        """Test hardware service can be initialized."""
        try:
            service = HardwareService()
            assert service is not None
            assert service.adapter is not None
        except FileNotFoundError:
            # Expected if gpu_specs.yaml is not found
            pass
    
    def test_get_available_hardware_empty(self):
        """Test getting available hardware returns empty dict when no specs."""
        # Create a service with a non-existent specs file
        with pytest.raises(RuntimeError, match="Failed to load hardware specifications"):
            service = HardwareService("non_existent_file.yaml")
    
    def test_workload_profile_creation(self):
        """Test WorkloadProfile creation."""
        profile = WorkloadProfile(
            model_type="dense",
            batch_size=32,
            sequence_length=2048,
            precision_requirements=["fp16", "bf16"],
            memory_constraints=32,
            latency_requirements=100.0
        )
        
        assert profile.model_type == "dense"
        assert profile.batch_size == 32
        assert profile.sequence_length == 2048
        assert "fp16" in profile.precision_requirements
        assert profile.memory_constraints == 32
        assert profile.latency_requirements == 100.0
    
    def test_get_available_hardware_with_mock_data(self):
        """Test getting available hardware with mock data."""
        # Create a temporary YAML file with test data
        test_data = {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {"fp16_tensor": 100},
                    "vector_performance": {"fp32": 50}
                }
            },
            "npus": {
                "test_npu": {
                    "name": "Test NPU",
                    "memory_size_gb": 64,
                    "memory_bandwidth_gbps": 800,
                    "tensor_performance": {"fp16_tensor": 200}
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_file = f.name
        
        try:
            service = HardwareService(temp_file)
            hardware = service.get_available_hardware()
            
            assert "gpu" in hardware
            assert "npu" in hardware
            assert len(hardware["gpu"]) == 1
            assert len(hardware["npu"]) == 1
            assert hardware["gpu"][0].id == "test_gpu"
            assert hardware["npu"][0].id == "test_npu"
        finally:
            Path(temp_file).unlink()
    
    def test_get_hardware_specs_existing(self):
        """Test getting specs for existing hardware."""
        test_data = {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {"fp16_tensor": 100},
                    "vector_performance": {"fp32": 50}
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_file = f.name
        
        try:
            service = HardwareService(temp_file)
            spec = service.get_hardware_specs("test_gpu")
            
            assert spec is not None
            assert spec.id == "test_gpu"
            assert spec.name == "Test GPU"
            assert spec.memory_size_gb == 32
            assert spec.memory_bandwidth_gbps == 1000
        finally:
            Path(temp_file).unlink()
    
    def test_get_hardware_specs_nonexistent(self):
        """Test getting specs for non-existent hardware."""
        test_data = {"gpus": {}}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_file = f.name
        
        try:
            service = HardwareService(temp_file)
            spec = service.get_hardware_specs("nonexistent_gpu")
            
            assert spec is None
        finally:
            Path(temp_file).unlink()
    
    def test_validate_hardware_compatibility_success(self):
        """Test hardware compatibility validation with compatible setup."""
        test_data = {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {"fp16_tensor": 100},
                    "vector_performance": {"fp32": 50}
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_file = f.name
        
        try:
            service = HardwareService(temp_file)
            
            # Create mock operators with minimal memory requirements
            class MockOperator:
                def __init__(self):
                    self.operator_type = "attention"
                    self.precision = "fp16"
                    self.parameters = 1000000  # 1M parameters
            
            operators = [MockOperator()]
            result = service.validate_hardware_compatibility("test_gpu", operators)
            
            assert result.is_valid
            assert len(result.errors) == 0
        finally:
            Path(temp_file).unlink()
    
    def test_validate_hardware_compatibility_memory_exceeded(self):
        """Test hardware compatibility validation with memory constraints."""
        test_data = {
            "gpus": {
                "small_gpu": {
                    "name": "Small GPU",
                    "memory_size_gb": 1,  # Very small memory
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {"fp16_tensor": 100},
                    "vector_performance": {"fp32": 50}
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_file = f.name
        
        try:
            service = HardwareService(temp_file)
            
            # Create mock operators with high memory requirements
            class MockOperator:
                def __init__(self):
                    self.operator_type = "attention"
                    self.precision = "fp32"
                    self.parameters = 1000000000  # 1B parameters
            
            operators = [MockOperator()]
            result = service.validate_hardware_compatibility("small_gpu", operators)
            
            # Should have memory-related errors or warnings
            assert len(result.errors) > 0 or len(result.warnings) > 0
        finally:
            Path(temp_file).unlink()
    
    def test_validate_hardware_compatibility_nonexistent_hardware(self):
        """Test hardware compatibility validation with non-existent hardware."""
        test_data = {"gpus": {}}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_file = f.name
        
        try:
            service = HardwareService(temp_file)
            result = service.validate_hardware_compatibility("nonexistent_gpu", [])
            
            assert not result.is_valid
            assert len(result.errors) > 0
            assert any("not found" in error for error in result.errors)
        finally:
            Path(temp_file).unlink()


class TestHardwareConfigValidator:
    """Test hardware configuration validation."""
    
    def test_validator_initialization(self):
        """Test validator can be initialized."""
        validator = HardwareConfigValidator()
        assert validator is not None
        assert validator.REQUIRED_FIELDS is not None
        assert validator.VALID_PRECISIONS is not None
    
    def test_validate_valid_config_data(self):
        """Test validation of valid configuration data."""
        validator = HardwareConfigValidator()
        
        config_data = {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {
                        "fp16_tensor": 100,
                        "bf16_tensor": 100
                    },
                    "vector_performance": {
                        "fp32": 50,
                        "fp16": 100
                    }
                }
            }
        }
        
        result = validator.validate_hardware_config_data(config_data)
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_invalid_config_data(self):
        """Test validation catches invalid configuration data."""
        validator = HardwareConfigValidator()
        
        # Missing required fields
        config_data = {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU"
                    # Missing memory_size_gb and memory_bandwidth_gbps
                }
            }
        }
        
        result = validator.validate_hardware_config_data(config_data)
        assert not result.is_valid
        assert len(result.errors) >= 2  # Should have errors for missing fields
    
    def test_validate_config_file_not_found(self):
        """Test validation handles missing config file."""
        validator = HardwareConfigValidator()
        
        result = validator.validate_hardware_config_file("non_existent_file.yaml")
        assert not result.is_valid
        assert any("not found" in error for error in result.errors)
    
    def test_validate_config_file_invalid_yaml(self):
        """Test validation handles invalid YAML."""
        validator = HardwareConfigValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_file = f.name
        
        try:
            result = validator.validate_hardware_config_file(temp_file)
            assert not result.is_valid
            assert any("YAML" in error for error in result.errors)
        finally:
            Path(temp_file).unlink()
    
    def test_validate_config_file_valid(self):
        """Test validation of valid config file."""
        validator = HardwareConfigValidator()
        
        config_data = {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 1000
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_file = f.name
        
        try:
            result = validator.validate_hardware_config_file(temp_file)
            assert result.is_valid
            assert len(result.errors) == 0
        finally:
            Path(temp_file).unlink()
    
    def test_generate_validation_report(self):
        """Test validation report generation."""
        validator = HardwareConfigValidator()
        
        result = ValidationResult(is_valid=False)
        result.add_error("Test error")
        result.add_warning("Test warning")
        result.add_recommendation("Test recommendation")
        
        report = validator.generate_validation_report(result)
        
        assert "❌" in report
        assert "Test error" in report
        assert "Test warning" in report
        assert "Test recommendation" in report
        assert "🚨 Errors:" in report
        assert "⚠️  Warnings:" in report
        assert "💡 Recommendations:" in report


class TestValidationResult:
    """Test ValidationResult functionality."""
    
    def test_validation_result_creation(self):
        """Test ValidationResult creation."""
        result = ValidationResult(is_valid=True)
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert len(result.recommendations) == 0
    
    def test_add_error_invalidates(self):
        """Test adding error invalidates result."""
        result = ValidationResult(is_valid=True)
        result.add_error("Test error")
        
        assert not result.is_valid
        assert "Test error" in result.errors
    
    def test_add_warning_doesnt_invalidate(self):
        """Test adding warning doesn't invalidate result."""
        result = ValidationResult(is_valid=True)
        result.add_warning("Test warning")
        
        assert result.is_valid
        assert "Test warning" in result.warnings
    
    def test_has_issues(self):
        """Test has_issues method."""
        result = ValidationResult(is_valid=True)
        assert not result.has_issues()
        
        result.add_warning("Test warning")
        assert result.has_issues()
        
        result = ValidationResult(is_valid=True)
        result.add_error("Test error")
        assert result.has_issues()


if __name__ == "__main__":
    pytest.main([__file__])