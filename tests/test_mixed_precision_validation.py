"""
Tests for mixed precision validation functionality.
"""

import pytest
from llm_modeling_metrics.core.operators import (
    validate_precision_type,
    validate_precision_compatibility,
    validate_mixed_precision_config,
    UnsupportedPrecisionError,
    IncompatiblePrecisionError,
    MatMulOperator,
    AttentionOperator,
    MoEOperator
)


class TestPrecisionValidation:
    """Test precision validation functions."""
    
    def test_validate_precision_type_valid(self):
        """Test validation of valid precision types."""
        valid_precisions = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        for precision in valid_precisions:
            validate_precision_type(precision)  # Should not raise
    
    def test_validate_precision_type_none(self):
        """Test that None is accepted for optional parameters."""
        validate_precision_type(None)  # Should not raise
    
    def test_validate_precision_type_invalid(self):
        """Test validation of invalid precision types."""
        invalid_precisions = ['fp64', 'int16', 'invalid', '', 123, []]
        for precision in invalid_precisions:
            with pytest.raises(UnsupportedPrecisionError):
                validate_precision_type(precision)
    
    def test_validate_precision_compatibility_valid(self):
        """Test validation of valid precision combinations."""
        valid_combinations = [
            {'weight': 'bf16', 'activation': 'bf16'},
            {'weight': 'bf16', 'activation': 'fp16', 'kv_cache': 'fp8'},
            {'expert_parameter': 'fp8', 'attention_parameter': 'bf16'},
        ]
        
        for combo in valid_combinations:
            validate_precision_compatibility(combo)  # Should not raise
    
    def test_validate_precision_compatibility_invalid(self):
        """Test validation of invalid precision combinations."""
        invalid_combinations = [
            {'activation': 'fp4'},  # fp4 not allowed for activations
            {'kv_cache': 'fp4'},    # fp4 not allowed for cache
            {'optimizer': 'fp4'},   # fp4 not allowed for optimizer
            {'optimizer': 'int8'},  # int8 not recommended for optimizer
        ]
        
        for combo in invalid_combinations:
            with pytest.raises(IncompatiblePrecisionError):
                validate_precision_compatibility(combo)
    
    def test_validate_mixed_precision_config_valid(self):
        """Test validation of valid mixed precision configurations."""
        validate_mixed_precision_config(
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            expert_parameter_dtype='fp8'
        )  # Should not raise
    
    def test_validate_mixed_precision_config_invalid(self):
        """Test validation of invalid mixed precision configurations."""
        with pytest.raises(IncompatiblePrecisionError):
            validate_mixed_precision_config(
                activation_dtype='fp4'  # fp4 not allowed for activations
            )


class TestOperatorValidation:
    """Test operator constructor validation."""
    
    def test_matmul_operator_valid_precisions(self):
        """Test MatMulOperator with valid precisions."""
        op = MatMulOperator(M=10, N=20, K=30, 
                          input_precision='bf16',
                          weight_precision='bf16',
                          output_precision='fp16')
        assert op.input_precision == 'bf16'
        assert op.weight_precision == 'bf16'
        assert op.output_precision == 'fp16'
    
    def test_matmul_operator_invalid_precision(self):
        """Test MatMulOperator with invalid precision."""
        with pytest.raises(UnsupportedPrecisionError):
            MatMulOperator(M=10, N=20, K=30, input_precision='invalid')
    
    def test_attention_operator_valid_precisions(self):
        """Test AttentionOperator with valid precisions."""
        op = AttentionOperator(hidden_size=512, num_heads=8,
                             kv_cache_precision='fp8')
        assert op.kv_cache_precision == 'fp8'
    
    def test_attention_operator_invalid_precision(self):
        """Test AttentionOperator with invalid precision."""
        with pytest.raises(UnsupportedPrecisionError):
            AttentionOperator(hidden_size=512, num_heads=8,
                            kv_cache_precision='invalid')
    
    def test_moe_operator_valid_precisions(self):
        """Test MoEOperator with valid precisions."""
        op = MoEOperator(hidden_size=512, intermediate_size=2048,
                       num_experts=8, experts_per_token=2,
                       expert_parameter_precision='fp8')
        assert op.expert_parameter_precision == 'fp8'
    
    def test_moe_operator_invalid_precision(self):
        """Test MoEOperator with invalid precision."""
        with pytest.raises(UnsupportedPrecisionError):
            MoEOperator(hidden_size=512, intermediate_size=2048,
                       num_experts=8, experts_per_token=2,
                       expert_parameter_precision='invalid')


class TestCompatibilityRules:
    """Test specific precision compatibility rules."""
    
    def test_fp4_activation_restriction(self):
        """Test that fp4 is not allowed for activations."""
        with pytest.raises(IncompatiblePrecisionError, match="fp4 precision is not compatible with activation"):
            validate_precision_compatibility({'activation': 'fp4'})
    
    def test_fp4_cache_restriction(self):
        """Test that fp4 is not allowed for cache."""
        with pytest.raises(IncompatiblePrecisionError, match="fp4 precision is not compatible with activation"):
            validate_precision_compatibility({'kv_cache': 'fp4'})
    
    def test_optimizer_precision_restrictions(self):
        """Test optimizer precision restrictions."""
        # fp4 should be rejected
        with pytest.raises(IncompatiblePrecisionError, match="Optimizer precision.*may cause numerical instability"):
            validate_precision_compatibility({'optimizer': 'fp4'})
        
        # int8 should be rejected
        with pytest.raises(IncompatiblePrecisionError, match="Optimizer precision.*may cause numerical instability"):
            validate_precision_compatibility({'optimizer': 'int8'})
        
        # fp16, bf16, fp32 should be accepted
        validate_precision_compatibility({'optimizer': 'fp16'})
        validate_precision_compatibility({'optimizer': 'bf16'})
        validate_precision_compatibility({'optimizer': 'fp32'})