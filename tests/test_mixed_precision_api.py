#!/usr/bin/env python3
"""
Test script for mixed precision API endpoints.
"""

import requests
import json
from typing import Dict, Any

# API base URL
BASE_URL = "http://localhost:8000"

def test_supported_dtypes():
    """Test the supported dtypes endpoint."""
    print("Testing /api/memory/dtypes endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/memory/dtypes")
        if response.status_code == 200:
            data = response.json()
            print("✓ Supported dtypes endpoint working")
            print(f"  - Supported dtypes: {data['supported_dtypes']}")
            print(f"  - Default dtype: {data['default_dtype']}")
            print(f"  - Mixed precision defaults: {data['mixed_precision_defaults']}")
            return True
        else:
            print(f"✗ Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Exception: {e}")
        return False

def test_precision_recommendations():
    """Test the precision recommendations endpoint."""
    print("\nTesting /api/memory/precision-recommendations endpoint...")
    
    test_cases = [
        ("meta-llama/Meta-Llama-3-8B-Instruct", "inference"),
        ("deepseek-ai/DeepSeek-V3", "memory_optimized"),
        ("Qwen/Qwen3-8B", "training")
    ]
    
    for model_name, use_case in test_cases:
        try:
            response = requests.get(
                f"{BASE_URL}/api/memory/precision-recommendations",
                params={"model_name": model_name, "use_case": use_case}
            )
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Recommendations for {model_name} ({use_case})")
                print(f"  - Config: {data['recommended_config']}")
                print(f"  - Estimated savings: {data['estimated_memory_savings_percent']}%")
            else:
                print(f"✗ Error for {model_name}: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"✗ Exception for {model_name}: {e}")
            return False
    
    return True

def test_mixed_precision_analysis():
    """Test the mixed precision memory analysis endpoint."""
    print("\nTesting /api/memory/mixed-precision endpoint...")
    
    test_request = {
        "model_names": ["meta-llama/Meta-Llama-3-8B-Instruct"],
        "sequence_length": 2048,
        "batch_size": 1,
        "dtype": "fp16",
        "weight_dtype": "bf16",
        "activation_dtype": "bf16",
        "kv_cache_dtype": "fp8",
        "training": False,
        "include_kv_cache": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/memory/mixed-precision",
            json=test_request,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            print("✓ Mixed precision analysis working")
            print(f"  - Analysis type: {data['analysis_type']}")
            print(f"  - Execution time: {data['execution_time']:.2f}s")
            print(f"  - Models analyzed: {len(data['model_results'])}")
            return True
        else:
            print(f"✗ Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Exception: {e}")
        return False

def test_enhanced_model_analysis():
    """Test the enhanced model analysis endpoint with mixed precision."""
    print("\nTesting /api/analyze endpoint with mixed precision...")
    
    test_request = {
        "model_names": ["meta-llama/Meta-Llama-3-8B-Instruct"],
        "sequence_length": 2048,
        "batch_size": 1,
        "precision": "fp16",
        "weight_dtype": "bf16",
        "activation_dtype": "bf16",
        "kv_cache_dtype": "fp8",
        "expert_parameter_dtype": None,
        "attention_parameter_dtype": "bf16",
        "training": False,
        "include_comparison": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/analyze",
            json=test_request,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            print("✓ Enhanced model analysis working")
            print(f"  - Execution time: {data['execution_time']:.2f}s")
            print(f"  - Models analyzed: {len(data['results'])}")
            return True
        else:
            print(f"✗ Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Exception: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Mixed Precision API Endpoints")
    print("=" * 50)
    
    tests = [
        test_supported_dtypes,
        test_precision_recommendations,
        test_mixed_precision_analysis,
        test_enhanced_model_analysis
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())