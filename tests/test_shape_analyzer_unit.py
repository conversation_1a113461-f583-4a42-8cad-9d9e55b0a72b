"""
Unit tests for ShapeAnalyzer functionality.
"""

import pytest
from unittest.mock import Mock, patch

from llm_modeling_metrics.metrics.shape_analyzer import <PERSON>hapeAnalyzer
from llm_modeling_metrics.core.base_model import ParallelConfig
from tests.conftest import create_mock_model_config, assert_shapes_valid


class TestShapeAnalyzerAttentionShapes:
    """Test attention shape computation."""
    
    def test_attention_shapes_standard(self):
        """Test standard attention shape computation."""
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=32,
            head_dim=128
        )
        
        assert_shapes_valid({'attention': shapes})
        
        # Check standard attention shapes
        assert shapes['q_proj'] == (4096, 4096)
        assert shapes['k_proj'] == (4096, 4096)
        assert shapes['v_proj'] == (4096, 4096)
        assert shapes['o_proj'] == (4096, 4096)
    
    def test_attention_shapes_gqa(self):
        """Test attention shapes with Grouped Query Attention."""
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=8,  # GQA with 8 KV heads
            head_dim=128
        )
        
        assert_shapes_valid({'attention': shapes})
        
        # Q projection should be full size
        assert shapes['q_proj'] == (4096, 4096)
        
        # K and V projections should be reduced
        kv_size = 8 * 128  # 8 KV heads * 128 head_dim = 1024
        assert shapes['k_proj'] == (4096, kv_size)
        assert shapes['v_proj'] == (4096, kv_size)
        
        # O projection should be full size
        assert shapes['o_proj'] == (4096, 4096)
    
    def test_attention_shapes_mqa(self):
        """Test attention shapes with Multi-Query Attention."""
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=1,  # MQA with single KV head
            head_dim=128
        )
        
        assert_shapes_valid({'attention': shapes})
        
        # Q projection should be full size
        assert shapes['q_proj'] == (4096, 4096)
        
        # K and V projections should be minimal
        kv_size = 1 * 128  # 1 KV head * 128 head_dim = 128
        assert shapes['k_proj'] == (4096, kv_size)
        assert shapes['v_proj'] == (4096, kv_size)
        
        # O projection should be full size
        assert shapes['o_proj'] == (4096, 4096)
    
    def test_attention_shapes_different_head_dims(self):
        """Test attention shapes with different head dimensions."""
        # Test with smaller head dimension
        shapes_small = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=2048,
            num_heads=32,
            num_kv_heads=32,
            head_dim=64  # Smaller head dim
        )
        
        assert shapes_small['q_proj'] == (2048, 2048)
        assert shapes_small['k_proj'] == (2048, 2048)
        
        # Test with larger head dimension
        shapes_large = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=8192,
            num_heads=32,
            num_kv_heads=32,
            head_dim=256  # Larger head dim
        )
        
        assert shapes_large['q_proj'] == (8192, 8192)
        assert shapes_large['k_proj'] == (8192, 8192)
    
    def test_attention_shapes_edge_cases(self):
        """Test attention shapes with edge cases."""
        # Single head
        shapes_single = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=512,
            num_heads=1,
            num_kv_heads=1,
            head_dim=512
        )
        
        assert shapes_single['q_proj'] == (512, 512)
        assert shapes_single['k_proj'] == (512, 512)
        
        # Many heads
        shapes_many = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=4096,
            num_heads=128,
            num_kv_heads=128,
            head_dim=32
        )
        
        assert shapes_many['q_proj'] == (4096, 4096)
        assert shapes_many['k_proj'] == (4096, 4096)


class TestShapeAnalyzerMLPShapes:
    """Test MLP shape computation."""
    
    def test_mlp_shapes_gated_activation(self):
        """Test MLP shapes with gated activation."""
        shapes = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            use_gated_activation=True
        )
        
        assert_shapes_valid({'mlp': shapes})
        
        # Check gated MLP shapes
        assert shapes['gate_proj'] == (4096, 11008)
        assert shapes['up_proj'] == (4096, 11008)
        assert shapes['down_proj'] == (11008, 4096)
        
        # Should not have standard MLP projections
        assert 'fc1' not in shapes
        assert 'fc2' not in shapes
    
    def test_mlp_shapes_standard_activation(self):
        """Test MLP shapes with standard activation."""
        shapes = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            use_gated_activation=False
        )
        
        assert_shapes_valid({'mlp': shapes})
        
        # Check standard MLP shapes
        assert shapes['fc1'] == (4096, 11008)
        assert shapes['fc2'] == (11008, 4096)
        
        # Should not have gated projections
        assert 'gate_proj' not in shapes
        assert 'up_proj' not in shapes
    
    def test_mlp_shapes_different_sizes(self):
        """Test MLP shapes with different intermediate sizes."""
        test_cases = [
            (512, 1024),
            (2048, 5504),
            (8192, 22016),
            (16384, 65536),
        ]
        
        for hidden_size, intermediate_size in test_cases:
            shapes = ShapeAnalyzer.compute_mlp_shapes(
                hidden_size=hidden_size,
                intermediate_size=intermediate_size,
                use_gated_activation=True
            )
            
            assert shapes['gate_proj'] == (hidden_size, intermediate_size)
            assert shapes['up_proj'] == (hidden_size, intermediate_size)
            assert shapes['down_proj'] == (intermediate_size, hidden_size)
    
    def test_mlp_shapes_edge_cases(self):
        """Test MLP shapes with edge cases."""
        # Minimal sizes
        shapes_minimal = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=1,
            intermediate_size=1,
            use_gated_activation=True
        )
        
        assert shapes_minimal['gate_proj'] == (1, 1)
        assert shapes_minimal['up_proj'] == (1, 1)
        assert shapes_minimal['down_proj'] == (1, 1)
        
        # Large sizes
        shapes_large = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=32768,
            intermediate_size=131072,
            use_gated_activation=False
        )
        
        assert shapes_large['fc1'] == (32768, 131072)
        assert shapes_large['fc2'] == (131072, 32768)


class TestShapeAnalyzerMoEShapes:
    """Test MoE shape computation."""
    
    def test_moe_shapes_basic(self):
        """Test basic MoE shape computation."""
        shapes = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            num_experts=64,
            shared_experts=2,
            use_gated_activation=True
        )
        
        assert_shapes_valid({'moe': shapes})
        
        # Check routed expert shapes
        assert 'routed_experts' in shapes
        routed_shapes = shapes['routed_experts']
        assert routed_shapes['gate_proj'] == (4096, 11008, 64)  # Added expert dimension
        assert routed_shapes['up_proj'] == (4096, 11008, 64)
        assert routed_shapes['down_proj'] == (11008, 4096, 64)
        
        # Check shared expert shapes
        assert 'shared_experts' in shapes
        shared_shapes = shapes['shared_experts']
        assert shared_shapes['gate_proj'] == (4096, 11008, 2)  # 2 shared experts
        assert shared_shapes['up_proj'] == (4096, 11008, 2)
        assert shared_shapes['down_proj'] == (11008, 4096, 2)
        
        # Check router shapes
        assert 'router' in shapes
        assert shapes['router']['gate'] == (4096, 64)  # hidden_size x num_experts
    
    def test_moe_shapes_without_shared_experts(self):
        """Test MoE shapes without shared experts."""
        shapes = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            num_experts=64,
            shared_experts=0,
            use_gated_activation=True
        )
        
        # Should have routed experts and router
        assert 'routed_experts' in shapes
        assert 'router' in shapes
        
        # Should not have shared experts
        assert 'shared_experts' not in shapes or len(shapes['shared_experts']) == 0
    
    def test_moe_shapes_standard_activation(self):
        """Test MoE shapes with standard activation."""
        shapes = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            num_experts=64,
            shared_experts=2,
            use_gated_activation=False
        )
        
        # Check routed expert shapes with standard activation
        routed_shapes = shapes['routed_experts']
        assert routed_shapes['fc1'] == (4096, 11008, 64)
        assert routed_shapes['fc2'] == (11008, 4096, 64)
        
        # Should not have gated projections
        assert 'gate_proj' not in routed_shapes
        assert 'up_proj' not in routed_shapes
    
    def test_moe_shapes_different_expert_counts(self):
        """Test MoE shapes with different expert counts."""
        expert_counts = [8, 16, 32, 128, 256, 512]
        
        for num_experts in expert_counts:
            shapes = ShapeAnalyzer.compute_moe_shapes(
                hidden_size=4096,
                intermediate_size=11008,
                num_experts=num_experts,
                shared_experts=2,
                use_gated_activation=True
            )
            
            # Router should scale with number of experts
            assert shapes['router']['gate'] == (4096, num_experts)
            
            # Expert shapes should have correct expert dimension
            routed_shapes = shapes['routed_experts']
            assert routed_shapes['gate_proj'][2] == num_experts
            assert routed_shapes['up_proj'][2] == num_experts
            assert routed_shapes['down_proj'][2] == num_experts
    
    def test_moe_shapes_edge_cases(self):
        """Test MoE shapes with edge cases."""
        # Single expert
        shapes_single = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=512,
            intermediate_size=1024,
            num_experts=1,
            shared_experts=0,
            use_gated_activation=True
        )
        
        assert shapes_single['router']['gate'] == (512, 1)
        assert shapes_single['routed_experts']['gate_proj'] == (512, 1024, 1)
        
        # Many experts
        shapes_many = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=8192,
            intermediate_size=22016,
            num_experts=1024,
            shared_experts=8,
            use_gated_activation=True
        )
        
        assert shapes_many['router']['gate'] == (8192, 1024)
        assert shapes_many['routed_experts']['gate_proj'] == (8192, 22016, 1024)
        assert shapes_many['shared_experts']['gate_proj'] == (8192, 22016, 8)


class TestShapeAnalyzerEmbeddingShapes:
    """Test embedding shape computation."""
    
    def test_embedding_shapes_basic(self):
        """Test basic embedding shape computation."""
        shapes = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size=32000,
            hidden_size=4096,
            tie_embeddings=False
        )
        
        assert_shapes_valid({'embeddings': shapes})
        
        # Check embedding shapes
        assert shapes['input_embeddings'] == (32000, 4096)
        assert shapes['output_embeddings'] == (32000, 4096)
    
    def test_embedding_shapes_tied(self):
        """Test embedding shapes with tied embeddings."""
        shapes = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size=32000,
            hidden_size=4096,
            tie_embeddings=True
        )
        
        # Should only have input embeddings
        assert shapes['input_embeddings'] == (32000, 4096)
        assert 'output_embeddings' not in shapes or shapes['output_embeddings'] is None
    
    def test_embedding_shapes_different_sizes(self):
        """Test embedding shapes with different vocabulary sizes."""
        test_cases = [
            (1000, 512),
            (50000, 2048),
            (100000, 8192),
            (200000, 16384),
        ]
        
        for vocab_size, hidden_size in test_cases:
            shapes = ShapeAnalyzer.compute_embedding_shapes(
                vocab_size=vocab_size,
                hidden_size=hidden_size,
                tie_embeddings=False
            )
            
            assert shapes['input_embeddings'] == (vocab_size, hidden_size)
            assert shapes['output_embeddings'] == (vocab_size, hidden_size)
    
    def test_embedding_shapes_edge_cases(self):
        """Test embedding shapes with edge cases."""
        # Minimal vocabulary
        shapes_minimal = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size=1,
            hidden_size=1,
            tie_embeddings=False
        )
        
        assert shapes_minimal['input_embeddings'] == (1, 1)
        assert shapes_minimal['output_embeddings'] == (1, 1)
        
        # Large vocabulary
        shapes_large = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size=1000000,
            hidden_size=32768,
            tie_embeddings=True
        )
        
        assert shapes_large['input_embeddings'] == (1000000, 32768)
        assert 'output_embeddings' not in shapes_large or shapes_large['output_embeddings'] is None


class TestShapeAnalyzerTensorParallelism:
    """Test tensor parallelism shape modifications."""
    
    def test_apply_tensor_parallel_attention(self):
        """Test tensor parallel modifications for attention shapes."""
        base_shapes = {
            'q_proj': (4096, 4096),
            'k_proj': (4096, 4096),
            'v_proj': (4096, 4096),
            'o_proj': (4096, 4096)
        }
        
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, 4, 'attention')
        
        # Column-parallel operations (split output dimension)
        assert tp_shapes['q_proj'] == (4096, 1024)  # 4096 / 4
        assert tp_shapes['k_proj'] == (4096, 1024)
        assert tp_shapes['v_proj'] == (4096, 1024)
        
        # Row-parallel operation (split input dimension)
        assert tp_shapes['o_proj'] == (1024, 4096)  # 4096 / 4
    
    def test_apply_tensor_parallel_mlp(self):
        """Test tensor parallel modifications for MLP shapes."""
        base_shapes = {
            'gate_proj': (4096, 11008),
            'up_proj': (4096, 11008),
            'down_proj': (11008, 4096)
        }
        
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, 4, 'mlp')
        
        # Column-parallel operations (split output dimension)
        assert tp_shapes['gate_proj'] == (4096, 2752)  # 11008 / 4
        assert tp_shapes['up_proj'] == (4096, 2752)
        
        # Row-parallel operation (split input dimension)
        assert tp_shapes['down_proj'] == (2752, 4096)  # 11008 / 4
    
    def test_apply_tensor_parallel_embeddings(self):
        """Test tensor parallel modifications for embedding shapes."""
        base_shapes = {
            'input_embeddings': (32000, 4096),
            'output_embeddings': (32000, 4096)
        }
        
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, 4, 'embeddings')
        
        # Embeddings are typically replicated (no change)
        assert tp_shapes['input_embeddings'] == (32000, 4096)
        assert tp_shapes['output_embeddings'] == (32000, 4096)
    
    def test_apply_tensor_parallel_different_sizes(self):
        """Test tensor parallelism with different TP sizes."""
        base_shapes = {'matrix': (4096, 8192)}
        
        tp_sizes = [1, 2, 4, 8, 16]
        for tp_size in tp_sizes:
            tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, tp_size, 'mlp')
            
            if tp_size == 1:
                assert tp_shapes['matrix'] == (4096, 8192)  # No change
            else:
                expected_output_dim = 8192 // tp_size
                assert tp_shapes['matrix'] == (4096, expected_output_dim)
    
    def test_apply_tensor_parallel_non_divisible(self):
        """Test tensor parallelism with non-divisible dimensions."""
        base_shapes = {'matrix': (1000, 1001)}
        
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, 3, 'mlp')
        
        # Should handle non-divisible dimensions gracefully
        assert tp_shapes['matrix'][0] == 1000  # Input dim unchanged
        expected_output_dim = 1001 // 3  # Integer division
        assert tp_shapes['matrix'][1] == expected_output_dim
    
    def test_apply_tensor_parallel_moe(self):
        """Test tensor parallelism with MoE shapes."""
        base_shapes = {
            'routed_experts': {
                'gate_proj': (4096, 11008, 64),
                'up_proj': (4096, 11008, 64),
                'down_proj': (11008, 4096, 64)
            },
            'router': {
                'gate': (4096, 64)
            }
        }
        
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, 4, 'moe')
        
        # Expert shapes should be modified
        routed_shapes = tp_shapes['routed_experts']
        assert routed_shapes['gate_proj'] == (4096, 2752, 64)  # 11008 / 4
        assert routed_shapes['up_proj'] == (4096, 2752, 64)
        assert routed_shapes['down_proj'] == (2752, 4096, 64)  # 11008 / 4
        
        # Router should be replicated
        assert tp_shapes['router']['gate'] == (4096, 64)


class TestShapeAnalyzerExpertParallelism:
    """Test expert parallelism shape modifications."""
    
    def test_apply_expert_parallel_basic(self):
        """Test basic expert parallelism application."""
        base_shapes = {
            'routed_experts': {
                'gate_proj': (4096, 11008, 64),
                'up_proj': (4096, 11008, 64),
                'down_proj': (11008, 4096, 64)
            },
            'router': {
                'gate': (4096, 64)
            }
        }
        
        ep_shapes = ShapeAnalyzer.apply_expert_parallel(base_shapes, 8)
        
        # Expert dimension should be split
        routed_shapes = ep_shapes['routed_experts']
        assert routed_shapes['gate_proj'] == (4096, 11008, 8)  # 64 / 8
        assert routed_shapes['up_proj'] == (4096, 11008, 8)
        assert routed_shapes['down_proj'] == (11008, 4096, 8)
        
        # Router should be modified to reflect expert partitioning
        assert ep_shapes['router']['gate'] == (4096, 8)  # 64 / 8
    
    def test_apply_expert_parallel_with_shared(self):
        """Test expert parallelism with shared experts."""
        base_shapes = {
            'routed_experts': {
                'gate_proj': (4096, 11008, 64),
                'up_proj': (4096, 11008, 64),
                'down_proj': (11008, 4096, 64)
            },
            'shared_experts': {
                'gate_proj': (4096, 11008, 2),
                'up_proj': (4096, 11008, 2),
                'down_proj': (11008, 4096, 2)
            },
            'router': {
                'gate': (4096, 64)
            }
        }
        
        ep_shapes = ShapeAnalyzer.apply_expert_parallel(base_shapes, 8)
        
        # Routed experts should be split
        routed_shapes = ep_shapes['routed_experts']
        assert routed_shapes['gate_proj'] == (4096, 11008, 8)  # 64 / 8
        
        # Shared experts should be replicated (no change)
        shared_shapes = ep_shapes['shared_experts']
        assert shared_shapes['gate_proj'] == (4096, 11008, 2)  # No change
    
    def test_apply_expert_parallel_different_sizes(self):
        """Test expert parallelism with different EP sizes."""
        base_shapes = {
            'routed_experts': {
                'gate_proj': (4096, 11008, 256)
            },
            'router': {
                'gate': (4096, 256)
            }
        }
        
        ep_sizes = [1, 2, 4, 8, 16, 32, 64, 128, 256]
        for ep_size in ep_sizes:
            ep_shapes = ShapeAnalyzer.apply_expert_parallel(base_shapes, ep_size)
            
            expected_experts_per_device = 256 // ep_size
            routed_shapes = ep_shapes['routed_experts']
            assert routed_shapes['gate_proj'] == (4096, 11008, expected_experts_per_device)
            assert ep_shapes['router']['gate'] == (4096, expected_experts_per_device)
    
    def test_apply_expert_parallel_non_divisible(self):
        """Test expert parallelism with non-divisible expert count."""
        base_shapes = {
            'routed_experts': {
                'gate_proj': (4096, 11008, 100)  # Not divisible by many numbers
            },
            'router': {
                'gate': (4096, 100)
            }
        }
        
        ep_shapes = ShapeAnalyzer.apply_expert_parallel(base_shapes, 7)
        
        # Should handle non-divisible expert counts gracefully
        expected_experts_per_device = 100 // 7  # Integer division = 14
        routed_shapes = ep_shapes['routed_experts']
        assert routed_shapes['gate_proj'] == (4096, 11008, expected_experts_per_device)


class TestShapeAnalyzerCompleteModel:
    """Test complete model shape computation."""
    
    def test_compute_complete_model_shapes_dense(self):
        """Test complete model shape computation for dense models."""
        model_config = create_mock_model_config('llama')
        
        shapes = ShapeAnalyzer.compute_complete_model_shapes(
            model_config, model_type='dense'
        )
        
        # Should have all components
        assert 'attention' in shapes
        assert 'mlp' in shapes
        assert 'embeddings' in shapes
        
        # Check attention shapes
        attention_shapes = shapes['attention']
        assert attention_shapes['q_proj'] == (4096, 4096)
        assert attention_shapes['k_proj'] == (4096, 4096)
        assert attention_shapes['v_proj'] == (4096, 4096)
        assert attention_shapes['o_proj'] == (4096, 4096)
        
        # Check MLP shapes (should be gated for Llama)
        mlp_shapes = shapes['mlp']
        assert mlp_shapes['gate_proj'] == (4096, 11008)
        assert mlp_shapes['up_proj'] == (4096, 11008)
        assert mlp_shapes['down_proj'] == (11008, 4096)
        
        # Check embedding shapes
        embedding_shapes = shapes['embeddings']
        assert embedding_shapes['input_embeddings'] == (32000, 4096)
    
    def test_compute_complete_model_shapes_moe(self):
        """Test complete model shape computation for MoE models."""
        model_config = create_mock_model_config('deepseek_v3')
        
        shapes = ShapeAnalyzer.compute_complete_model_shapes(
            model_config, model_type='moe'
        )
        
        # Should have all components
        assert 'attention' in shapes
        assert 'moe' in shapes
        assert 'embeddings' in shapes
        
        # Check MoE shapes
        moe_shapes = shapes['moe']
        assert 'routed_experts' in moe_shapes
        assert 'shared_experts' in moe_shapes
        assert 'router' in moe_shapes
        
        # Check router shapes
        assert moe_shapes['router']['gate'] == (7168, 256)  # hidden_size x n_routed_experts
    
    def test_compute_complete_model_shapes_with_parallel(self):
        """Test complete model shapes with parallel configuration."""
        model_config = create_mock_model_config('llama')
        parallel_config = ParallelConfig(tensor_parallel_size=4)
        
        shapes = ShapeAnalyzer.compute_complete_model_shapes(
            model_config, model_type='dense', parallel_config=parallel_config
        )
        
        # Attention shapes should be modified for tensor parallelism
        attention_shapes = shapes['attention']
        assert attention_shapes['q_proj'] == (4096, 1024)  # 4096 / 4
        assert attention_shapes['k_proj'] == (4096, 1024)
        assert attention_shapes['v_proj'] == (4096, 1024)
        assert attention_shapes['o_proj'] == (1024, 4096)  # Row-parallel
        
        # MLP shapes should be modified
        mlp_shapes = shapes['mlp']
        assert mlp_shapes['gate_proj'] == (4096, 2752)  # 11008 / 4
        assert mlp_shapes['up_proj'] == (4096, 2752)
        assert mlp_shapes['down_proj'] == (2752, 4096)  # Row-parallel
    
    def test_compute_complete_model_shapes_moe_with_expert_parallel(self):
        """Test complete MoE model shapes with expert parallelism."""
        model_config = create_mock_model_config('deepseek_v3')
        parallel_config = ParallelConfig(
            tensor_parallel_size=2,
            expert_parallel_size=8
        )
        
        shapes = ShapeAnalyzer.compute_complete_model_shapes(
            model_config, model_type='moe', parallel_config=parallel_config
        )
        
        # Should have both tensor and expert parallelism applied
        moe_shapes = shapes['moe']
        routed_shapes = moe_shapes['routed_experts']
        
        # Tensor parallelism: intermediate_size / tp_size = 18432 / 2 = 9216
        # Expert parallelism: n_routed_experts / ep_size = 256 / 8 = 32
        assert routed_shapes['gate_proj'] == (7168, 9216, 32)
        assert routed_shapes['up_proj'] == (7168, 9216, 32)
        assert routed_shapes['down_proj'] == (9216, 7168, 32)


class TestShapeAnalyzerEdgeCases:
    """Test edge cases and error conditions for ShapeAnalyzer."""
    
    def test_zero_dimensions(self):
        """Test handling of zero dimensions."""
        # Should handle zero dimensions gracefully
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=0,
            num_heads=1,
            num_kv_heads=1,
            head_dim=0
        )
        
        # Should return shapes with zero dimensions
        assert shapes['q_proj'] == (0, 0)
        assert shapes['k_proj'] == (0, 0)
    
    def test_invalid_parallel_sizes(self):
        """Test handling of invalid parallel sizes."""
        base_shapes = {'matrix': (4096, 4096)}
        
        # Zero parallel size should raise error
        with pytest.raises(ValueError, match="Parallel size must be positive"):
            ShapeAnalyzer.apply_tensor_parallel(base_shapes, 0, 'attention')
        
        # Negative parallel size should raise error
        with pytest.raises(ValueError, match="Parallel size must be positive"):
            ShapeAnalyzer.apply_tensor_parallel(base_shapes, -1, 'attention')
    
    def test_unknown_component_type(self):
        """Test handling of unknown component types."""
        base_shapes = {'matrix': (4096, 4096)}
        
        # Unknown component type should use default behavior
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(base_shapes, 4, 'unknown')
        
        # Should apply default column-parallel behavior
        assert tp_shapes['matrix'] == (4096, 1024)  # Split output dimension
    
    def test_empty_shapes(self):
        """Test handling of empty shape dictionaries."""
        empty_shapes = {}
        
        # Should handle empty shapes gracefully
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(empty_shapes, 4, 'attention')
        assert tp_shapes == {}
        
        ep_shapes = ShapeAnalyzer.apply_expert_parallel(empty_shapes, 8)
        assert ep_shapes == {}
    
    def test_nested_shape_structures(self):
        """Test handling of deeply nested shape structures."""
        nested_shapes = {
            'level1': {
                'level2': {
                    'level3': {
                        'matrix': (4096, 4096)
                    }
                }
            }
        }
        
        # Should handle nested structures appropriately
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(nested_shapes, 4, 'attention')
        
        # Should preserve structure and modify leaf shapes
        assert 'level1' in tp_shapes
        assert 'level2' in tp_shapes['level1']
        assert 'level3' in tp_shapes['level1']['level2']
    
    def test_mixed_shape_types(self):
        """Test handling of mixed shape types (tuples, lists, etc.)."""
        mixed_shapes = {
            'tuple_shape': (4096, 4096),
            'list_shape': [4096, 4096],
            'nested': {
                'tuple_shape': (2048, 2048)
            }
        }
        
        # Should handle different shape representations
        tp_shapes = ShapeAnalyzer.apply_tensor_parallel(mixed_shapes, 4, 'attention')
        
        # Should convert to consistent format
        assert isinstance(tp_shapes['tuple_shape'], tuple)
        assert tp_shapes['tuple_shape'] == (4096, 1024)


class TestShapeAnalyzerUtilities:
    """Test utility functions in ShapeAnalyzer."""
    
    def test_validate_shapes(self):
        """Test shape validation utility."""
        # Valid shapes
        valid_shapes = {
            'attention': {
                'q_proj': (4096, 4096),
                'k_proj': (4096, 4096)
            },
            'mlp': {
                'gate_proj': (4096, 11008)
            }
        }
        
        assert ShapeAnalyzer.validate_shapes(valid_shapes) is True
        
        # Invalid shapes (negative dimensions)
        invalid_shapes = {
            'attention': {
                'q_proj': (4096, -4096)  # Negative dimension
            }
        }
        
        assert ShapeAnalyzer.validate_shapes(invalid_shapes) is False
    
    def test_get_shape_summary(self):
        """Test shape summary generation."""
        shapes = {
            'attention': {
                'q_proj': (4096, 4096),
                'k_proj': (4096, 4096),
                'v_proj': (4096, 4096),
                'o_proj': (4096, 4096)
            },
            'mlp': {
                'gate_proj': (4096, 11008),
                'up_proj': (4096, 11008),
                'down_proj': (11008, 4096)
            }
        }
        
        summary = ShapeAnalyzer.get_shape_summary(shapes)
        
        assert 'total_parameters' in summary
        assert 'component_breakdown' in summary
        assert summary['total_parameters'] > 0
        
        # Check component breakdown
        breakdown = summary['component_breakdown']
        assert 'attention' in breakdown
        assert 'mlp' in breakdown
        assert breakdown['attention']['parameter_count'] > 0
        assert breakdown['mlp']['parameter_count'] > 0
    
    def test_compare_shapes(self):
        """Test shape comparison utility."""
        shapes1 = {
            'attention': {
                'q_proj': (4096, 4096)
            }
        }
        
        shapes2 = {
            'attention': {
                'q_proj': (4096, 1024)  # Different shape
            }
        }
        
        comparison = ShapeAnalyzer.compare_shapes(shapes1, shapes2)
        
        assert 'differences' in comparison
        assert 'similarities' in comparison
        assert len(comparison['differences']) > 0
        
        # Should identify the difference
        differences = comparison['differences']
        assert any('q_proj' in diff for diff in differences)
    
    def test_estimate_memory_from_shapes(self):
        """Test memory estimation from shapes."""
        shapes = {
            'attention': {
                'q_proj': (4096, 4096),
                'k_proj': (4096, 4096)
            }
        }
        
        memory_estimate = ShapeAnalyzer.estimate_memory_from_shapes(
            shapes, dtype_bytes=4
        )
        
        assert 'total_memory_bytes' in memory_estimate
        assert 'component_memory' in memory_estimate
        assert memory_estimate['total_memory_bytes'] > 0
        
        # Check component breakdown
        component_memory = memory_estimate['component_memory']
        assert 'attention' in component_memory
        assert component_memory['attention'] > 0