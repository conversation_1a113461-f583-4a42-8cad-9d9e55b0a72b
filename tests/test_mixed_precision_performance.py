"""
Performance tests for mixed precision calculations.

These tests validate that mixed precision calculations are efficient
and provide the expected performance characteristics.
"""

import pytest
import time
import psutil
import os
from typing import Dict, List, Tuple
from unittest.mock import Mock

from llm_modeling_metrics.core.operators import (
    MatMulOperator, AttentionOperator, MLPOperator, MoEOperator
)
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel
from tests.conftest import create_mock_model_config, MockConfig


class TestMixedPrecisionPerformance:
    """Test performance characteristics of mixed precision calculations."""
    
    def test_operator_calculation_performance(self):
        """Test that mixed precision operator calculations are efficient."""
        # Test parameters
        M, N, K = 4096, 4096, 4096
        batch_size = 1
        sequence_length = 2048
        
        # Create operators with different precisions
        operators = [
            MatMulOperator(M=M, N=N, K=K, precision='bf16'),
            MatMulOperator(M=M, N=N, K=K, input_precision='bf16', weight_precision='int8', output_precision='bf16'),
            MatMulOperator(M=M, N=N, K=K, input_precision='bf16', weight_precision='fp8', output_precision='bf16'),
            MatMulOperator(M=M, N=N, K=K, input_precision='bf16', weight_precision='fp4', output_precision='bf16'),
        ]
        
        # Measure calculation times
        times = []
        for op in operators:
            start_time = time.time()
            
            # Perform multiple calculations
            for _ in range(100):
                flops = op.compute_flops(batch_size, sequence_length)
                memory = op.compute_memory_capacity_bytes(batch_size, sequence_length)
                movement = op.compute_memory_movement_bytes(batch_size, sequence_length)
                params = op.compute_params()
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        # Mixed precision calculations should not be significantly slower
        base_time = times[0]
        for i, mixed_time in enumerate(times[1:], 1):
            overhead = (mixed_time - base_time) / base_time
            assert overhead < 0.5, f"Operator {i} has excessive overhead: {overhead:.1%}"
    
    def test_attention_operator_kv_cache_performance(self):
        """Test AttentionOperator performance with different KV cache precisions."""
        hidden_size = 4096
        num_heads = 32
        batch_size = 1
        sequence_length = 2048
        
        # Test different KV cache precisions
        precisions = ['bf16', 'fp8', 'fp4']
        times = []
        
        for kv_precision in precisions:
            op = AttentionOperator(
                hidden_size=hidden_size,
                num_heads=num_heads,
                activation_precision='bf16',
                kv_cache_precision=kv_precision,
                batch_size=batch_size,
                sequence_length=sequence_length
            )
            
            start_time = time.time()
            
            # Perform calculations
            for _ in range(50):
                flops = op.compute_flops(batch_size, sequence_length)
                memory = op.compute_memory_capacity_bytes(batch_size, sequence_length)
                movement = op.compute_memory_movement_bytes(batch_size, sequence_length)
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        # All precisions should have similar calculation times
        max_time = max(times)
        min_time = min(times)
        time_variation = (max_time - min_time) / min_time
        
        assert time_variation < 0.3, f"Excessive time variation between precisions: {time_variation:.1%}"
    
    def test_dense_model_memory_calculation_performance(self):
        """Test DenseModel memory calculation performance with mixed precision."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Test different calculation methods
        test_cases = [
            ("legacy", {"dtype": "bf16", "training": True}),
            ("mixed_simple", {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "bf16",
                "training": True
            }),
            ("mixed_complex", {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "fp8",
                "grad_dtype": "fp16",
                "optimizer_dtype": "fp32",
                "training": True
            }),
        ]
        
        times = {}
        
        for test_name, kwargs in test_cases:
            start_time = time.time()
            
            # Perform multiple calculations
            for _ in range(20):
                memory = model.compute_memory_requirements(
                    sequence_length=2048,
                    batch_size=1,
                    **kwargs
                )
            
            end_time = time.time()
            times[test_name] = end_time - start_time
        
        # Mixed precision should not be significantly slower than legacy
        legacy_time = times["legacy"]
        mixed_simple_time = times["mixed_simple"]
        mixed_complex_time = times["mixed_complex"]
        
        simple_overhead = (mixed_simple_time - legacy_time) / legacy_time
        complex_overhead = (mixed_complex_time - legacy_time) / legacy_time
        
        assert simple_overhead < 0.2, f"Simple mixed precision overhead too high: {simple_overhead:.1%}"
        assert complex_overhead < 0.5, f"Complex mixed precision overhead too high: {complex_overhead:.1%}"
    
    def test_moe_model_expert_precision_performance(self):
        """Test MoEModel performance with different expert precisions."""
        config = MockConfig(create_mock_model_config('deepseek_v3'))
        model = MoEModel("test-model", config)
        
        # Test different expert precisions
        expert_precisions = ['bf16', 'fp8', 'fp4']
        times = {}
        
        for expert_precision in expert_precisions:
            start_time = time.time()
            
            # Perform calculations
            for _ in range(10):
                memory = model.compute_memory_requirements(
                    sequence_length=2048,
                    batch_size=1,
                    expert_parameter_dtype=expert_precision,
                    training=True
                )
                active_params = model.compute_active_params_per_token(
                    expert_parameter_dtype=expert_precision
                )
                flops = model.compute_flops(sequence_length=2048, batch_size=1)
            
            end_time = time.time()
            times[expert_precision] = end_time - start_time
        
        # All expert precisions should have similar performance
        max_time = max(times.values())
        min_time = min(times.values())
        time_variation = (max_time - min_time) / min_time
        
        assert time_variation < 0.4, f"Excessive time variation between expert precisions: {time_variation:.1%}"
    
    def test_large_model_mixed_precision_scaling(self):
        """Test mixed precision performance scaling with large models."""
        # Create progressively larger model configs
        model_sizes = [
            {"hidden_size": 2048, "num_hidden_layers": 16, "intermediate_size": 8192},
            {"hidden_size": 4096, "num_hidden_layers": 32, "intermediate_size": 16384},
            {"hidden_size": 8192, "num_hidden_layers": 64, "intermediate_size": 32768},
        ]
        
        times = []
        
        for size_config in model_sizes:
            config_dict = create_mock_model_config('llama')
            config_dict.update(size_config)
            config = MockConfig(config_dict)
            model = DenseModel("test-model", config)
            
            start_time = time.time()
            
            # Perform mixed precision calculation
            memory = model.compute_memory_requirements(
                sequence_length=2048,
                batch_size=1,
                weight_dtype='bf16',
                activation_dtype='bf16',
                kv_cache_dtype='fp8',
                grad_dtype='fp16',
                optimizer_dtype='fp32',
                training=True
            )
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        # Time should scale reasonably with model size
        # Allow for some non-linearity but not excessive
        for i in range(1, len(times)):
            time_ratio = times[i] / times[i-1]
            assert time_ratio < 5.0, f"Time scaling too poor between sizes {i-1} and {i}: {time_ratio:.1f}x"
    
    def test_memory_usage_during_calculations(self):
        """Test memory usage during mixed precision calculations."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Perform many mixed precision calculations
        for _ in range(100):
            memory = model.compute_memory_requirements(
                sequence_length=2048,
                batch_size=1,
                weight_dtype='bf16',
                activation_dtype='bf16',
                kv_cache_dtype='fp8',
                grad_dtype='fp16',
                optimizer_dtype='fp32',
                training=True
            )
            
            flops = model.compute_flops(sequence_length=2048, batch_size=1)
            params = model.get_total_params()
        
        # Check final memory usage
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for calculations)
        assert memory_increase < 100 * 1024 * 1024, \
            f"Excessive memory usage during calculations: {memory_increase / (1024*1024):.1f} MB"
    
    def test_calculation_accuracy_vs_performance_tradeoff(self):
        """Test that performance optimizations don't compromise accuracy."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Calculate memory with high precision (reference)
        memory_reference = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='fp32',
            training=True
        )
        
        # Calculate memory with mixed precision (optimized)
        memory_optimized = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            grad_dtype='fp16',
            optimizer_dtype='fp32',
            training=True
        )
        
        # Parameter counts should be identical
        params_reference = model.get_total_params()
        params_optimized = model.get_total_params()
        assert params_reference == params_optimized
        
        # Memory calculations should be consistent (optimized should be less)
        assert memory_optimized['total'] < memory_reference['total']
        assert memory_optimized['parameters'] < memory_reference['parameters']
        
        # But the calculations should be deterministic
        memory_optimized_2 = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            grad_dtype='fp16',
            optimizer_dtype='fp32',
            training=True
        )
        
        assert memory_optimized == memory_optimized_2


class TestMixedPrecisionBenchmarks:
    """Benchmark tests for mixed precision performance."""
    
    def test_operator_throughput_benchmark(self):
        """Benchmark operator calculation throughput."""
        # Test parameters
        M, N, K = 4096, 4096, 4096
        batch_size = 1
        sequence_length = 2048
        num_iterations = 1000
        
        # Create operators
        operators = {
            'bf16': MatMulOperator(M=M, N=N, K=K, precision='bf16'),
            'mixed_int8': MatMulOperator(M=M, N=N, K=K, 
                                       input_precision='bf16', 
                                       weight_precision='int8', 
                                       output_precision='bf16'),
            'mixed_fp8': MatMulOperator(M=M, N=N, K=K,
                                      input_precision='bf16',
                                      weight_precision='fp8',
                                      output_precision='bf16'),
        }
        
        # Benchmark each operator
        results = {}
        
        for name, op in operators.items():
            start_time = time.time()
            
            for _ in range(num_iterations):
                flops = op.compute_flops(batch_size, sequence_length)
                memory = op.compute_memory_capacity_bytes(batch_size, sequence_length)
                movement = op.compute_memory_movement_bytes(batch_size, sequence_length)
            
            end_time = time.time()
            total_time = end_time - start_time
            throughput = num_iterations / total_time
            
            results[name] = {
                'total_time': total_time,
                'throughput': throughput,
                'time_per_op': total_time / num_iterations * 1000  # ms
            }
        
        # Print benchmark results
        print("\nOperator Throughput Benchmark:")
        print(f"{'Operator':<15} {'Time (s)':<10} {'Throughput (ops/s)':<20} {'Time/op (ms)':<15}")
        print("-" * 65)
        
        for name, result in results.items():
            print(f"{name:<15} {result['total_time']:<10.3f} {result['throughput']:<20.1f} {result['time_per_op']:<15.3f}")
        
        # Verify reasonable performance
        base_throughput = results['bf16']['throughput']
        for name, result in results.items():
            if name != 'bf16':
                throughput_ratio = result['throughput'] / base_throughput
                assert throughput_ratio > 0.5, f"{name} throughput too low: {throughput_ratio:.2f}x base"
    
    def test_model_memory_calculation_benchmark(self):
        """Benchmark model memory calculation performance."""
        configs = [
            ("small", {"hidden_size": 1024, "num_hidden_layers": 12, "intermediate_size": 4096}),
            ("medium", {"hidden_size": 4096, "num_hidden_layers": 32, "intermediate_size": 16384}),
            ("large", {"hidden_size": 8192, "num_hidden_layers": 64, "intermediate_size": 32768}),
        ]
        
        num_iterations = 50
        results = {}
        
        for size_name, size_config in configs:
            config_dict = create_mock_model_config('llama')
            config_dict.update(size_config)
            config = MockConfig(config_dict)
            model = DenseModel(f"test-model-{size_name}", config)
            
            # Benchmark mixed precision calculation
            start_time = time.time()
            
            for _ in range(num_iterations):
                memory = model.compute_memory_requirements(
                    sequence_length=2048,
                    batch_size=1,
                    weight_dtype='bf16',
                    activation_dtype='bf16',
                    kv_cache_dtype='fp8',
                    grad_dtype='fp16',
                    optimizer_dtype='fp32',
                    training=True
                )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            results[size_name] = {
                'total_time': total_time,
                'time_per_calc': total_time / num_iterations * 1000,  # ms
                'params': model.get_total_params()
            }
        
        # Print benchmark results
        print("\nModel Memory Calculation Benchmark:")
        print(f"{'Model Size':<10} {'Parameters':<15} {'Total Time (s)':<15} {'Time/calc (ms)':<15}")
        print("-" * 60)
        
        for size_name, result in results.items():
            params_str = f"{result['params'] / 1e9:.1f}B"
            print(f"{size_name:<10} {params_str:<15} {result['total_time']:<15.3f} {result['time_per_calc']:<15.3f}")
        
        # Verify scaling is reasonable
        small_time = results['small']['time_per_calc']
        medium_time = results['medium']['time_per_calc']
        large_time = results['large']['time_per_calc']
        
        # Time should scale sub-linearly with model size
        medium_ratio = medium_time / small_time
        large_ratio = large_time / medium_time
        
        assert medium_ratio < 10, f"Medium model scaling too poor: {medium_ratio:.1f}x"
        assert large_ratio < 10, f"Large model scaling too poor: {large_ratio:.1f}x"
    
    def test_moe_model_benchmark(self):
        """Benchmark MoE model calculations with mixed precision."""
        config = MockConfig(create_mock_model_config('deepseek_v3'))
        model = MoEModel("test-moe", config)
        
        num_iterations = 20
        
        # Benchmark different expert precisions
        expert_precisions = ['bf16', 'fp8', 'fp4']
        results = {}
        
        for expert_precision in expert_precisions:
            start_time = time.time()
            
            for _ in range(num_iterations):
                memory = model.compute_memory_requirements(
                    sequence_length=2048,
                    batch_size=1,
                    expert_parameter_dtype=expert_precision,
                    training=True
                )
                active_params = model.compute_active_params_per_token(
                    expert_parameter_dtype=expert_precision
                )
                flops = model.compute_flops(sequence_length=2048, batch_size=1)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            results[expert_precision] = {
                'total_time': total_time,
                'time_per_calc': total_time / num_iterations * 1000,  # ms
            }
        
        # Print benchmark results
        print("\nMoE Model Mixed Precision Benchmark:")
        print(f"{'Expert Precision':<15} {'Total Time (s)':<15} {'Time/calc (ms)':<15}")
        print("-" * 50)
        
        for precision, result in results.items():
            print(f"{precision:<15} {result['total_time']:<15.3f} {result['time_per_calc']:<15.3f}")
        
        # Verify all precisions have reasonable performance
        times = [result['time_per_calc'] for result in results.values()]
        max_time = max(times)
        min_time = min(times)
        time_variation = (max_time - min_time) / min_time
        
        assert time_variation < 0.5, f"Excessive time variation between expert precisions: {time_variation:.1%}"
    
    def test_concurrent_calculation_performance(self):
        """Test performance under concurrent mixed precision calculations."""
        import threading
        import queue
        
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        num_threads = 4
        calculations_per_thread = 25
        results_queue = queue.Queue()
        
        def worker():
            thread_start = time.time()
            
            for _ in range(calculations_per_thread):
                memory = model.compute_memory_requirements(
                    sequence_length=2048,
                    batch_size=1,
                    weight_dtype='bf16',
                    activation_dtype='bf16',
                    kv_cache_dtype='fp8',
                    grad_dtype='fp16',
                    optimizer_dtype='fp32',
                    training=True
                )
            
            thread_end = time.time()
            results_queue.put(thread_end - thread_start)
        
        # Start concurrent workers
        overall_start = time.time()
        threads = []
        
        for _ in range(num_threads):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        overall_end = time.time()
        
        # Collect results
        thread_times = []
        while not results_queue.empty():
            thread_times.append(results_queue.get())
        
        overall_time = overall_end - overall_start
        total_calculations = num_threads * calculations_per_thread
        overall_throughput = total_calculations / overall_time
        
        print(f"\nConcurrent Calculation Performance:")
        print(f"Threads: {num_threads}")
        print(f"Calculations per thread: {calculations_per_thread}")
        print(f"Total calculations: {total_calculations}")
        print(f"Overall time: {overall_time:.3f}s")
        print(f"Overall throughput: {overall_throughput:.1f} calc/s")
        print(f"Average thread time: {sum(thread_times) / len(thread_times):.3f}s")
        
        # Verify reasonable concurrent performance
        assert overall_throughput > 10, f"Concurrent throughput too low: {overall_throughput:.1f} calc/s"
        
        # Thread times should be reasonably consistent
        max_thread_time = max(thread_times)
        min_thread_time = min(thread_times)
        time_variation = (max_thread_time - min_thread_time) / min_thread_time
        
        assert time_variation < 1.0, f"Excessive thread time variation: {time_variation:.1%}"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])  # -s to show print statements