"""
Unit tests for BaseModel interface and core functionality.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from llm_modeling_metrics.core.base_model import BaseModel, ModelMetrics, ParallelConfig
from tests.conftest import (
    assert_metrics_valid, assert_parallel_config_valid, 
    create_mock_model_config, MockConfig
)


class ConcreteModel(BaseModel):
    """Concrete implementation of BaseModel for testing."""
    
    def _parse_config(self):
        """Parse the model configuration."""
        self._parsed_config = {
            'hidden_size': getattr(self.config, 'hidden_size', 4096),
            'num_attention_heads': getattr(self.config, 'num_attention_heads', 32),
            'num_hidden_layers': getattr(self.config, 'num_hidden_layers', 32),
            'intermediate_size': getattr(self.config, 'intermediate_size', 11008),
            'vocab_size': getattr(self.config, 'vocab_size', 32000),
        }
    
    def compute_attention_params(self) -> int:
        """Compute attention parameters."""
        hidden_size = self._parsed_config['hidden_size']
        return hidden_size * hidden_size * 4  # Q, K, V, O projections
    
    def compute_mlp_params(self) -> int:
        """Compute MLP parameters."""
        hidden_size = self._parsed_config['hidden_size']
        intermediate_size = self._parsed_config['intermediate_size']
        return hidden_size * intermediate_size * 3  # gate, up, down
    
    def compute_embedding_params(self) -> int:
        """Compute embedding parameters."""
        hidden_size = self._parsed_config['hidden_size']
        vocab_size = self._parsed_config['vocab_size']
        return vocab_size * hidden_size * 2  # input + output embeddings
    
    def compute_flops(self, sequence_length: int = 2048, batch_size: int = 1) -> dict:
        """Compute FLOPs."""
        hidden_size = self._parsed_config['hidden_size']
        base_flops = batch_size * sequence_length * hidden_size * 1000
        return {
            'attention': base_flops * 2,
            'mlp': base_flops * 3,
            'total': base_flops * 5
        }
    
    def compute_memory_requirements(self, sequence_length: int = 2048, 
                                  batch_size: int = 1) -> dict:
        """Compute memory requirements."""
        params = self.get_total_params()
        activations = batch_size * sequence_length * self._parsed_config['hidden_size'] * 4
        return {
            'parameters': params * 4,  # FP32
            'activations': activations * 4,  # FP32
            'total': (params + activations) * 4
        }
    
    def get_matrix_shapes(self, parallel_config=None) -> dict:
        """Get matrix shapes."""
        hidden_size = self._parsed_config['hidden_size']
        intermediate_size = self._parsed_config['intermediate_size']
        
        shapes = {
            'attention': {
                'q_proj': (hidden_size, hidden_size),
                'k_proj': (hidden_size, hidden_size),
                'v_proj': (hidden_size, hidden_size),
                'o_proj': (hidden_size, hidden_size)
            },
            'mlp': {
                'gate_proj': (hidden_size, intermediate_size),
                'up_proj': (hidden_size, intermediate_size),
                'down_proj': (intermediate_size, hidden_size)
            }
        }
        
        if parallel_config and parallel_config.tensor_parallel_size > 1:
            tp_size = parallel_config.tensor_parallel_size
            # Modify shapes for tensor parallelism
            shapes['attention']['q_proj'] = (hidden_size, hidden_size // tp_size)
            shapes['attention']['k_proj'] = (hidden_size, hidden_size // tp_size)
            shapes['attention']['v_proj'] = (hidden_size, hidden_size // tp_size)
            shapes['attention']['o_proj'] = (hidden_size // tp_size, hidden_size)
            
            shapes['mlp']['gate_proj'] = (hidden_size, intermediate_size // tp_size)
            shapes['mlp']['up_proj'] = (hidden_size, intermediate_size // tp_size)
            shapes['mlp']['down_proj'] = (intermediate_size // tp_size, hidden_size)
        
        return shapes


class TestParallelConfig:
    """Test ParallelConfig dataclass."""
    
    def test_default_values(self):
        """Test default parallel configuration values."""
        config = ParallelConfig()
        assert config.tensor_parallel_size == 1
        assert config.pipeline_parallel_size == 1
        assert config.data_parallel_size == 1
        assert config.expert_parallel_size == 1
        assert config.expert_data_parallel_size == 1
    
    def test_custom_values(self):
        """Test custom parallel configuration values."""
        config = ParallelConfig(
            tensor_parallel_size=4,
            pipeline_parallel_size=2,
            data_parallel_size=8,
            expert_parallel_size=16,
            expert_data_parallel_size=4
        )
        assert config.tensor_parallel_size == 4
        assert config.pipeline_parallel_size == 2
        assert config.data_parallel_size == 8
        assert config.expert_parallel_size == 16
        assert config.expert_data_parallel_size == 4
    
    def test_validation_errors(self):
        """Test validation of parallel configuration parameters."""
        with pytest.raises(ValueError, match="tensor_parallel_size must be >= 1"):
            ParallelConfig(tensor_parallel_size=0)
        
        with pytest.raises(ValueError, match="pipeline_parallel_size must be >= 1"):
            ParallelConfig(pipeline_parallel_size=-1)
        
        with pytest.raises(ValueError, match="data_parallel_size must be >= 1"):
            ParallelConfig(data_parallel_size=0)
        
        with pytest.raises(ValueError, match="expert_parallel_size must be >= 1"):
            ParallelConfig(expert_parallel_size=-2)
        
        with pytest.raises(ValueError, match="expert_data_parallel_size must be >= 1"):
            ParallelConfig(expert_data_parallel_size=0)
    
    def test_valid_configurations(self, parallel_configs):
        """Test that all fixture parallel configurations are valid."""
        for name, config in parallel_configs.items():
            assert_parallel_config_valid(config)


class TestModelMetrics:
    """Test ModelMetrics dataclass."""
    
    def test_creation_with_required_fields(self):
        """Test ModelMetrics creation with required fields."""
        metrics = ModelMetrics(
            model_name="test-model",
            architecture="test-arch",
            total_params=1000000,
            attention_params=400000,
            mlp_params=500000,
            embedding_params=100000,
            flops_forward=2000000000,
            flops_per_token=1000000,
            memory_params=4000000,
            memory_activations=1000000,
            memory_total=5000000
        )
        
        assert metrics.model_name == "test-model"
        assert metrics.architecture == "test-arch"
        assert metrics.total_params == 1000000
        assert metrics.sequence_length == 2048  # default
        assert metrics.batch_size == 1  # default
        assert isinstance(metrics.timestamp, datetime)
    
    def test_to_dict_conversion(self, sample_model_metrics):
        """Test conversion of ModelMetrics to dictionary."""
        metrics_dict = sample_model_metrics.to_dict()
        
        # Check required fields
        assert metrics_dict['model_name'] == sample_model_metrics.model_name
        assert metrics_dict['architecture'] == sample_model_metrics.architecture
        assert metrics_dict['total_params'] == sample_model_metrics.total_params
        assert metrics_dict['flops_forward'] == sample_model_metrics.flops_forward
        assert metrics_dict['memory_total'] == sample_model_metrics.memory_total
        
        # Check shapes
        assert 'attention_shapes' in metrics_dict
        assert 'mlp_shapes' in metrics_dict
        
        # Check timestamp is ISO format
        assert isinstance(metrics_dict['timestamp'], str)
        datetime.fromisoformat(metrics_dict['timestamp'])  # Should not raise
    
    def test_to_dict_with_parallel_config(self, sample_model_metrics):
        """Test to_dict with parallel configuration."""
        parallel_config = ParallelConfig(tensor_parallel_size=4, pipeline_parallel_size=2)
        sample_model_metrics.parallel_config = parallel_config
        
        metrics_dict = sample_model_metrics.to_dict()
        
        assert 'parallel_config' in metrics_dict
        pc_dict = metrics_dict['parallel_config']
        assert pc_dict['tensor_parallel_size'] == 4
        assert pc_dict['pipeline_parallel_size'] == 2
        assert pc_dict['data_parallel_size'] == 1
    
    def test_to_dict_with_moe_fields(self, sample_model_metrics):
        """Test to_dict with MoE-specific fields."""
        sample_model_metrics.experts_per_token = 8
        sample_model_metrics.active_params_per_token = 2000000000
        
        metrics_dict = sample_model_metrics.to_dict()
        
        assert metrics_dict['experts_per_token'] == 8
        assert metrics_dict['active_params_per_token'] == 2000000000


class TestBaseModel:
    """Test BaseModel abstract base class."""
    
    def test_initialization_with_config(self, mock_llama_config):
        """Test BaseModel initialization with provided config."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        assert model.model_name == "test-model"
        assert model.config == mock_llama_config
        assert model._parsed_config['hidden_size'] == 4096
        assert model._parsed_config['num_attention_heads'] == 32
    
    def test_initialization_without_config(self):
        """Test BaseModel initialization without config."""
        # This should work since ConcreteModel doesn't require config fetching
        model = ConcreteModel("test-model", None)
        assert model.model_name == "test-model"
        assert model.config is None
    
    def test_parameter_computation(self, mock_llama_config):
        """Test parameter computation methods."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        attention_params = model.compute_attention_params()
        mlp_params = model.compute_mlp_params()
        embedding_params = model.compute_embedding_params()
        total_params = model.get_total_params()
        
        assert attention_params > 0
        assert mlp_params > 0
        assert embedding_params > 0
        assert total_params == attention_params + mlp_params + embedding_params
    
    def test_flops_computation(self, mock_llama_config):
        """Test FLOP computation."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test with default parameters
        flops = model.compute_flops()
        assert 'total' in flops
        assert flops['total'] > 0
        assert 'attention' in flops
        assert 'mlp' in flops
        
        # Test with custom parameters
        flops_custom = model.compute_flops(sequence_length=1024, batch_size=2)
        # Should be different due to different sequence length and batch size
        expected_ratio = (1024 * 2) / (2048 * 1)  # seq_len * batch_size ratio
        assert abs(flops_custom['total'] / flops['total'] - expected_ratio) < 0.1
    
    def test_memory_computation(self, mock_llama_config):
        """Test memory requirement computation."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        memory = model.compute_memory_requirements()
        assert 'parameters' in memory
        assert 'activations' in memory
        assert 'total' in memory
        assert memory['parameters'] > 0
        assert memory['activations'] > 0
        assert memory['total'] > 0
    
    def test_matrix_shapes(self, mock_llama_config):
        """Test matrix shape computation."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test without parallel config
        shapes = model.get_matrix_shapes()
        assert 'attention' in shapes
        assert 'mlp' in shapes
        assert 'q_proj' in shapes['attention']
        assert shapes['attention']['q_proj'] == (4096, 4096)
        
        # Test with tensor parallelism
        parallel_config = ParallelConfig(tensor_parallel_size=2)
        parallel_shapes = model.get_matrix_shapes(parallel_config)
        assert parallel_shapes['attention']['q_proj'] == (4096, 2048)  # Split output dim
        assert parallel_shapes['attention']['o_proj'] == (2048, 4096)  # Split input dim
    
    def test_get_metrics(self, mock_llama_config):
        """Test comprehensive metrics computation."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        metrics = model.get_metrics()
        assert_metrics_valid(metrics)
        assert metrics.model_name == "test-model"
        assert metrics.architecture in ['llama', 'unknown']  # Depends on inference
        
        # Test with custom parameters
        parallel_config = ParallelConfig(tensor_parallel_size=4)
        metrics_parallel = model.get_metrics(
            sequence_length=1024, 
            batch_size=2, 
            parallel_config=parallel_config
        )
        assert_metrics_valid(metrics_parallel)
        assert metrics_parallel.sequence_length == 1024
        assert metrics_parallel.batch_size == 2
        assert metrics_parallel.parallel_config == parallel_config
    
    def test_parallel_config_validation(self, mock_llama_config):
        """Test parallel configuration validation."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Valid configuration
        valid_config = ParallelConfig(tensor_parallel_size=2)
        assert model.validate_parallel_config(valid_config) is True
        
        # Invalid configuration (heads not divisible)
        invalid_config = ParallelConfig(tensor_parallel_size=5)  # 32 heads not divisible by 5
        assert model.validate_parallel_config(invalid_config) is False
    
    @patch('llm_modeling_metrics.utils.caching.get_cache_manager')
    def test_caching_functionality(self, mock_cache_manager, mock_llama_config):
        """Test that caching is properly integrated."""
        # Setup mock cache manager
        mock_cache = Mock()
        mock_cache_manager.return_value.computation_cache = mock_cache
        
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Call cached methods multiple times
        params1 = model._get_cached_attention_params()
        params2 = model._get_cached_attention_params()
        
        # Should return same result
        assert params1 == params2
        
        # Test cache invalidation
        model.invalidate_cache()
        # Check that invalidate was called multiple times (once for each cached method)
        assert mock_cache.invalidate.call_count >= 3  # attention, mlp, embedding params
    
    def test_edge_cases(self):
        """Test edge cases and error conditions."""
        # Test with minimal config
        minimal_config = MockConfig({
            'hidden_size': 1,
            'num_attention_heads': 1,
            'num_hidden_layers': 1,
            'intermediate_size': 1,
            'vocab_size': 1
        })
        
        model = ConcreteModel("minimal-model", minimal_config)
        metrics = model.get_metrics(sequence_length=1, batch_size=1)
        assert_metrics_valid(metrics)
        
        # Test with zero sequence length
        flops = model.compute_flops(sequence_length=0)
        assert flops['total'] == 0
        
        # Test metrics with zero sequence length
        metrics_zero = model.get_metrics(sequence_length=0)
        assert metrics_zero.flops_per_token == 0
    
    def test_architecture_inference(self, mock_llama_config):
        """Test architecture inference from model name and config."""
        # Test Llama inference
        llama_model = ConcreteModel("meta-llama/Llama-2-7b-hf", mock_llama_config)
        metrics = llama_model.get_metrics()
        assert metrics.architecture == 'llama'
        
        # Test DeepSeek inference
        deepseek_config = MockConfig({'model_type': 'deepseek_v3'})
        deepseek_model = ConcreteModel("deepseek-ai/DeepSeek-R1", deepseek_config)
        metrics = deepseek_model.get_metrics()
        assert metrics.architecture == 'deepseek_v3'
        
        # Test unknown architecture
        unknown_model = ConcreteModel("unknown/model", MockConfig({'model_type': 'unknown'}))
        metrics = unknown_model.get_metrics()
        assert metrics.architecture == 'unknown'


class TestBaseModelEdgeCases:
    """Test edge cases and error conditions for BaseModel."""
    
    def test_abstract_method_enforcement(self):
        """Test that abstract methods cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            BaseModel("test-model")
    
    def test_invalid_parallel_config_values(self, mock_llama_config):
        """Test validation of invalid parallel configuration values."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test with invalid tensor parallel size (not divisible)
        invalid_config = ParallelConfig(tensor_parallel_size=7)  # 32 heads not divisible by 7
        assert not model.validate_parallel_config(invalid_config)
        
        # Test with valid tensor parallel size
        valid_config = ParallelConfig(tensor_parallel_size=4)  # 32 heads divisible by 4
        assert model.validate_parallel_config(valid_config)
    
    def test_config_parsing_errors(self):
        """Test error handling in configuration parsing."""
        # Test with None config
        model = ConcreteModel("test-model", None)
        assert model.config is None
        assert model._parsed_config == {}
        
        # Test with empty config - ConcreteModel uses defaults
        empty_config = MockConfig({})
        model = ConcreteModel("test-model", empty_config)
        # Should handle missing fields gracefully with defaults
        assert model._parsed_config['hidden_size'] == 4096  # Default value
        assert model._parsed_config['num_attention_heads'] == 32  # Default value
    
    def test_zero_parameter_edge_cases(self):
        """Test behavior with zero or minimal parameters."""
        minimal_config = MockConfig({
            'hidden_size': 0,
            'num_attention_heads': 0,
            'num_hidden_layers': 0,
            'intermediate_size': 0,
            'vocab_size': 0
        })
        
        model = ConcreteModel("minimal-model", minimal_config)
        
        # Should handle zero parameters gracefully
        assert model.compute_attention_params() == 0
        assert model.compute_mlp_params() == 0
        assert model.get_total_params() == 0
        
        # FLOPs should be zero with zero hidden_size
        flops = model.compute_flops()
        assert flops['total'] == 0
    
    def test_extreme_sequence_lengths(self, mock_llama_config):
        """Test behavior with extreme sequence lengths."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test with very large sequence length
        large_seq_metrics = model.get_metrics(sequence_length=100000, batch_size=1)
        assert_metrics_valid(large_seq_metrics)
        assert large_seq_metrics.sequence_length == 100000
        
        # Test with sequence length of 1
        small_seq_metrics = model.get_metrics(sequence_length=1, batch_size=1)
        assert_metrics_valid(small_seq_metrics)
        assert small_seq_metrics.sequence_length == 1
        
        # Larger sequence should have more FLOPs
        assert large_seq_metrics.flops_forward > small_seq_metrics.flops_forward
    
    def test_extreme_batch_sizes(self, mock_llama_config):
        """Test behavior with extreme batch sizes."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test with large batch size
        large_batch_metrics = model.get_metrics(sequence_length=2048, batch_size=128)
        assert_metrics_valid(large_batch_metrics)
        assert large_batch_metrics.batch_size == 128
        
        # Test with batch size of 1
        small_batch_metrics = model.get_metrics(sequence_length=2048, batch_size=1)
        assert_metrics_valid(small_batch_metrics)
        
        # Larger batch should have more FLOPs
        assert large_batch_metrics.flops_forward > small_batch_metrics.flops_forward
    
    def test_memory_computation_edge_cases(self, mock_llama_config):
        """Test memory computation with edge cases."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test with zero sequence length
        memory_zero_seq = model.compute_memory_requirements(sequence_length=0, batch_size=1)
        assert memory_zero_seq['activations'] == 0
        assert memory_zero_seq['parameters'] > 0  # Parameters should still exist
        
        # Test with zero batch size
        memory_zero_batch = model.compute_memory_requirements(sequence_length=2048, batch_size=0)
        assert memory_zero_batch['activations'] == 0
        assert memory_zero_batch['parameters'] > 0
    
    def test_matrix_shapes_edge_cases(self, mock_llama_config):
        """Test matrix shape computation with edge cases."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        # Test with extreme tensor parallel sizes
        extreme_tp_config = ParallelConfig(tensor_parallel_size=32)  # Equal to number of heads
        shapes = model.get_matrix_shapes(extreme_tp_config)
        
        # Should handle extreme parallelism
        assert shapes['attention']['q_proj'][1] == 4096 // 32  # 128
        assert shapes['attention']['o_proj'][0] == 4096 // 32  # 128
    
    def test_architecture_inference_edge_cases(self):
        """Test architecture inference with various model names and configs."""
        test_cases = [
            ("unknown-model", MockConfig({'model_type': 'unknown'}), 'unknown'),
            ("custom/model", MockConfig({}), 'unknown'),  # No model_type, no inference from name
            ("meta-llama/custom-model", MockConfig({}), 'llama'),  # No model_type, inferred from name
            ("deepseek-ai/custom", MockConfig({'model_type': 'custom'}), 'custom'),
            ("deepseek-ai/test", MockConfig({}), 'deepseek'),  # Inferred from name
        ]
        
        for model_name, config, expected_arch in test_cases:
            model = ConcreteModel(model_name, config)
            metrics = model.get_metrics()
            assert metrics.architecture == expected_arch


class TestBaseModelIntegration:
    """Integration tests for BaseModel with different configurations."""
    
    def test_different_model_sizes(self):
        """Test BaseModel with different model sizes."""
        sizes = [
            {'hidden_size': 768, 'num_attention_heads': 12, 'intermediate_size': 3072},  # Small
            {'hidden_size': 4096, 'num_attention_heads': 32, 'intermediate_size': 11008},  # Medium
            {'hidden_size': 8192, 'num_attention_heads': 64, 'intermediate_size': 22016},  # Large
        ]
        
        previous_params = 0
        for size_config in sizes:
            config = MockConfig({
                **create_mock_model_config("llama"),
                **size_config
            })
            
            model = ConcreteModel(f"test-model-{size_config['hidden_size']}", config)
            metrics = model.get_metrics()
            assert_metrics_valid(metrics)
            
            # Larger models should have more parameters and FLOPs
            assert metrics.total_params > previous_params
            assert metrics.flops_forward > 0
            previous_params = metrics.total_params
    
    def test_different_sequence_lengths(self, mock_llama_config):
        """Test BaseModel with different sequence lengths."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        sequence_lengths = [512, 1024, 2048, 4096, 8192]
        previous_flops = 0
        
        for seq_len in sequence_lengths:
            metrics = model.get_metrics(sequence_length=seq_len)
            assert_metrics_valid(metrics)
            assert metrics.sequence_length == seq_len
            
            # FLOPs should increase with sequence length
            assert metrics.flops_forward > previous_flops
            previous_flops = metrics.flops_forward
    
    def test_different_batch_sizes(self, mock_llama_config):
        """Test BaseModel with different batch sizes."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        batch_sizes = [1, 2, 4, 8, 16]
        previous_flops = 0
        
        for batch_size in batch_sizes:
            metrics = model.get_metrics(batch_size=batch_size)
            assert_metrics_valid(metrics)
            assert metrics.batch_size == batch_size
            
            # FLOPs should increase with batch size
            assert metrics.flops_forward > previous_flops
            previous_flops = metrics.flops_forward
    
    def test_parallel_configurations(self, mock_llama_config, parallel_configs):
        """Test BaseModel with different parallel configurations."""
        model = ConcreteModel("test-model", mock_llama_config)
        
        for config_name, parallel_config in parallel_configs.items():
            if parallel_config.tensor_parallel_size <= 32:  # Valid for 32 heads
                metrics = model.get_metrics(parallel_config=parallel_config)
                assert_metrics_valid(metrics)
                assert metrics.parallel_config == parallel_config
                
                # Check that shapes are modified for tensor parallelism
                if parallel_config.tensor_parallel_size > 1:
                    shapes = model.get_matrix_shapes(parallel_config)
                    original_shapes = model.get_matrix_shapes()
                    
                    # Some shapes should be different
                    assert shapes != original_shapes