"""
Comprehensive test suite for mixed precision support.

This test suite covers:
1. Unit tests for each enhanced operator with different precision combinations
2. Integration tests for DenseModel and MoEModel mixed precision workflows
3. Backward compatibility tests to ensure existing code works unchanged
4. API tests for mixed precision endpoints
5. Performance tests to validate calculation efficiency
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List, Tuple

from llm_modeling_metrics.core.operators import (
    BaseOperator, MatMulOperator, AttentionOperator, MLPOperator, MoEOperator,
    PRECISION_BYTES, MixedPrecisionConfig, validate_precision_type,
    validate_precision_compatibility, validate_mixed_precision_config,
    UnsupportedPrecisionError, IncompatiblePrecisionError
)
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel
from llm_modeling_metrics.core.base_model import ParallelConfig
from tests.conftest import create_mock_model_config, MockConfig


class TestMixedPrecisionOperators:
    """Test mixed precision support in individual operators."""
    
    def test_matmul_operator_mixed_precision(self):
        """Test MatMulOperator with different precision combinations."""
        test_cases = [
            # (input_precision, weight_precision, output_precision, expected_memory_ratio)
            ('bf16', 'bf16', 'bf16', 1.0),
            ('bf16', 'int8', 'bf16', 0.75),  # Weight is half size
            ('bf16', 'fp8', 'bf16', 0.75),   # Weight is half size
            ('bf16', 'fp4', 'bf16', 0.625),  # Weight is quarter size
            ('fp16', 'fp16', 'fp32', 1.5),   # Output is double size
        ]
        
        M, N, K = 1024, 2048, 1024
        base_op = MatMulOperator(M=M, N=N, K=K, precision='bf16')
        base_memory = base_op.compute_memory_capacity_bytes(batch_size=1, sequence_length=1)
        
        for input_prec, weight_prec, output_prec, expected_ratio in test_cases:
            op = MatMulOperator(
                M=M, N=N, K=K,
                input_precision=input_prec,
                weight_precision=weight_prec,
                output_precision=output_prec
            )
            
            memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=1)
            actual_ratio = memory / base_memory
            
            # Allow 15% tolerance for rounding and implementation differences
            assert abs(actual_ratio - expected_ratio) < 0.15, \
                f"Memory ratio for {input_prec}/{weight_prec}/{output_prec} should be ~{expected_ratio}, got {actual_ratio}"
    
    def test_attention_operator_kv_cache_precision(self):
        """Test AttentionOperator with different KV cache precisions."""
        hidden_size = 4096
        num_heads = 32
        sequence_length = 2048
        batch_size = 1
        
        # Test different KV cache precisions
        test_cases = [
            ('bf16', 'bf16', 1.0),    # Same precision
            ('bf16', 'fp8', 0.5),     # Half precision cache
            ('bf16', 'fp4', 0.25),    # Quarter precision cache
        ]
        
        for activation_prec, kv_cache_prec, expected_ratio in test_cases:
            op = AttentionOperator(
                hidden_size=hidden_size,
                num_heads=num_heads,
                activation_precision=activation_prec,
                kv_cache_precision=kv_cache_prec,
                batch_size=batch_size,
                sequence_length=sequence_length
            )
            
            # Test memory calculation includes KV cache precision
            memory = op.compute_memory_capacity_bytes(batch_size, sequence_length)
            assert memory > 0
            
            # Test that KV cache precision is correctly stored
            assert op.kv_cache_precision == kv_cache_prec
            assert op.activation_precision == activation_prec
    
    def test_mlp_operator_mixed_precision(self):
        """Test MLPOperator with mixed precision."""
        hidden_size = 4096
        intermediate_size = 11008
        
        # Test with different weight and activation precisions
        op = MLPOperator(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            weight_precision='int8',
            activation_precision='bf16'
        )
        
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)
        flops = op.compute_flops(batch_size=1, sequence_length=2048)
        
        assert memory > 0
        assert flops > 0
        assert op.weight_precision == 'int8'
        assert op.activation_precision == 'bf16'
    
    def test_moe_operator_expert_precision(self):
        """Test MoEOperator with different expert parameter precisions."""
        hidden_size = 4096
        intermediate_size = 1407
        num_experts = 64
        experts_per_token = 6
        
        # Test with different expert parameter precisions
        test_cases = ['bf16', 'fp8', 'fp4']
        
        for expert_prec in test_cases:
            op = MoEOperator(
                hidden_size=hidden_size,
                intermediate_size=intermediate_size,
                num_experts=num_experts,
                experts_per_token=experts_per_token,
                expert_parameter_precision=expert_prec
            )
            
            memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)
            params = op.compute_params()
            
            assert memory > 0
            assert params > 0
            assert op.expert_parameter_precision == expert_prec
    
    def test_operator_precision_validation(self):
        """Test precision validation in operators."""
        # Test valid precisions
        valid_precisions = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        for precision in valid_precisions:
            op = MatMulOperator(M=10, N=20, K=30, input_precision=precision)
            assert op.input_precision == precision
        
        # Test invalid precisions
        invalid_precisions = ['fp64', 'int16', 'invalid']
        for precision in invalid_precisions:
            with pytest.raises(UnsupportedPrecisionError):
                MatMulOperator(M=10, N=20, K=30, input_precision=precision)
        
        # Test empty string (should fall back to default due to Python's 'or' behavior)
        op_empty = MatMulOperator(M=10, N=20, K=30, input_precision='')
        # Empty string is falsy, so it should fall back to activation_precision or precision
        assert op_empty.input_precision == 'bf16'
        
        # Test that None is handled gracefully (should not raise)
        op_with_none = MatMulOperator(M=10, N=20, K=30, input_precision=None)
        assert op_with_none.input_precision == 'bf16'  # Should use default
    
    def test_operator_bytes_per_element(self):
        """Test bytes per element calculation for different precisions."""
        op = MatMulOperator(M=10, N=20, K=30, precision='bf16')
        
        expected_bytes = {
            'fp32': 4,
            'fp16': 2,
            'bf16': 2,
            'int8': 1,
            'fp8': 1,
            'fp4': 0.5
        }
        
        for precision, expected in expected_bytes.items():
            bytes_per_elem = op.get_bytes_per_element(precision)
            assert bytes_per_elem == expected, \
                f"Expected {expected} bytes for {precision}, got {bytes_per_elem}"


class TestMixedPrecisionModels:
    """Test mixed precision support in model classes."""
    
    def test_dense_model_mixed_precision_memory(self):
        """Test DenseModel memory computation with mixed precision."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Test with mixed precision parameters
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            grad_dtype='fp16',
            optimizer_dtype='fp32',
            training=True,
            include_kv_cache=True
        )
        
        # Should have all expected components
        expected_keys = ['parameters', 'activations', 'kv_cache', 'gradients', 'optimizer_states', 'total']
        for key in expected_keys:
            assert key in memory, f"Missing key: {key}"
            assert memory[key] >= 0, f"Negative memory for {key}: {memory[key]}"
        
        # Total should be sum of components
        component_sum = sum(memory[k] for k in expected_keys if k != 'total')
        assert abs(memory['total'] - component_sum) <= 1, \
            f"Total memory {memory['total']} should equal sum of components {component_sum}"
    
    def test_dense_model_backward_compatibility(self):
        """Test that DenseModel maintains backward compatibility."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Test legacy dtype parameter
        memory_legacy = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='bf16',
            training=True
        )
        
        # Test new mixed precision parameters with same values
        # Note: optimizer_dtype defaults to fp32 for consistency with legacy behavior
        memory_mixed = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='bf16',
            grad_dtype='bf16',
            optimizer_dtype='fp32',  # Same as legacy default
            training=True
        )
        
        # Results should be identical or very close for numeric values
        numeric_keys = ['parameters', 'activations', 'total', 'gradients', 'optimizer_states']
        for key in numeric_keys:
            if key in memory_legacy and key in memory_mixed:
                if isinstance(memory_legacy[key], (int, float)) and isinstance(memory_mixed[key], (int, float)):
                    diff_ratio = abs(memory_legacy[key] - memory_mixed[key]) / max(memory_legacy[key], 1)
                    assert diff_ratio < 0.01, \
                        f"Backward compatibility failed for {key}: legacy={memory_legacy[key]}, mixed={memory_mixed[key]}"
    
    def test_dense_model_precision_precedence(self):
        """Test that new precision parameters take precedence over legacy dtype."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Test with both legacy and new parameters
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='fp32',  # Legacy parameter
            weight_dtype='bf16',  # Should override legacy
            activation_dtype='fp16',  # Should override legacy
            training=False
        )
        
        # Should use new parameters, not legacy
        assert memory['parameters'] > 0
        assert memory['activations'] > 0
        
        # Memory should reflect mixed precision, not fp32 everywhere
        memory_fp32 = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='fp32',
            training=False
        )
        
        # Mixed precision should use less memory than all fp32
        assert memory['total'] < memory_fp32['total']
    
    def test_moe_model_mixed_precision_memory(self):
        """Test MoEModel memory computation with mixed precision."""
        config = MockConfig(create_mock_model_config('deepseek_v3'))
        model = MoEModel("test-model", config)
        
        # Test with MoE-specific mixed precision
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            expert_parameter_dtype='fp8',  # MoE-specific
            attention_parameter_dtype='bf16',
            training=True
        )
        
        # Should have MoE-specific components
        expected_keys = ['parameters', 'activations', 'total']
        for key in expected_keys:
            assert key in memory, f"Missing key: {key}"
            assert memory[key] >= 0, f"Negative memory for {key}: {memory[key]}"
        
        # Expert parameters should benefit from fp8 precision
        memory_bf16_experts = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            expert_parameter_dtype='bf16',
            training=False
        )
        
        # fp8 experts should use less memory than bf16 experts
        assert memory['parameters'] < memory_bf16_experts['parameters']
    
    def test_moe_model_active_params_precision_independence(self):
        """Test that active parameters calculation is independent of precision."""
        config = MockConfig(create_mock_model_config('deepseek_v3'))
        model = MoEModel("test-model", config)
        
        # Test with different expert precisions
        active_params_bf16 = model.compute_active_params_per_token(expert_parameter_dtype='bf16')
        active_params_fp8 = model.compute_active_params_per_token(expert_parameter_dtype='fp8')
        active_params_fp4 = model.compute_active_params_per_token(expert_parameter_dtype='fp4')
        
        # Active parameter count should be the same regardless of precision
        assert active_params_bf16 == active_params_fp8 == active_params_fp4
        
        # But memory requirements should differ
        memory_bf16 = model.compute_memory_requirements(expert_parameter_dtype='bf16', training=False)
        memory_fp8 = model.compute_memory_requirements(expert_parameter_dtype='fp8', training=False)
        
        assert memory_bf16['parameters'] > memory_fp8['parameters']


class TestMixedPrecisionValidation:
    """Test mixed precision validation functionality."""
    
    def test_precision_type_validation(self):
        """Test individual precision type validation."""
        # Valid precisions
        valid_precisions = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        for precision in valid_precisions:
            validate_precision_type(precision)  # Should not raise
        
        # None should be accepted
        validate_precision_type(None)
        
        # Invalid precisions
        invalid_precisions = ['fp64', 'int16', 'invalid', '', 123, []]
        for precision in invalid_precisions:
            with pytest.raises(UnsupportedPrecisionError):
                validate_precision_type(precision)
    
    def test_precision_compatibility_validation(self):
        """Test precision compatibility validation."""
        # Valid combinations
        valid_combinations = [
            {'weight': 'bf16', 'activation': 'bf16'},
            {'weight': 'int8', 'activation': 'bf16', 'kv_cache': 'fp8'},
            {'expert_parameter': 'fp8', 'attention_parameter': 'bf16'},
            {'optimizer': 'fp32', 'grad': 'fp16'},
        ]
        
        for combo in valid_combinations:
            validate_precision_compatibility(combo)  # Should not raise
        
        # Invalid combinations
        invalid_combinations = [
            {'activation': 'fp4'},  # fp4 not allowed for activations
            {'kv_cache': 'fp4'},    # fp4 not allowed for cache
            {'optimizer': 'fp4'},   # fp4 not allowed for optimizer
            {'optimizer': 'int8'},  # int8 not recommended for optimizer
        ]
        
        for combo in invalid_combinations:
            with pytest.raises(IncompatiblePrecisionError):
                validate_precision_compatibility(combo)
    
    def test_mixed_precision_config_validation(self):
        """Test complete mixed precision configuration validation."""
        # Valid configuration
        validate_mixed_precision_config(
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            expert_parameter_dtype='fp8',
            attention_parameter_dtype='bf16',
            grad_dtype='fp16',
            optimizer_dtype='fp32'
        )
        
        # Invalid configuration
        with pytest.raises(IncompatiblePrecisionError):
            validate_mixed_precision_config(
                activation_dtype='fp4'  # Invalid for activations
            )
    
    def test_mixed_precision_config_dataclass(self):
        """Test MixedPrecisionConfig dataclass."""
        # Valid config
        config = MixedPrecisionConfig(
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            expert_parameter_dtype='fp8'
        )
        
        assert config.weight_dtype == 'bf16'
        assert config.kv_cache_dtype == 'fp8'
        
        # Invalid config should raise during initialization
        with pytest.raises(UnsupportedPrecisionError):
            MixedPrecisionConfig(weight_dtype='invalid')


class TestMixedPrecisionIntegration:
    """Test mixed precision integration across the system."""
    
    def test_end_to_end_dense_model_workflow(self):
        """Test complete dense model workflow with mixed precision."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("meta-llama/Llama-2-7b-hf", config)
        
        # Test complete workflow
        # 1. Parameter computation
        attention_params = model.compute_attention_params()
        mlp_params = model.compute_mlp_params()
        embedding_params = model.compute_embedding_params()
        total_params = model.get_total_params()
        
        assert total_params == attention_params + mlp_params + embedding_params
        
        # 2. FLOP computation with mixed precision
        flops = model.compute_flops(sequence_length=2048, batch_size=1)
        assert 'attention' in flops
        assert 'mlp' in flops
        assert flops['attention'] > 0
        assert flops['mlp'] > 0
        
        # 3. Memory computation with mixed precision
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            training=True
        )
        
        assert memory['total'] > 0
        assert memory['parameters'] > 0
        assert memory['activations'] > 0
        
        # 4. Matrix shapes (should be independent of precision)
        shapes = model.get_matrix_shapes()
        assert 'attention' in shapes
        assert 'mlp' in shapes
        
        # 5. Metrics generation
        metrics = model.get_metrics(
            sequence_length=2048,
            batch_size=1,
            mixed_precision_config={
                'weight_dtype': 'bf16',
                'activation_dtype': 'bf16',
                'kv_cache_dtype': 'fp8'
            }
        )
        
        assert metrics.total_params == total_params
        assert metrics.flops_forward > 0
        assert metrics.memory_total > 0
    
    def test_end_to_end_moe_model_workflow(self):
        """Test complete MoE model workflow with mixed precision."""
        config = MockConfig(create_mock_model_config('deepseek_v3'))
        model = MoEModel("deepseek-ai/DeepSeek-R1", config)
        
        # Test MoE-specific workflow
        # 1. Parameter computation
        total_params = model.get_total_params()
        active_params = model.compute_active_params_per_token()
        
        assert active_params < total_params  # Should be sparse
        assert active_params > 0
        
        # 2. FLOP computation
        flops = model.compute_flops(sequence_length=2048, batch_size=1)
        flops_per_token = model.compute_flops_per_token()
        
        assert 'moe' in flops
        assert 'router' in flops
        assert flops_per_token > 0
        
        # 3. Memory computation with MoE mixed precision
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            expert_parameter_dtype='fp8',
            attention_parameter_dtype='bf16',
            training=True
        )
        
        assert 'expert_cache' in memory
        assert memory['expert_cache'] > 0
        
        # 4. Metrics with expert efficiency
        metrics = model.get_metrics()
        
        assert hasattr(metrics, 'experts_per_token')
        assert hasattr(metrics, 'active_params_per_token')
        assert metrics.experts_per_token > 0
        assert metrics.active_params_per_token == active_params
    
    def test_model_comparison_with_mixed_precision(self):
        """Test model comparison with different mixed precision configurations."""
        # Create two identical models with different precision configs
        config = MockConfig(create_mock_model_config('llama'))
        model1 = DenseModel("model-bf16", config)
        model2 = DenseModel("model-mixed", config)
        
        # Compare memory usage
        memory_bf16 = model1.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='bf16',
            training=True
        )
        
        memory_mixed = model2.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            grad_dtype='fp16',
            optimizer_dtype='fp32',
            training=True
        )
        
        # Mixed precision should use less total memory
        assert memory_mixed['total'] < memory_bf16['total']
        
        # But parameter counts should be the same
        assert model1.get_total_params() == model2.get_total_params()


class TestMixedPrecisionAPI:
    """Test mixed precision support in web API."""
    
    @pytest.fixture
    def mock_client(self):
        """Create mock API client."""
        from fastapi.testclient import TestClient
        from llm_modeling_metrics.web.app import app
        return TestClient(app)
    
    @pytest.fixture
    def mock_model_factory(self):
        """Mock model factory for API tests."""
        with patch('llm_modeling_metrics.web.app.ModelFactory') as mock_factory:
            # Mock config manager
            mock_config_manager = Mock()
            mock_config_manager.fetch_config.return_value = create_mock_model_config('llama')
            mock_factory.get_config_manager.return_value = mock_config_manager
            
            # Mock model creation
            mock_model = Mock()
            mock_model.get_metrics.return_value = Mock(
                model_name="test-model",
                total_params=7000000000,
                flops_forward=14000000000000,
                memory_total=28000000000
            )
            mock_factory.create_model.return_value = mock_model
            
            yield mock_factory
    
    def test_api_mixed_precision_parameters(self, mock_client, mock_model_factory):
        """Test API endpoints with mixed precision parameters."""
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "mixed_precision": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "fp8",
                "grad_dtype": "fp16",
                "optimizer_dtype": "fp32"
            }
        }
        
        response = mock_client.post("/api/analyze", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "results" in data
        assert "meta-llama/Llama-2-7b-hf" in data["results"]
    
    def test_api_backward_compatibility(self, mock_client, mock_model_factory):
        """Test API backward compatibility with legacy dtype parameter."""
        # Legacy format
        payload_legacy = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "dtype": "bf16"
        }
        
        response = mock_client.post("/api/analyze", json=payload_legacy)
        assert response.status_code == 200
        
        # New format with equivalent settings
        payload_new = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "mixed_precision": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "bf16"
            }
        }
        
        response = mock_client.post("/api/analyze", json=payload_new)
        assert response.status_code == 200
    
    def test_api_mixed_precision_validation(self, mock_client, mock_model_factory):
        """Test API validation of mixed precision parameters."""
        # Invalid precision type
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "mixed_precision": {
                "weight_dtype": "invalid_precision"
            }
        }
        
        response = mock_client.post("/api/analyze", json=payload)
        assert response.status_code == 422  # Validation error
    
    def test_api_moe_mixed_precision(self, mock_client, mock_model_factory):
        """Test API with MoE-specific mixed precision parameters."""
        # Mock MoE config
        mock_model_factory.get_config_manager.return_value.fetch_config.return_value = \
            create_mock_model_config('deepseek_v3')
        
        payload = {
            "model_names": ["deepseek-ai/DeepSeek-R1"],
            "sequence_length": 2048,
            "batch_size": 1,
            "mixed_precision": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "fp8",
                "expert_parameter_dtype": "fp8",
                "attention_parameter_dtype": "bf16"
            }
        }
        
        response = mock_client.post("/api/analyze", json=payload)
        assert response.status_code == 200


class TestMixedPrecisionPerformance:
    """Test performance characteristics of mixed precision calculations."""
    
    def test_calculation_performance(self):
        """Test that mixed precision calculations are efficient."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Measure time for legacy calculation
        start_time = time.time()
        for _ in range(10):
            memory_legacy = model.compute_memory_requirements(
                sequence_length=2048,
                batch_size=1,
                dtype='bf16',
                training=True
            )
        legacy_time = time.time() - start_time
        
        # Measure time for mixed precision calculation
        start_time = time.time()
        for _ in range(10):
            memory_mixed = model.compute_memory_requirements(
                sequence_length=2048,
                batch_size=1,
                weight_dtype='bf16',
                activation_dtype='bf16',
                kv_cache_dtype='fp8',
                grad_dtype='fp16',
                optimizer_dtype='fp32',
                training=True
            )
        mixed_time = time.time() - start_time
        
        # Mixed precision should not be significantly slower
        # Allow up to 50% overhead for additional calculations
        assert mixed_time < legacy_time * 1.5, \
            f"Mixed precision calculation too slow: {mixed_time:.3f}s vs {legacy_time:.3f}s"
    
    def test_memory_calculation_accuracy(self):
        """Test accuracy of mixed precision memory calculations."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Calculate memory with known precisions
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',      # 2 bytes
            activation_dtype='bf16',  # 2 bytes
            kv_cache_dtype='fp8',     # 1 byte
            grad_dtype='fp16',        # 2 bytes
            optimizer_dtype='fp32',   # 4 bytes
            training=True,
            include_kv_cache=True
        )
        
        # Verify that memory components reflect precision differences
        # KV cache should be smaller due to fp8
        memory_bf16_cache = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='bf16',  # Changed to bf16
            grad_dtype='fp16',
            optimizer_dtype='fp32',
            training=True,
            include_kv_cache=True
        )
        
        # fp8 cache should use less memory than bf16 cache
        assert memory['kv_cache'] < memory_bf16_cache['kv_cache']
        
        # Optimizer states should be largest due to fp32
        assert memory['optimizer_states'] > memory['gradients']  # fp32 > fp16
    
    def test_large_model_mixed_precision_scaling(self):
        """Test mixed precision with large model configurations."""
        # Create a large model config
        large_config = MockConfig({
            'hidden_size': 16384,
            'num_hidden_layers': 128,
            'num_attention_heads': 128,
            'intermediate_size': 65536,
            'vocab_size': 100000,
            'model_type': 'llama'
        })
        
        model = DenseModel("large-model", large_config)
        
        # Test that mixed precision provides significant memory savings
        memory_fp32 = model.compute_memory_requirements(
            sequence_length=4096,
            batch_size=1,
            dtype='fp32',
            training=True
        )
        
        memory_mixed = model.compute_memory_requirements(
            sequence_length=4096,
            batch_size=1,
            weight_dtype='bf16',
            activation_dtype='bf16',
            kv_cache_dtype='fp8',
            grad_dtype='fp16',
            optimizer_dtype='fp32',
            training=True
        )
        
        # Mixed precision should provide substantial savings
        savings_ratio = (memory_fp32['total'] - memory_mixed['total']) / memory_fp32['total']
        assert savings_ratio > 0.2, f"Expected >20% memory savings, got {savings_ratio:.1%}"
    
    def test_moe_mixed_precision_efficiency(self):
        """Test MoE model efficiency with mixed precision."""
        config = MockConfig(create_mock_model_config('deepseek_v3'))
        model = MoEModel("test-model", config)
        
        # Test expert parameter precision efficiency
        memory_bf16_experts = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            expert_parameter_dtype='bf16',
            training=False
        )
        
        memory_fp8_experts = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            expert_parameter_dtype='fp8',
            training=False
        )
        
        memory_fp4_experts = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            expert_parameter_dtype='fp4',
            training=False
        )
        
        # Should see progressive memory reduction
        assert memory_fp8_experts['parameters'] < memory_bf16_experts['parameters']
        assert memory_fp4_experts['parameters'] < memory_fp8_experts['parameters']
        
        # But active parameters should remain the same
        active_bf16 = model.compute_active_params_per_token(expert_parameter_dtype='bf16')
        active_fp8 = model.compute_active_params_per_token(expert_parameter_dtype='fp8')
        active_fp4 = model.compute_active_params_per_token(expert_parameter_dtype='fp4')
        
        assert active_bf16 == active_fp8 == active_fp4


class TestMixedPrecisionEdgeCases:
    """Test edge cases and error conditions for mixed precision."""
    
    def test_extreme_precision_combinations(self):
        """Test extreme precision combinations."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Test extreme mixed precision (fp32 weights, fp4 cache)
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype='fp32',
            activation_dtype='bf16',
            kv_cache_dtype='fp4',
            training=False
        )
        
        assert memory['total'] > 0
        assert memory['parameters'] > 0
        assert memory['kv_cache'] > 0
    
    def test_none_precision_handling(self):
        """Test handling of None precision values."""
        # Should use defaults when None is passed
        op = MatMulOperator(M=10, N=20, K=30, 
                          input_precision=None,
                          weight_precision=None,
                          output_precision=None)
        
        # Should fall back to base precision
        assert op.input_precision == 'bf16'  # Default
        assert op.weight_precision == 'bf16'
        assert op.output_precision == 'bf16'
    
    def test_precision_parameter_precedence(self):
        """Test parameter precedence in mixed precision."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        # Test that specific parameters override general ones
        memory = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='fp32',  # General parameter
            weight_dtype='bf16',  # Should override dtype for weights
            kv_cache_dtype='fp8',  # Should override dtype for cache
            training=False
        )
        
        # Should use mixed precision, not fp32 everywhere
        memory_all_fp32 = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            dtype='fp32',
            training=False
        )
        
        assert memory['total'] < memory_all_fp32['total']
    
    def test_invalid_precision_combinations_recovery(self):
        """Test recovery from invalid precision combinations."""
        # Test that system can handle and report invalid combinations gracefully
        with pytest.raises(IncompatiblePrecisionError) as exc_info:
            validate_mixed_precision_config(
                activation_dtype='fp4',  # Invalid
                optimizer_dtype='int8'   # Invalid
            )
        
        # Error message should be informative
        error_msg = str(exc_info.value)
        assert 'fp4' in error_msg or 'activation' in error_msg
    
    def test_precision_rounding_edge_cases(self):
        """Test precision calculations with edge cases that might cause rounding errors."""
        # Test with fp4 (0.5 bytes per element)
        op = MatMulOperator(M=1, N=1, K=1, weight_precision='fp4')
        
        # Should handle fractional bytes correctly
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=1)
        assert memory >= 0
        
        # Test with odd numbers that might cause rounding issues
        op = MatMulOperator(M=3, N=5, K=7, weight_precision='fp4')
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=1)
        assert memory >= 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])