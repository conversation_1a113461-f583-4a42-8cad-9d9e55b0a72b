"""
Integration test for roofline and timing API endpoints.
This test verifies that the endpoints are properly registered and can handle basic requests.
"""

import pytest
import json
from unittest.mock import Mock, patch

def test_roofline_timing_endpoints_registration():
    """Test that roofline and timing endpoints are properly registered."""
    from llm_modeling_metrics.web.app import app
    from fastapi.openapi.utils import get_openapi
    
    # Get the OpenAPI schema
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Check for roofline endpoints
    roofline_endpoints = [path for path in openapi_schema['paths'].keys() if 'roofline' in path]
    timing_endpoints = [path for path in openapi_schema['paths'].keys() if 'timing' in path]
    
    # Verify all expected endpoints are present
    expected_roofline = ['/api/roofline/generate', '/api/roofline/plot-operators', '/api/roofline/compare']
    expected_timing = ['/api/timing/analyze', '/api/timing/bottlenecks', '/api/timing/compare-hardware']
    
    assert set(roofline_endpoints) == set(expected_roofline), f"Missing roofline endpoints: {set(expected_roofline) - set(roofline_endpoints)}"
    assert set(timing_endpoints) == set(expected_timing), f"Missing timing endpoints: {set(expected_timing) - set(timing_endpoints)}"
    
    # Verify endpoints have correct HTTP methods
    for endpoint in expected_roofline + expected_timing:
        assert 'post' in openapi_schema['paths'][endpoint], f"Endpoint {endpoint} should support POST method"


def test_roofline_timing_models_import():
    """Test that all roofline and timing models can be imported."""
    from llm_modeling_metrics.web.models import (
        # Roofline models
        KneePointModel, OperatorPointModel, RooflineDataModel,
        RooflinePlotDataModel, ComparisonPlotDataModel,
        RooflineGenerateRequest, RooflinePlotOperatorsRequest, RooflineCompareRequest,
        # Timing models
        OperatorTimingModel, BottleneckAnalysisModel, TimingComparisonModel,
        TimingAnalyzeRequest, TimingCompareHardwareRequest, OperatorConfigModel
    )
    
    # Verify models can be instantiated with basic data
    knee_point = KneePointModel(
        operational_intensity=100.0,
        performance_tflops=200.0,
        precision="fp16",
        hardware_id="test_gpu"
    )
    assert knee_point.operational_intensity == 100.0
    
    operator_config = OperatorConfigModel(
        name="test_op",
        type="attention",
        parameters={"seq_len": 2048}
    )
    assert operator_config.name == "test_op"


def test_service_imports():
    """Test that roofline and timing services can be imported."""
    from llm_modeling_metrics.hardware.roofline_service import RooflineService
    from llm_modeling_metrics.hardware.timing_service import OperatorTimingService
    
    # Verify services can be instantiated
    roofline_service = RooflineService()
    timing_service = OperatorTimingService()
    
    assert roofline_service is not None
    assert timing_service is not None


def test_endpoint_request_validation():
    """Test that endpoint request models validate correctly."""
    from llm_modeling_metrics.web.models import (
        RooflineGenerateRequest, TimingAnalyzeRequest, OperatorConfigModel
    )
    
    # Test valid roofline request
    roofline_request = RooflineGenerateRequest(
        hardware_ids=["gpu1", "gpu2"],
        precisions=["fp16", "bf16"]
    )
    assert len(roofline_request.hardware_ids) == 2
    
    # Test valid timing request
    timing_request = TimingAnalyzeRequest(
        operators=[
            OperatorConfigModel(
                name="attention",
                type="attention",
                parameters={"seq_len": 2048, "batch_size": 1}
            )
        ],
        hardware_id="test_gpu"
    )
    assert len(timing_request.operators) == 1
    
    # Test validation errors
    with pytest.raises(ValueError, match="Hardware IDs list cannot be empty"):
        RooflineGenerateRequest(hardware_ids=[])
    
    with pytest.raises(ValueError, match="Operators list cannot be empty"):
        TimingAnalyzeRequest(operators=[], hardware_id="test_gpu")


if __name__ == "__main__":
    pytest.main([__file__])