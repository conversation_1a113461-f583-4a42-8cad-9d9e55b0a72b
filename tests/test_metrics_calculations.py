"""
Unit tests for metrics calculation functionality.
"""

import pytest
from unittest.mock import Mock, patch

from llm_modeling_metrics.metrics.flops_calculator import FLOPsCalculator
from llm_modeling_metrics.metrics.memory_calculator import MemoryCalculator
from llm_modeling_metrics.metrics.shape_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from llm_modeling_metrics.core.base_model import ParallelConfig
from tests.conftest import (
    create_mock_model_config, assert_flops_breakdown_valid, 
    assert_memory_breakdown_valid, assert_shapes_valid
)


class TestFLOPsCalculator:
    """Test FLOPsCalculator functionality."""
    
    def test_attention_flops_basic(self):
        """Test basic attention FLOP computation."""
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=32,
            sequence_length=2048,
            batch_size=1
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Check expected components
        expected_components = [
            'q_projection', 'k_projection', 'v_projection',
            'attention_scores', 'attention_output', 'output_projection'
        ]
        for component in expected_components:
            assert component in flops
            assert flops[component] > 0
        
        # Check that total equals sum of components
        component_sum = sum(flops[k] for k in expected_components)
        assert flops['total'] == component_sum
    
    def test_attention_flops_gqa(self):
        """Test attention FLOPs with Grouped Query Attention."""
        # Test with fewer KV heads (GQA)
        flops_gqa = FLOPsCalculator.compute_attention_flops(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=8,  # 4x fewer KV heads
            sequence_length=2048,
            batch_size=1
        )
        
        # Test with standard attention
        flops_standard = FLOPsCalculator.compute_attention_flops(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=32,
            sequence_length=2048,
            batch_size=1
        )
        
        assert_flops_breakdown_valid(flops_gqa)
        assert_flops_breakdown_valid(flops_standard)
        
        # GQA should have fewer FLOPs for K and V projections
        assert flops_gqa['k_projection'] < flops_standard['k_projection']
        assert flops_gqa['v_projection'] < flops_standard['v_projection']
        assert flops_gqa['total'] < flops_standard['total']
    
    def test_attention_flops_scaling(self):
        """Test attention FLOP scaling with different parameters."""
        base_params = {
            'hidden_size': 4096,
            'num_heads': 32,
            'num_kv_heads': 32,
            'sequence_length': 2048,
            'batch_size': 1
        }
        
        # Test sequence length scaling
        seq_lengths = [512, 1024, 2048, 4096]
        previous_flops = 0
        
        for seq_len in seq_lengths:
            flops = FLOPsCalculator.compute_attention_flops(**{**base_params, 'sequence_length': seq_len})
            assert flops['total'] > previous_flops
            previous_flops = flops['total']
        
        # Test batch size scaling
        batch_sizes = [1, 2, 4, 8]
        previous_flops = 0
        
        for batch_size in batch_sizes:
            flops = FLOPsCalculator.compute_attention_flops(**{**base_params, 'batch_size': batch_size})
            assert flops['total'] > previous_flops
            previous_flops = flops['total']
    
    def test_mlp_flops_gated_activation(self):
        """Test MLP FLOPs with gated activation."""
        flops = FLOPsCalculator.compute_mlp_flops(
            hidden_size=4096,
            intermediate_size=11008,
            sequence_length=2048,
            batch_size=1,
            use_gated_activation=True
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Check gated activation components
        expected_components = ['gate_projection', 'up_projection', 'gated_activation', 'down_projection']
        for component in expected_components:
            assert component in flops
            assert flops[component] > 0
    
    def test_mlp_flops_standard_activation(self):
        """Test MLP FLOPs with standard activation."""
        flops = FLOPsCalculator.compute_mlp_flops(
            hidden_size=4096,
            intermediate_size=11008,
            sequence_length=2048,
            batch_size=1,
            use_gated_activation=False
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Check standard activation components
        expected_components = ['up_projection', 'activation', 'down_projection']
        for component in expected_components:
            assert component in flops
            assert flops[component] > 0
        
        # Should not have gated components
        assert 'gate_projection' not in flops
        assert 'gated_activation' not in flops
    
    def test_mlp_flops_comparison(self):
        """Test comparison between gated and standard MLP FLOPs."""
        base_params = {
            'hidden_size': 4096,
            'intermediate_size': 11008,
            'sequence_length': 2048,
            'batch_size': 1
        }
        
        flops_gated = FLOPsCalculator.compute_mlp_flops(**base_params, use_gated_activation=True)
        flops_standard = FLOPsCalculator.compute_mlp_flops(**base_params, use_gated_activation=False)
        
        # Gated activation should have more FLOPs due to additional gate projection
        assert flops_gated['total'] > flops_standard['total']
    
    def test_embedding_flops(self):
        """Test embedding FLOP computation."""
        flops = FLOPsCalculator.compute_embedding_flops(
            vocab_size=32000,
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            tie_embeddings=False
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Input embedding should be 0 (lookup operations)
        assert flops['input_embedding'] == 0
        
        # LM head should have FLOPs
        assert flops['lm_head'] > 0
        assert flops['total'] == flops['lm_head']
    
    def test_layer_norm_flops(self):
        """Test layer normalization FLOP computation."""
        flops = FLOPsCalculator.compute_layer_norm_flops(
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            num_layer_norms=2
        )
        
        assert_flops_breakdown_valid(flops)
        assert flops['layer_norm'] > 0
        assert flops['total'] == flops['layer_norm']
        
        # Test scaling with number of layer norms
        flops_single = FLOPsCalculator.compute_layer_norm_flops(
            hidden_size=4096, sequence_length=2048, batch_size=1, num_layer_norms=1
        )
        
        assert flops['total'] == 2 * flops_single['total']
    
    def test_moe_flops(self):
        """Test MoE FLOP computation."""
        flops = FLOPsCalculator.compute_moe_flops(
            hidden_size=4096,
            intermediate_size=11008,
            num_experts=64,
            experts_per_token=8,
            sequence_length=2048,
            batch_size=1,
            use_gated_activation=True,
            shared_experts=2
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Check MoE components
        expected_components = ['router', 'routed_experts', 'shared_experts']
        for component in expected_components:
            assert component in flops
            assert flops[component] > 0
        
        # Router should have FLOPs for all experts
        assert flops['router'] > 0
        
        # Routed experts should be less than full expert computation
        # (since only experts_per_token out of num_experts are used)
        assert flops['routed_experts'] > 0
        
        # Shared experts should be present
        assert flops['shared_experts'] > 0
    
    def test_moe_flops_without_shared_experts(self):
        """Test MoE FLOPs without shared experts."""
        flops = FLOPsCalculator.compute_moe_flops(
            hidden_size=4096,
            intermediate_size=11008,
            num_experts=64,
            experts_per_token=8,
            sequence_length=2048,
            batch_size=1,
            use_gated_activation=True,
            shared_experts=0
        )
        
        assert_flops_breakdown_valid(flops)
        assert 'shared_experts' not in flops or flops['shared_experts'] == 0
    
    def test_model_flops_dense(self):
        """Test complete dense model FLOP computation."""
        model_config = create_mock_model_config('llama')
        
        flops = FLOPsCalculator.compute_model_flops(
            model_config, sequence_length=2048, batch_size=1, include_embeddings=True
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Check dense model components
        expected_components = [
            'attention_total', 'mlp_total', 'layer_norm_total', 'embeddings'
        ]
        for component in expected_components:
            assert component in flops
            assert flops[component] > 0
        
        # Check per-layer components
        assert 'attention_per_layer' in flops
        assert 'mlp_per_layer' in flops
        assert flops['attention_total'] == flops['attention_per_layer'] * model_config['num_hidden_layers']
        assert flops['mlp_total'] == flops['mlp_per_layer'] * model_config['num_hidden_layers']
    
    def test_model_flops_moe(self):
        """Test complete MoE model FLOP computation."""
        model_config = create_mock_model_config('deepseek_v3')
        
        flops = FLOPsCalculator.compute_model_flops(
            model_config, sequence_length=2048, batch_size=1, include_embeddings=True
        )
        
        assert_flops_breakdown_valid(flops)
        
        # Check MoE model components
        expected_components = [
            'attention_total', 'moe_total', 'layer_norm_total', 'embeddings'
        ]
        for component in expected_components:
            assert component in flops
            assert flops[component] > 0
        
        # Should not have mlp_total for MoE models
        assert 'mlp_total' not in flops
        
        # Check per-layer components
        assert 'attention_per_layer' in flops
        assert 'moe_per_layer' in flops
    
    def test_adjust_flops_for_parallel(self):
        """Test FLOP adjustment for parallel execution."""
        base_flops = {
            'attention_total': 1000000000,
            'mlp_total': 2000000000,
            'embeddings': 500000000,
            'total': 3500000000
        }
        
        parallel_config = ParallelConfig(
            tensor_parallel_size=4,
            pipeline_parallel_size=2,
            data_parallel_size=2
        )
        
        adjusted_flops = FLOPsCalculator.adjust_flops_for_parallel(base_flops, parallel_config)
        
        # Check that original values are preserved
        for key in base_flops:
            assert adjusted_flops[key] == base_flops[key]
        
        # Check per-device FLOPs
        assert 'attention_total_per_device' in adjusted_flops
        assert 'mlp_total_per_device' in adjusted_flops
        
        # Check communication overhead
        if parallel_config.tensor_parallel_size > 1:
            assert 'communication_overhead' in adjusted_flops
            assert 'total_with_comm' in adjusted_flops
            assert adjusted_flops['total_with_comm'] > adjusted_flops['total']
    
    def test_flops_breakdown_summary(self):
        """Test FLOP breakdown summary generation."""
        flops = {
            'attention_total': 1000000000,
            'mlp_total': 2000000000,
            'layer_norm_total': 100000000,
            'embeddings': 500000000,
            'total': 3600000000
        }
        
        summary = FLOPsCalculator.get_flops_breakdown_summary(flops)
        
        assert 'total' in summary
        assert 'total_human' in summary
        assert 'breakdown' in summary
        
        assert summary['total'] == 3600000000
        assert 'G' in summary['total_human'] or 'T' in summary['total_human']
        
        # Check breakdown percentages
        breakdown = summary['breakdown']
        for component in ['attention_total', 'mlp_total', 'layer_norm_total', 'embeddings']:
            if component in breakdown:
                assert 'flops' in breakdown[component]
                assert 'flops_human' in breakdown[component]
                assert 'percentage' in breakdown[component]
                assert 0 <= breakdown[component]['percentage'] <= 100
    
    def test_format_flops(self):
        """Test FLOP formatting utility."""
        test_cases = [
            (1000, "1000"),
            (1500, "1.50K"),
            (1000000, "1.00M"),
            (1500000000, "1.50G"),
            (2500000000000, "2.50T"),
        ]
        
        for flops, expected in test_cases:
            formatted = FLOPsCalculator._format_flops(flops)
            assert formatted == expected
    
    def test_edge_cases(self):
        """Test edge cases in FLOP calculations."""
        # Test with zero sequence length
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=4096, num_heads=32, num_kv_heads=32,
            sequence_length=0, batch_size=1
        )
        assert flops['total'] == 0
        
        # Test with zero batch size
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=4096, num_heads=32, num_kv_heads=32,
            sequence_length=2048, batch_size=0
        )
        assert flops['total'] == 0
        
        # Test with minimal dimensions
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=1, num_heads=1, num_kv_heads=1,
            sequence_length=1, batch_size=1
        )
        assert flops['total'] > 0


class TestMetricsCalculationAccuracy:
    """Test accuracy of metrics calculations against known values."""
    
    def test_attention_flops_accuracy(self):
        """Test attention FLOP calculation accuracy against manual computation."""
        # Known configuration: Llama-7B-like model
        hidden_size = 4096
        num_heads = 32
        num_kv_heads = 32
        sequence_length = 2048
        batch_size = 1
        
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=hidden_size,
            num_heads=num_heads,
            num_kv_heads=num_kv_heads,
            sequence_length=sequence_length,
            batch_size=batch_size
        )
        
        # Manual calculation for verification
        # Q, K, V projections: 3 * (batch * seq * hidden * hidden) * 2
        qkv_flops = 3 * batch_size * sequence_length * hidden_size * hidden_size * 2
        
        # Attention scores: batch * heads * seq * seq * head_dim * 2
        head_dim = hidden_size // num_heads
        attn_scores_flops = batch_size * num_heads * sequence_length * sequence_length * head_dim * 2
        
        # Attention output: batch * heads * seq * seq * head_dim * 2
        attn_output_flops = batch_size * num_heads * sequence_length * sequence_length * head_dim * 2
        
        # Output projection: batch * seq * hidden * hidden * 2
        output_proj_flops = batch_size * sequence_length * hidden_size * hidden_size * 2
        
        expected_total = qkv_flops + attn_scores_flops + attn_output_flops + output_proj_flops
        
        # Allow for small differences due to implementation details
        assert abs(flops['total'] - expected_total) / expected_total < 0.1
    
    def test_mlp_flops_accuracy(self):
        """Test MLP FLOP calculation accuracy."""
        hidden_size = 4096
        intermediate_size = 11008
        sequence_length = 2048
        batch_size = 1
        
        # Test gated activation (SwiGLU)
        flops_gated = FLOPsCalculator.compute_mlp_flops(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            sequence_length=sequence_length,
            batch_size=batch_size,
            use_gated_activation=True
        )
        
        # Manual calculation for gated MLP
        # Gate projection: batch * seq * hidden * intermediate * 2
        gate_flops = batch_size * sequence_length * hidden_size * intermediate_size * 2
        # Up projection: batch * seq * hidden * intermediate * 2
        up_flops = batch_size * sequence_length * hidden_size * intermediate_size * 2
        # Gated activation: batch * seq * intermediate (element-wise operations)
        activation_flops = batch_size * sequence_length * intermediate_size
        # Down projection: batch * seq * intermediate * hidden * 2
        down_flops = batch_size * sequence_length * intermediate_size * hidden_size * 2
        
        expected_total = gate_flops + up_flops + activation_flops + down_flops
        
        assert abs(flops_gated['total'] - expected_total) / expected_total < 0.1
    
    def test_memory_calculation_accuracy(self):
        """Test memory calculation accuracy."""
        total_params = 7000000000  # 7B parameters
        hidden_size = 4096
        sequence_length = 2048
        batch_size = 1
        num_layers = 32
        num_kv_heads = 32
        
        memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=total_params,
            hidden_size=hidden_size,
            sequence_length=sequence_length,
            batch_size=batch_size,
            num_layers=num_layers,
            num_kv_heads=num_kv_heads,
            param_dtype_bytes=4,  # FP32
            activation_dtype_bytes=4
        )
        
        # Manual calculation for parameters
        expected_param_memory = total_params * 4  # FP32
        assert memory['parameters'] == expected_param_memory
        
        # KV cache memory calculation
        head_dim = hidden_size // num_kv_heads
        expected_kv_cache = (
            2 *  # K and V
            batch_size * 
            sequence_length * 
            num_layers * 
            num_kv_heads * 
            head_dim * 
            4  # FP32
        )
        assert abs(memory['kv_cache'] - expected_kv_cache) / expected_kv_cache < 0.1
    
    def test_shape_calculation_accuracy(self):
        """Test matrix shape calculation accuracy."""
        hidden_size = 4096
        num_heads = 32
        num_kv_heads = 8  # GQA
        intermediate_size = 11008
        
        # Test attention shapes with GQA
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=hidden_size,
            num_heads=num_heads,
            num_kv_heads=num_kv_heads,
            head_dim=hidden_size // num_heads
        )
        
        # Verify shapes
        assert shapes['q_proj'] == (hidden_size, num_heads * (hidden_size // num_heads))
        assert shapes['k_proj'] == (hidden_size, num_kv_heads * (hidden_size // num_heads))
        assert shapes['v_proj'] == (hidden_size, num_kv_heads * (hidden_size // num_heads))
        assert shapes['o_proj'] == (hidden_size, hidden_size)
        
        # Test MLP shapes
        mlp_shapes = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            use_gated_activation=True
        )
        
        assert mlp_shapes['gate_proj'] == (hidden_size, intermediate_size)
        assert mlp_shapes['up_proj'] == (hidden_size, intermediate_size)
        assert mlp_shapes['down_proj'] == (intermediate_size, hidden_size)


class TestMemoryCalculator:
    """Test MemoryCalculator functionality."""
    
    def test_parameter_memory_basic(self):
        """Test basic parameter memory calculation."""
        memory = MemoryCalculator.compute_parameter_memory(
            total_params=7000000000,
            dtype_bytes=4  # FP32
        )
        
        assert_memory_breakdown_valid(memory)
        assert memory['parameters'] == 7000000000 * 4
        assert memory['total'] == memory['parameters']
    
    def test_parameter_memory_different_dtypes(self):
        """Test parameter memory with different data types."""
        total_params = 7000000000
        
        dtypes = [
            (2, 'FP16'),  # Half precision
            (4, 'FP32'),  # Single precision
            (1, 'INT8'),  # Quantized
        ]
        
        previous_memory = 0
        for dtype_bytes, dtype_name in dtypes:
            memory = MemoryCalculator.compute_parameter_memory(total_params, dtype_bytes)
            
            expected_memory = total_params * dtype_bytes
            assert memory['parameters'] == expected_memory
            
            if previous_memory > 0:
                assert memory['parameters'] >= previous_memory  # Larger dtypes use more memory
            previous_memory = memory['parameters']
    
    def test_activation_memory_basic(self):
        """Test basic activation memory calculation."""
        memory = MemoryCalculator.compute_activation_memory(
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            num_layers=32,
            dtype_bytes=4
        )
        
        assert_memory_breakdown_valid(memory)
        assert memory['activations'] > 0
        assert memory['total'] == memory['activations']
    
    def test_activation_memory_scaling(self):
        """Test activation memory scaling with different parameters."""
        base_params = {
            'hidden_size': 4096,
            'sequence_length': 2048,
            'batch_size': 1,
            'num_layers': 32,
            'dtype_bytes': 4
        }
        
        # Test batch size scaling
        batch_sizes = [1, 2, 4, 8]
        previous_memory = 0
        
        for batch_size in batch_sizes:
            memory = MemoryCalculator.compute_activation_memory(**{**base_params, 'batch_size': batch_size})
            assert memory['activations'] > previous_memory
            previous_memory = memory['activations']
        
        # Test sequence length scaling
        seq_lengths = [512, 1024, 2048, 4096]
        previous_memory = 0
        
        for seq_len in seq_lengths:
            memory = MemoryCalculator.compute_activation_memory(**{**base_params, 'sequence_length': seq_len})
            assert memory['activations'] > previous_memory
            previous_memory = memory['activations']
    
    def test_kv_cache_memory(self):
        """Test KV cache memory calculation."""
        memory = MemoryCalculator.compute_kv_cache_memory(
            hidden_size=4096,
            num_layers=32,
            num_kv_heads=32,
            sequence_length=2048,
            batch_size=1,
            dtype_bytes=4
        )
        
        assert_memory_breakdown_valid(memory)
        assert memory['kv_cache'] > 0
        assert memory['total'] == memory['kv_cache']
    
    def test_kv_cache_memory_gqa(self):
        """Test KV cache memory with Grouped Query Attention."""
        # Standard attention
        memory_standard = MemoryCalculator.compute_kv_cache_memory(
            hidden_size=4096, num_layers=32, num_kv_heads=32,
            sequence_length=2048, batch_size=1, dtype_bytes=4
        )
        
        # GQA with fewer KV heads
        memory_gqa = MemoryCalculator.compute_kv_cache_memory(
            hidden_size=4096, num_layers=32, num_kv_heads=8,
            sequence_length=2048, batch_size=1, dtype_bytes=4
        )
        
        # GQA should use less memory
        assert memory_gqa['kv_cache'] < memory_standard['kv_cache']
    
    def test_total_memory_requirements(self):
        """Test total memory requirements calculation."""
        memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=7000000000,
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            num_layers=32,
            num_kv_heads=32,
            param_dtype_bytes=4,
            activation_dtype_bytes=4
        )
        
        assert_memory_breakdown_valid(memory)
        
        # Check all components are present
        expected_components = ['parameters', 'activations', 'kv_cache', 'optimizer_states', 'gradients']
        for component in expected_components:
            assert component in memory
            assert memory[component] >= 0
        
        # Total should be sum of all components
        component_sum = sum(memory[k] for k in expected_components)
        assert memory['total'] == component_sum
    
    def test_memory_with_optimizer_states(self):
        """Test memory calculation with optimizer states."""
        base_memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=7000000000,
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            num_layers=32,
            num_kv_heads=32,
            include_optimizer_states=False
        )
        
        optimizer_memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=7000000000,
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            num_layers=32,
            num_kv_heads=32,
            include_optimizer_states=True,
            optimizer_state_multiplier=2.0  # Adam optimizer
        )
        
        # With optimizer states should use more memory
        assert optimizer_memory['total'] > base_memory['total']
        assert optimizer_memory['optimizer_states'] > 0
        assert base_memory['optimizer_states'] == 0
    
    def test_memory_breakdown_summary(self):
        """Test memory breakdown summary generation."""
        memory = {
            'parameters': 28000000000,  # 28GB
            'activations': 2000000000,  # 2GB
            'kv_cache': 1000000000,     # 1GB
            'optimizer_states': 56000000000,  # 56GB
            'gradients': 28000000000,   # 28GB
            'total': 115000000000       # 115GB
        }
        
        summary = MemoryCalculator.get_memory_breakdown_summary(memory)
        
        assert 'total_gb' in summary
        assert 'breakdown' in summary
        
        assert summary['total_gb'] == 115000000000 / (1024**3)
        
        # Check breakdown percentages
        breakdown = summary['breakdown']
        for component in ['parameters', 'activations', 'kv_cache', 'optimizer_states', 'gradients']:
            if component in breakdown:
                assert 'bytes' in breakdown[component]
                assert 'gb' in breakdown[component]
                assert 'percentage' in breakdown[component]
                assert 0 <= breakdown[component]['percentage'] <= 100
    
    def test_memory_for_parallel_config(self):
        """Test memory calculation for parallel configurations."""
        base_memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=7000000000,
            hidden_size=4096,
            sequence_length=2048,
            batch_size=1,
            num_layers=32,
            num_kv_heads=32
        )
        
        parallel_config = ParallelConfig(tensor_parallel_size=4, pipeline_parallel_size=2)
        
        parallel_memory = MemoryCalculator.adjust_memory_for_parallel(
            base_memory, parallel_config
        )
        
        # Should have per-device memory calculations
        assert 'memory_per_device' in parallel_memory
        assert 'sharding_strategy' in parallel_memory
        
        # Per-device memory should be less than total
        total_devices = (parallel_config.tensor_parallel_size * 
                        parallel_config.pipeline_parallel_size * 
                        parallel_config.data_parallel_size)
        
        expected_per_device = base_memory['total'] / total_devices
        assert parallel_memory['memory_per_device'] <= expected_per_device * 1.1  # Allow overhead
    
    def test_format_memory(self):
        """Test memory formatting utility."""
        test_cases = [
            (1024, "1.00KB"),
            (1024**2, "1.00MB"),
            (1024**3, "1.00GB"),
            (1024**4, "1.00TB"),
            (1536 * 1024**3, "1.50TB"),
        ]
        
        for memory_bytes, expected in test_cases:
            formatted = MemoryCalculator._format_memory(memory_bytes)
            assert formatted == expected
    
    def test_edge_cases(self):
        """Test edge cases in memory calculations."""
        # Test with zero parameters
        memory = MemoryCalculator.compute_parameter_memory(0, 4)
        assert memory['parameters'] == 0
        assert memory['total'] == 0
        
        # Test with zero sequence length
        memory = MemoryCalculator.compute_activation_memory(
            hidden_size=4096, sequence_length=0, batch_size=1,
            num_layers=32, dtype_bytes=4
        )
        assert memory['activations'] == 0
        
        # Test with minimal values
        memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=1, hidden_size=1, sequence_length=1,
            batch_size=1, num_layers=1, num_kv_heads=1
        )
        assert memory['total'] > 0


class TestMetricsCalculationEdgeCases:
    """Test edge cases and error conditions in metrics calculations."""
    
    def test_zero_dimension_handling(self):
        """Test handling of zero dimensions in calculations."""
        # Test FLOPs with zero dimensions
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=0, num_heads=1, num_kv_heads=1,
            sequence_length=2048, batch_size=1
        )
        assert flops['total'] == 0
        
        # Test memory with zero dimensions
        memory = MemoryCalculator.compute_activation_memory(
            hidden_size=0, sequence_length=2048, batch_size=1,
            num_layers=32, dtype_bytes=4
        )
        assert memory['activations'] == 0
        
        # Test shapes with zero dimensions
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=0, num_heads=1, num_kv_heads=1, head_dim=0
        )
        for shape_name, shape in shapes.items():
            assert 0 in shape  # At least one dimension should be 0
    
    def test_invalid_parameter_combinations(self):
        """Test handling of invalid parameter combinations."""
        # Test with more KV heads than attention heads (invalid)
        with pytest.raises(ValueError, match="num_kv_heads cannot be greater than num_heads"):
            FLOPsCalculator.compute_attention_flops(
                hidden_size=4096, num_heads=16, num_kv_heads=32,
                sequence_length=2048, batch_size=1
            )
        
        # Test with non-divisible head dimensions
        with pytest.raises(ValueError, match="hidden_size must be divisible by num_heads"):
            ShapeAnalyzer.compute_attention_shapes(
                hidden_size=4097, num_heads=32, num_kv_heads=32, head_dim=None
            )
    
    def test_extreme_values(self):
        """Test behavior with extreme values."""
        # Test with very large sequence length
        flops = FLOPsCalculator.compute_attention_flops(
            hidden_size=4096, num_heads=32, num_kv_heads=32,
            sequence_length=1000000, batch_size=1
        )
        assert flops['total'] > 0
        assert flops['attention_scores'] > flops['q_projection']  # Quadratic scaling
        
        # Test with very large batch size
        memory = MemoryCalculator.compute_activation_memory(
            hidden_size=4096, sequence_length=2048, batch_size=1000,
            num_layers=32, dtype_bytes=4
        )
        assert memory['activations'] > 0
        
        # Memory should scale linearly with batch size
        memory_small = MemoryCalculator.compute_activation_memory(
            hidden_size=4096, sequence_length=2048, batch_size=1,
            num_layers=32, dtype_bytes=4
        )
        assert abs(memory['activations'] / memory_small['activations'] - 1000) < 1
    
    def test_numerical_precision(self):
        """Test numerical precision in calculations."""
        # Test with very small values
        flops_small = FLOPsCalculator.compute_mlp_flops(
            hidden_size=1, intermediate_size=1,
            sequence_length=1, batch_size=1,
            use_gated_activation=False
        )
        assert flops_small['total'] > 0
        
        # Test with very large values
        flops_large = FLOPsCalculator.compute_mlp_flops(
            hidden_size=16384, intermediate_size=65536,
            sequence_length=8192, batch_size=128,
            use_gated_activation=True
        )
        assert flops_large['total'] > flops_small['total']
        
        # Check that calculations don't overflow
        assert flops_large['total'] < 2**63  # Within int64 range
    
    def test_moe_edge_cases(self):
        """Test MoE-specific edge cases."""
        # Test with zero experts
        with pytest.raises(ValueError, match="num_experts must be > 0"):
            FLOPsCalculator.compute_moe_flops(
                hidden_size=4096, intermediate_size=11008,
                num_experts=0, experts_per_token=2,
                sequence_length=2048, batch_size=1
            )
        
        # Test with more experts per token than total experts
        with pytest.raises(ValueError, match="experts_per_token cannot exceed num_experts"):
            FLOPsCalculator.compute_moe_flops(
                hidden_size=4096, intermediate_size=11008,
                num_experts=8, experts_per_token=16,
                sequence_length=2048, batch_size=1
            )
        
        # Test with valid MoE configuration
        flops = FLOPsCalculator.compute_moe_flops(
            hidden_size=4096, intermediate_size=11008,
            num_experts=64, experts_per_token=8,
            sequence_length=2048, batch_size=1,
            shared_experts=2
        )
        
        # Router FLOPs should be proportional to num_experts
        expected_router_flops = 2048 * 1 * 4096 * 64 * 2  # seq * batch * hidden * experts * 2
        assert abs(flops['router'] - expected_router_flops) / expected_router_flops < 0.1
    
    def test_parallel_adjustment_edge_cases(self):
        """Test edge cases in parallel adjustment calculations."""
        base_flops = {
            'attention_total': 1000000000,
            'mlp_total': 2000000000,
            'total': 3000000000
        }
        
        # Test with extreme parallel configuration
        extreme_config = ParallelConfig(
            tensor_parallel_size=64,
            pipeline_parallel_size=8,
            data_parallel_size=16
        )
        
        adjusted_flops = FLOPsCalculator.adjust_flops_for_parallel(base_flops, extreme_config)
        
        # Should handle extreme configurations without errors
        assert 'total_with_comm' in adjusted_flops
        assert adjusted_flops['total_with_comm'] >= adjusted_flops['total']
        
        # Per-device FLOPs should be reasonable
        total_devices = 64 * 8 * 16
        expected_per_device = base_flops['total'] / total_devices
        assert 'attention_total_per_device' in adjusted_flops
    
    def test_format_utilities_edge_cases(self):
        """Test formatting utilities with edge cases."""
        # Test with zero values
        assert FLOPsCalculator._format_flops(0) == "0"
        assert MemoryCalculator._format_memory(0) == "0.00B"
        
        # Test with very large values
        large_flops = 10**18  # 1 ExaFLOP
        formatted = FLOPsCalculator._format_flops(large_flops)
        assert 'E' in formatted or 'P' in formatted  # Should use scientific notation or Peta
        
        large_memory = 10**18  # 1 ExaByte
        formatted_mem = MemoryCalculator._format_memory(large_memory)
        assert 'EB' in formatted_mem or 'PB' in formatted_mem
        
        # Test with fractional values
        fractional_flops = 1500
        assert FLOPsCalculator._format_flops(fractional_flops) == "1.50K"
    
    def test_breakdown_summary_edge_cases(self):
        """Test breakdown summary generation with edge cases."""
        # Test with empty breakdown
        empty_flops = {'total': 0}
        summary = FLOPsCalculator.get_flops_breakdown_summary(empty_flops)
        assert summary['total'] == 0
        assert summary['breakdown'] == {}
        
        # Test with single component
        single_flops = {'attention_total': 1000000000, 'total': 1000000000}
        summary = FLOPsCalculator.get_flops_breakdown_summary(single_flops)
        assert summary['breakdown']['attention_total']['percentage'] == 100.0
        
        # Test with very small values
        tiny_memory = {
            'parameters': 1,
            'activations': 1,
            'total': 2
        }
        summary = MemoryCalculator.get_memory_breakdown_summary(tiny_memory)
        assert summary['total_gb'] > 0
        assert abs(sum(comp['percentage'] for comp in summary['breakdown'].values()) - 100.0) < 0.1


class TestShapeAnalyzer:
    """Test ShapeAnalyzer functionality."""
    
    def test_attention_shapes_basic(self):
        """Test basic attention shape analysis."""
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=32,
            head_dim=128
        )
        
        assert_shapes_valid(shapes)
        
        # Check expected shapes
        expected_shapes = ['q_proj', 'k_proj', 'v_proj', 'o_proj']
        for shape_name in expected_shapes:
            assert shape_name in shapes
            assert len(shapes[shape_name]) == 2  # Should be 2D matrices
        
        # Check specific dimensions
        assert shapes['q_proj'] == (4096, 4096)  # hidden_size x (num_heads * head_dim)
        assert shapes['k_proj'] == (4096, 4096)  # hidden_size x (num_kv_heads * head_dim)
        assert shapes['v_proj'] == (4096, 4096)
        assert shapes['o_proj'] == (4096, 4096)
    
    def test_attention_shapes_gqa(self):
        """Test attention shapes with Grouped Query Attention."""
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=4096,
            num_heads=32,
            num_kv_heads=8,  # 4x fewer KV heads
            head_dim=128
        )
        
        assert_shapes_valid(shapes)
        
        # Q projection should be full size
        assert shapes['q_proj'] == (4096, 4096)  # 32 * 128
        
        # K and V projections should be smaller
        assert shapes['k_proj'] == (4096, 1024)  # 8 * 128
        assert shapes['v_proj'] == (4096, 1024)  # 8 * 128
        
        # O projection should be full size
        assert shapes['o_proj'] == (4096, 4096)
    
    def test_mlp_shapes_basic(self):
        """Test basic MLP shape analysis."""
        shapes = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            use_gated_activation=True
        )
        
        assert_shapes_valid(shapes)
        
        # Check gated MLP shapes
        expected_shapes = ['gate_proj', 'up_proj', 'down_proj']
        for shape_name in expected_shapes:
            assert shape_name in shapes
        
        assert shapes['gate_proj'] == (4096, 11008)
        assert shapes['up_proj'] == (4096, 11008)
        assert shapes['down_proj'] == (11008, 4096)
    
    def test_mlp_shapes_standard(self):
        """Test MLP shapes with standard activation."""
        shapes = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            use_gated_activation=False
        )
        
        assert_shapes_valid(shapes)
        
        # Check standard MLP shapes
        expected_shapes = ['up_proj', 'down_proj']
        for shape_name in expected_shapes:
            assert shape_name in shapes
        
        # Should not have gate projection
        assert 'gate_proj' not in shapes
        
        assert shapes['up_proj'] == (4096, 11008)
        assert shapes['down_proj'] == (11008, 4096)
    
    def test_embedding_shapes(self):
        """Test embedding shape analysis."""
        shapes = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size=32000,
            hidden_size=4096,
            tie_embeddings=False
        )
        
        assert_shapes_valid(shapes)
        
        # Check embedding shapes
        assert 'input_embeddings' in shapes
        assert 'output_embeddings' in shapes
        
        assert shapes['input_embeddings'] == (32000, 4096)
        assert shapes['output_embeddings'] == (4096, 32000)  # Transposed for LM head
    
    def test_embedding_shapes_tied(self):
        """Test embedding shapes with tied embeddings."""
        shapes = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size=32000,
            hidden_size=4096,
            tie_embeddings=True
        )
        
        assert_shapes_valid(shapes)
        
        # With tied embeddings, should only have input embeddings
        assert 'input_embeddings' in shapes
        assert 'output_embeddings' not in shapes or shapes['output_embeddings'] == shapes['input_embeddings']
    
    def test_moe_shapes(self):
        """Test MoE shape analysis."""
        shapes = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=4096,
            intermediate_size=11008,
            num_experts=64,
            use_gated_activation=True
        )
        
        assert_shapes_valid(shapes)
        
        # Check router shapes
        assert 'router' in shapes
        assert shapes['router'] == (4096, 64)  # hidden_size x num_experts
        
        # Check expert shapes
        assert 'expert_gate_proj' in shapes
        assert 'expert_up_proj' in shapes
        assert 'expert_down_proj' in shapes
        
        # Expert shapes should be per-expert
        assert shapes['expert_gate_proj'] == (4096, 11008)
        assert shapes['expert_up_proj'] == (4096, 11008)
        assert shapes['expert_down_proj'] == (11008, 4096)
    
    def test_shapes_for_parallel_config(self):
        """Test shape analysis for parallel configurations."""
        base_shapes = {
            'q_proj': (4096, 4096),
            'k_proj': (4096, 4096),
            'v_proj': (4096, 4096),
            'o_proj': (4096, 4096),
            'gate_proj': (4096, 11008),
            'up_proj': (4096, 11008),
            'down_proj': (11008, 4096)
        }
        
        parallel_config = ParallelConfig(tensor_parallel_size=4)
        
        parallel_shapes = ShapeAnalyzer.adjust_shapes_for_parallel(
            base_shapes, parallel_config
        )
        
        assert_shapes_valid(parallel_shapes)
        
        # Check tensor parallel modifications
        assert parallel_shapes['q_proj'] == (4096, 1024)  # Output dim divided
        assert parallel_shapes['k_proj'] == (4096, 1024)
        assert parallel_shapes['v_proj'] == (4096, 1024)
        assert parallel_shapes['o_proj'] == (1024, 4096)  # Input dim divided
        
        assert parallel_shapes['gate_proj'] == (4096, 2752)  # 11008 / 4
        assert parallel_shapes['up_proj'] == (4096, 2752)
        assert parallel_shapes['down_proj'] == (2752, 4096)
    
    def test_detailed_shape_analysis(self):
        """Test detailed shape analysis with metadata."""
        model_config = create_mock_model_config('llama')
        
        detailed_analysis = ShapeAnalyzer.analyze_model_shapes(
            model_config, include_metadata=True
        )
        
        assert 'shapes' in detailed_analysis
        assert 'metadata' in detailed_analysis
        assert 'summary' in detailed_analysis
        
        # Check metadata
        metadata = detailed_analysis['metadata']
        assert 'total_parameters' in metadata
        assert 'memory_footprint' in metadata
        assert 'computation_intensity' in metadata
        
        # Check summary
        summary = detailed_analysis['summary']
        assert 'layer_types' in summary
        assert 'parameter_distribution' in summary
    
    def test_shape_validation(self):
        """Test shape validation utilities."""
        valid_shapes = {
            'matrix1': (1024, 2048),
            'matrix2': (2048, 1024),
            'vector': (1024,)
        }
        
        invalid_shapes = {
            'zero_dim': (0, 1024),
            'negative_dim': (1024, -1),
            'empty_tuple': ()
        }
        
        # Valid shapes should pass
        assert ShapeAnalyzer.validate_shapes(valid_shapes)
        
        # Invalid shapes should fail
        assert not ShapeAnalyzer.validate_shapes(invalid_shapes)
    
    def test_shape_compatibility(self):
        """Test shape compatibility checking."""
        # Compatible shapes for matrix multiplication
        shape_a = (1024, 2048)
        shape_b = (2048, 1024)
        
        assert ShapeAnalyzer.check_shape_compatibility(shape_a, shape_b, operation='matmul')
        
        # Incompatible shapes
        shape_c = (1024, 1024)
        assert not ShapeAnalyzer.check_shape_compatibility(shape_a, shape_c, operation='matmul')
    
    def test_edge_cases(self):
        """Test edge cases in shape analysis."""
        # Test with minimal dimensions
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=1, num_heads=1, num_kv_heads=1, head_dim=1
        )
        assert_shapes_valid(shapes)
        assert shapes['q_proj'] == (1, 1)
        
        # Test with large dimensions
        shapes = ShapeAnalyzer.compute_attention_shapes(
            hidden_size=16384, num_heads=128, num_kv_heads=128, head_dim=128
        )
        assert_shapes_valid(shapes)
        assert shapes['q_proj'] == (16384, 16384)
        
        # Test MoE with single expert
        shapes = ShapeAnalyzer.compute_moe_shapes(
            hidden_size=4096, intermediate_size=11008, num_experts=1
        )
        assert_shapes_valid(shapes)
        assert shapes['router'] == (4096, 1)


class TestMetricsIntegration:
    """Integration tests for metrics calculations."""
    
    def test_complete_model_analysis(self):
        """Test complete model analysis workflow."""
        model_config = create_mock_model_config('llama')
        
        # Compute FLOPs
        flops = FLOPsCalculator.compute_model_flops(model_config)
        assert_flops_breakdown_valid(flops)
        
        # Compute memory
        memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=model_config['vocab_size'] * model_config['hidden_size'] * 2,  # Rough estimate
            hidden_size=model_config['hidden_size'],
            sequence_length=2048,
            batch_size=1,
            num_layers=model_config['num_hidden_layers'],
            num_kv_heads=model_config['num_key_value_heads']
        )
        assert_memory_breakdown_valid(memory)
        
        # Compute shapes
        shapes = ShapeAnalyzer.analyze_model_shapes(model_config)
        assert_shapes_valid(shapes['shapes'])
        
        # Verify consistency
        assert flops['total'] > 0
        assert memory['total'] > 0
        assert len(shapes['shapes']) > 0
    
    def test_parallel_analysis_consistency(self):
        """Test consistency across parallel analysis."""
        model_config = create_mock_model_config('llama')
        parallel_config = ParallelConfig(tensor_parallel_size=4)
        
        # Base analysis
        base_flops = FLOPsCalculator.compute_model_flops(model_config)
        base_memory = MemoryCalculator.compute_total_memory_requirements(
            total_params=1000000000,  # 1B params
            hidden_size=model_config['hidden_size'],
            sequence_length=2048,
            batch_size=1,
            num_layers=model_config['num_hidden_layers'],
            num_kv_heads=model_config['num_key_value_heads']
        )
        
        # Parallel analysis
        parallel_flops = FLOPsCalculator.adjust_flops_for_parallel(base_flops, parallel_config)
        parallel_memory = MemoryCalculator.adjust_memory_for_parallel(base_memory, parallel_config)
        
        # FLOPs should be consistent (total doesn't change with parallelism)
        assert parallel_flops['total'] == base_flops['total']
        
        # Memory per device should be less than total
        assert parallel_memory['memory_per_device'] < base_memory['total']
    
    def test_moe_vs_dense_comparison(self):
        """Test comparison between MoE and dense models."""
        dense_config = create_mock_model_config('llama')
        moe_config = create_mock_model_config('deepseek_v3')
        
        # Compute FLOPs for both
        dense_flops = FLOPsCalculator.compute_model_flops(dense_config)
        moe_flops = FLOPsCalculator.compute_model_flops(moe_config)
        
        assert_flops_breakdown_valid(dense_flops)
        assert_flops_breakdown_valid(moe_flops)
        
        # MoE should have different FLOP structure
        assert 'mlp_total' in dense_flops
        assert 'moe_total' in moe_flops
        assert 'mlp_total' not in moe_flops
        
        # Both should have attention FLOPs
        assert 'attention_total' in dense_flops
        assert 'attention_total' in moe_flops