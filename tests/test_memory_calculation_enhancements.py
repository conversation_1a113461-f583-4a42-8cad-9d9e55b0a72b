"""
Unit tests for memory calculation enhancements.
"""

import pytest
from llm_modeling_metrics.metrics.memory_calculator import MemoryCalculator
from llm_modeling_metrics.models.dense_model import DenseModel
from tests.conftest import create_mock_model_config, MockConfig, assert_memory_breakdown_valid


class TestMemoryCalculatorDtypeSupport:
    """Test dtype-specific memory calculations in MemoryCalculator."""
    
    def test_compute_kv_cache_memory_by_dtype_fp16(self):
        """Test KV cache memory calculation with fp16 dtype."""
        model_config = create_mock_model_config('llama')
        
        memory = MemoryCalculator.compute_kv_cache_memory_by_dtype(
            model_config, sequence_length=2048, batch_size=1, dtype='fp16'
        )
        
        # Check that all numeric values are non-negative
        for key, value in memory.items():
            if isinstance(value, (int, float)):
                assert value >= 0, f"Memory value for {key} should be non-negative: {value}"
        
        assert memory['dtype'] == 'fp16'
        assert memory['bytes_per_element'] == 2
        assert memory['total_kv_cache'] > 0
        assert memory['kv_cache_per_token'] > 0
    
    def test_compute_kv_cache_memory_by_dtype_different_dtypes(self):
        """Test KV cache memory calculation with different dtypes."""
        model_config = create_mock_model_config('llama')
        
        dtypes = ['fp16', 'fp32', 'bf16', 'int8']
        memories = {}
        
        for dtype in dtypes:
            memories[dtype] = MemoryCalculator.compute_kv_cache_memory_by_dtype(
                model_config, sequence_length=2048, batch_size=1, dtype=dtype
            )
        
        # Verify dtype properties
        assert memories['fp16']['bytes_per_element'] == 2
        assert memories['fp32']['bytes_per_element'] == 4
        assert memories['bf16']['bytes_per_element'] == 2
        assert memories['int8']['bytes_per_element'] == 1
        
        # Verify memory ratios
        base_memory = memories['int8']['total_kv_cache']
        assert memories['fp16']['total_kv_cache'] == 2 * base_memory
        assert memories['bf16']['total_kv_cache'] == 2 * base_memory
        assert memories['fp32']['total_kv_cache'] == 4 * base_memory


class TestAttentionMechanismDetection:
    """Test attention mechanism detection functionality."""
    
    def test_get_attention_mechanism_info_mha(self):
        """Test attention mechanism detection for Multi-Head Attention (MHA)."""
        model_config = create_mock_model_config('llama')
        
        attention_info = MemoryCalculator.get_attention_mechanism_info(model_config)
        
        assert attention_info['type'] == 'MHA'
        assert attention_info['description'] == 'Multi-Head Attention'
        assert attention_info['num_attention_heads'] == 32
        assert attention_info['num_key_value_heads'] == 32
        assert attention_info['head_ratio'] == 1.0
    
    def test_get_attention_mechanism_info_gqa(self):
        """Test attention mechanism detection for Grouped Query Attention (GQA)."""
        model_config = create_mock_model_config('llama')
        model_config['num_key_value_heads'] = 8  # GQA: fewer KV heads
        
        attention_info = MemoryCalculator.get_attention_mechanism_info(model_config)
        
        assert attention_info['type'] == 'GQA'
        assert attention_info['description'] == 'Grouped Query Attention'
        assert attention_info['num_attention_heads'] == 32
        assert attention_info['num_key_value_heads'] == 8
        assert attention_info['head_ratio'] == 4.0  # 32 / 8
    
    def test_get_attention_mechanism_info_mla_indicators(self):
        """Test attention mechanism detection for MLA with various indicators."""
        # Test with kv_lora_rank indicator
        model_config = create_mock_model_config('llama')
        model_config['kv_lora_rank'] = 512  # MLA indicator
        
        attention_info = MemoryCalculator.get_attention_mechanism_info(model_config)
        
        assert attention_info['type'] == 'MLA'
        assert attention_info['description'] == 'Multi-head Latent Attention'


class TestDenseModelMemoryBreakdownByDtype:
    """Test memory breakdown methods with dtype parameters in DenseModel."""
    
    def test_get_memory_breakdown_by_dtype_fp16(self):
        """Test memory breakdown with fp16 dtype."""
        config = MockConfig(create_mock_model_config('llama'))
        model = DenseModel("test-model", config)
        
        breakdown = model.get_memory_breakdown_by_dtype(
            sequence_length=2048, batch_size=1, dtype='fp16', include_kv_cache=True
        )
        
        assert 'parameters' in breakdown
        assert 'kv_cache' in breakdown
        assert 'activations' in breakdown
        assert 'total' in breakdown
        
        assert breakdown['parameters'] > 0
        assert breakdown['kv_cache'] > 0
        assert breakdown['activations'] > 0
        assert breakdown['total'] > 0
        
        # Test attention mechanism detection separately
        attention_type = model.get_attention_mechanism_type()
        assert attention_type == 'MHA'


class TestMemoryCalculationErrorHandling:
    """Test error handling for invalid inputs and edge cases."""
    
    def test_compute_kv_cache_memory_by_dtype_invalid_dtype(self):
        """Test KV cache memory calculation with invalid dtype."""
        model_config = create_mock_model_config('llama')
        
        # Invalid dtype should default to fp16 (2 bytes)
        memory = MemoryCalculator.compute_kv_cache_memory_by_dtype(
            model_config, sequence_length=2048, batch_size=1, dtype='invalid_dtype'
        )
        
        assert memory['dtype'] == 'invalid_dtype'
        assert memory['bytes_per_element'] == 2  # Default to fp16
        assert memory['total_kv_cache'] > 0
    
    def test_compute_kv_cache_memory_by_dtype_zero_sequence_length(self):
        """Test KV cache memory calculation with zero sequence length."""
        model_config = create_mock_model_config('llama')
        
        memory = MemoryCalculator.compute_kv_cache_memory_by_dtype(
            model_config, sequence_length=0, batch_size=1, dtype='fp16'
        )
        
        assert memory['total_kv_cache'] == 0
        assert memory['kv_cache_per_layer'] == 0
        assert memory['kv_cache_per_token'] > 0  # Should still be positive


class TestMemoryCalculatorSequenceLengthAnalysis:
    """Test sequence length analysis functionality."""
    
    def test_analyze_memory_growth_by_sequence_length_basic(self):
        """Test basic memory growth analysis across sequence lengths."""
        model_config = create_mock_model_config('llama')
        total_params = 7000000000  # 7B parameters
        sequence_lengths = [512, 1024, 2048, 4096]
        
        analysis = MemoryCalculator.analyze_memory_growth_by_sequence_length(
            model_config, total_params, sequence_lengths, batch_size=1, dtype='fp16'
        )
        
        assert 'sequence_lengths' in analysis
        assert 'dtype' in analysis
        assert 'batch_size' in analysis
        assert 'attention_mechanism' in analysis
        assert 'memory_data' in analysis
        
        assert analysis['sequence_lengths'] == sequence_lengths
        assert analysis['dtype'] == 'fp16'
        assert analysis['batch_size'] == 1
        assert len(analysis['memory_data']) == len(sequence_lengths)
        
        # Verify each data point
        for i, data_point in enumerate(analysis['memory_data']):
            assert data_point['sequence_length'] == sequence_lengths[i]
            assert data_point['kv_cache_memory'] > 0
            assert data_point['total_memory'] > 0
            assert data_point['parameter_memory'] > 0
            assert data_point['activation_memory'] >= 0
            assert data_point['memory_per_token'] > 0
        
        # Verify memory growth pattern
        for i in range(1, len(analysis['memory_data'])):
            current = analysis['memory_data'][i]
            previous = analysis['memory_data'][i-1]
            
            # KV cache and activation memory should grow with sequence length
            assert current['kv_cache_memory'] > previous['kv_cache_memory']
            assert current['total_memory'] > previous['total_memory']
            
            # Parameter memory should remain constant
            assert current['parameter_memory'] == previous['parameter_memory']
    
    def test_analyze_memory_growth_by_sequence_length_different_dtypes(self):
        """Test memory growth analysis with different dtypes."""
        model_config = create_mock_model_config('llama')
        total_params = 7000000000
        sequence_lengths = [1024, 2048]
        
        dtypes = ['fp16', 'fp32']
        analyses = {}
        
        for dtype in dtypes:
            analyses[dtype] = MemoryCalculator.analyze_memory_growth_by_sequence_length(
                model_config, total_params, sequence_lengths, batch_size=1, dtype=dtype
            )
        
        # Verify dtype-specific differences
        for seq_idx in range(len(sequence_lengths)):
            fp16_memory = analyses['fp16']['memory_data'][seq_idx]['kv_cache_memory']
            fp32_memory = analyses['fp32']['memory_data'][seq_idx]['kv_cache_memory']
            
            # fp32 should be 2x fp16
            assert fp32_memory == 2 * fp16_memory


class TestMemoryCalculationAccuracy:
    """Test accuracy of memory calculations against known values."""
    
    def test_kv_cache_memory_calculation_accuracy(self):
        """Test KV cache memory calculation accuracy against manual computation."""
        model_config = {
            'hidden_size': 4096,
            'num_hidden_layers': 32,
            'num_attention_heads': 32,
            'num_key_value_heads': 32,
        }
        
        memory = MemoryCalculator.compute_kv_cache_memory_by_dtype(
            model_config, sequence_length=2048, batch_size=1, dtype='fp16'
        )
        
        # Manual calculation
        # K cache: batch_size * num_kv_heads * seq_len * head_dim * bytes_per_element
        # V cache: batch_size * num_kv_heads * seq_len * head_dim * bytes_per_element
        # Total per layer: 2 * (1 * 32 * 2048 * 128 * 2) = 2 * 16,777,216 = 33,554,432
        # Total for all layers: 33,554,432 * 32 = 1,073,741,824
        
        head_dim = 4096 // 32  # 128
        expected_per_layer = 2 * (1 * 32 * 2048 * head_dim * 2)
        expected_total = expected_per_layer * 32
        
        assert memory['kv_cache_per_layer'] == expected_per_layer
        assert memory['total_kv_cache'] == expected_total
    
    def test_memory_growth_linearity(self):
        """Test that memory growth is linear with sequence length."""
        model_config = create_mock_model_config('llama')
        total_params = 7000000000
        
        # Test sequence lengths that double each time
        sequence_lengths = [512, 1024, 2048, 4096]
        
        analysis = MemoryCalculator.analyze_memory_growth_by_sequence_length(
            model_config, total_params, sequence_lengths, batch_size=1, dtype='fp16'
        )
        
        # Verify linear scaling
        for i in range(1, len(analysis['memory_data'])):
            current = analysis['memory_data'][i]
            previous = analysis['memory_data'][i-1]
            
            # KV cache should scale exactly linearly
            kv_ratio = current['kv_cache_memory'] / previous['kv_cache_memory']
            seq_ratio = current['sequence_length'] / previous['sequence_length']
            
            assert abs(kv_ratio - seq_ratio) < 0.001  # Should be very close to exact