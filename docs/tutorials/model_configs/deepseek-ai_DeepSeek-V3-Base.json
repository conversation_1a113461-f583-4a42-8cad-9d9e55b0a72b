{"vocab_size": 129280, "max_position_embeddings": 163840, "hidden_size": 7168, "intermediate_size": 18432, "moe_intermediate_size": 2048, "num_hidden_layers": 61, "num_nextn_predict_layers": 1, "num_attention_heads": 128, "n_shared_experts": 1, "n_routed_experts": 256, "ep_size": 1, "routed_scaling_factor": 2.5, "kv_lora_rank": 512, "q_lora_rank": 1536, "qk_rope_head_dim": 64, "v_head_dim": 128, "qk_nope_head_dim": 128, "topk_method": "noaux_tc", "n_group": 8, "topk_group": 4, "num_experts_per_tok": 8, "moe_layer_freq": 1, "first_k_dense_replace": 3, "norm_topk_prob": true, "scoring_func": "sigmoid", "num_key_value_heads": 128, "hidden_act": "silu", "initializer_range": 0.02, "rms_norm_eps": 1e-06, "use_cache": true, "rope_theta": 10000, "rope_scaling": {"beta_fast": 32, "beta_slow": 1, "factor": 40, "mscale": 1.0, "mscale_all_dim": 1.0, "original_max_position_embeddings": 4096, "type": "yarn"}, "attention_bias": false, "attention_dropout": 0.0, "return_dict": true, "output_hidden_states": false, "torchscript": false, "torch_dtype": "bfloat16", "use_bfloat16": false, "tf_legacy_loss": false, "pruned_heads": {}, "tie_word_embeddings": false, "chunk_size_feed_forward": 0, "is_encoder_decoder": false, "is_decoder": false, "cross_attention_hidden_size": null, "add_cross_attention": false, "tie_encoder_decoder": false, "max_length": 20, "min_length": 0, "do_sample": false, "early_stopping": false, "num_beams": 1, "num_beam_groups": 1, "diversity_penalty": 0.0, "temperature": 1.0, "top_k": 50, "top_p": 1.0, "typical_p": 1.0, "repetition_penalty": 1.0, "length_penalty": 1.0, "no_repeat_ngram_size": 0, "encoder_no_repeat_ngram_size": 0, "bad_words_ids": null, "num_return_sequences": 1, "output_scores": false, "return_dict_in_generate": false, "forced_bos_token_id": null, "forced_eos_token_id": null, "remove_invalid_values": false, "exponential_decay_length_penalty": null, "suppress_tokens": null, "begin_suppress_tokens": null, "architectures": ["DeepseekV3ForCausalLM"], "finetuning_task": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "tokenizer_class": null, "prefix": null, "bos_token_id": 0, "pad_token_id": null, "eos_token_id": 1, "sep_token_id": null, "decoder_start_token_id": null, "task_specific_params": null, "problem_type": null, "_name_or_path": "deepseek-ai/DeepSeek-V3-Base", "transformers_version": "4.53.0", "auto_map": {"AutoConfig": "configuration_deepseek.DeepseekV3Config", "AutoModel": "modeling_deepseek.DeepseekV3Model", "AutoModelForCausalLM": "modeling_deepseek.DeepseekV3ForCausalLM"}, "model_type": "deepseek_v3", "quantization_config": {"activation_scheme": "dynamic", "fmt": "e4m3", "quant_method": "fp8", "weight_block_size": [128, 128]}, "output_attentions": false}