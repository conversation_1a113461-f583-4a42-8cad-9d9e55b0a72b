# Mixed Precision Guide

## Overview

Mixed precision support allows you to specify different data types (precisions) for different components of your LLM models. This enables fine-grained optimization of memory usage and computational efficiency while maintaining model quality.

## Why Mixed Precision?

Modern LLM deployments benefit from mixed precision because:

- **Memory Optimization**: Different components have different precision requirements
- **Quality Preservation**: Critical components can maintain high precision while others are compressed
- **Hardware Efficiency**: Modern GPUs have specialized support for different precisions
- **Cost Reduction**: Lower memory usage enables deployment on smaller, cheaper hardware

## Supported Precision Types

| Precision | Bytes | Description | Best Use Cases |
|-----------|-------|-------------|----------------|
| `fp32` | 4 | 32-bit floating point | Optimizer states, high-precision requirements |
| `bf16` | 2 | 16-bit brain floating point | Weights, activations (good balance) |
| `fp16` | 2 | 16-bit floating point | Gradients, general purpose |
| `fp8` | 1 | 8-bit floating point | KV cache, expert parameters |
| `int8` | 1 | 8-bit integer | Quantized weights |
| `fp4` | 0.5 | 4-bit floating point (packed) | Aggressive expert quantization |

## Component-Specific Precision

### Weight Precision (`weight_dtype`)

Controls the precision of model parameters (weights and biases).

**Recommendations:**
- **Production**: `bf16` - Good balance of quality and memory
- **Memory-constrained**: `int8` - Significant memory savings, requires calibration
- **Quality-critical**: `fp32` - Maximum precision, high memory usage

```python
# Conservative approach
memory = model.compute_memory_requirements(weight_dtype='bf16')

# Memory-optimized approach
memory = model.compute_memory_requirements(weight_dtype='int8')
```

### Activation Precision (`activation_dtype`)

Controls the precision of intermediate activations during forward pass.

**Recommendations:**
- **Standard**: `bf16` - Widely supported, stable training
- **Memory-critical**: `fp16` - Slight memory savings
- **Maximum quality**: `fp32` - Highest precision, rarely needed

```python
# Standard configuration
memory = model.compute_memory_requirements(activation_dtype='bf16')
```

### KV Cache Precision (`kv_cache_dtype`)

Controls the precision of key-value cache for attention mechanisms.

**Recommendations:**
- **Balanced**: `fp8` - Significant memory savings, minimal quality impact
- **Conservative**: `bf16` - No quality loss
- **Aggressive**: `fp4` - Maximum savings, monitor quality

```python
# Recommended for most use cases
memory = model.compute_memory_requirements(
    kv_cache_dtype='fp8',
    include_kv_cache=True
)
```

### Attention Parameter Precision (`attention_parameter_dtype`)

Controls the precision of attention-specific parameters (Q, K, V projections).

**Recommendations:**
- **Standard**: `bf16` - Maintains attention quality
- **Memory-optimized**: `fp8` - Good for large models
- **Quality-critical**: `fp32` - Maximum precision

### Expert Parameter Precision (`expert_parameter_dtype`) - MoE Only

Controls the precision of expert parameters in Mixture of Experts models.

**Recommendations:**
- **Balanced**: `fp8` - Good memory savings with acceptable quality
- **Aggressive**: `fp4` - Maximum memory savings for large expert counts
- **Conservative**: `bf16` - No quality loss

```python
# MoE-specific optimization
memory = moe_model.compute_memory_requirements(
    attention_parameter_dtype='bf16',  # Keep attention quality
    expert_parameter_dtype='fp8',      # Compress experts
)
```

### Training-Specific Precisions

#### Gradient Precision (`grad_dtype`)

**Recommendations:**
- **Standard**: `fp16` - Good balance for most training scenarios
- **Stable training**: `bf16` - Better numerical stability
- **Memory-critical**: `fp8` - Experimental, monitor convergence

#### Optimizer Precision (`optimizer_dtype`)

**Recommendations:**
- **Standard**: `fp32` - Maintains optimizer state precision
- **Memory-optimized**: `fp16` - Experimental, may affect convergence

```python
# Training configuration
memory = model.compute_memory_requirements(
    weight_dtype='bf16',
    activation_dtype='bf16',
    grad_dtype='fp16',
    optimizer_dtype='fp32',
    training=True
)
```

## Deployment Scenarios

### Production Inference (Quality Priority)

Optimize for quality while achieving reasonable memory savings.

```python
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',           # High quality weights
    activation_dtype='bf16',       # Stable activations
    kv_cache_dtype='fp8',          # Memory-efficient cache
    attention_parameter_dtype='bf16',  # Quality attention
    include_kv_cache=True
)
```

**Expected savings**: 15-25% memory reduction with minimal quality impact.

### Production Inference (Balanced)

Balance between memory savings and quality.

```python
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',
    activation_dtype='bf16',
    kv_cache_dtype='fp8',
    attention_parameter_dtype='fp8',   # Moderate compression
    include_kv_cache=True
)
```

**Expected savings**: 25-35% memory reduction with small quality impact.

### Edge Deployment (Memory Priority)

Maximize memory savings for resource-constrained environments.

```python
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='int8',           # Aggressive weight quantization
    activation_dtype='bf16',       # Keep activations stable
    kv_cache_dtype='fp8',
    attention_parameter_dtype='fp8',
    include_kv_cache=True
)
```

**Expected savings**: 40-60% memory reduction, monitor quality carefully.

### MoE Deployment

Optimize expert-heavy models with targeted precision.

```python
# For models with many experts
memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    weight_dtype='bf16',
    activation_dtype='bf16',
    kv_cache_dtype='fp8',
    attention_parameter_dtype='bf16',  # Keep attention quality
    expert_parameter_dtype='fp8',      # Compress expert parameters
    include_kv_cache=True
)

# Aggressive expert compression
aggressive_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    expert_parameter_dtype='fp4',      # 4-bit experts
    attention_parameter_dtype='bf16',
    include_kv_cache=True
)
```

### Training Configuration

Optimize for training stability and memory efficiency.

```python
training_memory = model.compute_memory_requirements(
    sequence_length=2048,
    batch_size=4,
    weight_dtype='bf16',           # Standard weight precision
    activation_dtype='bf16',       # Stable activations
    grad_dtype='fp16',             # Memory-efficient gradients
    optimizer_dtype='fp32',        # Stable optimizer states
    training=True
)
```

## Hardware Considerations

### NVIDIA H100/A100

- Excellent FP8 and BF16 support
- Hardware-accelerated mixed precision operations
- Recommended: BF16 weights, FP8 KV cache

### NVIDIA V100/T4

- Good FP16 support, limited FP8 support
- Focus on FP16/INT8 optimizations
- Recommended: BF16 weights, FP16 activations

### Edge Devices

- Limited precision support
- May require aggressive quantization
- Recommended: INT8 weights, careful quality monitoring

## Migration Strategy

### Phase 1: Conservative Start

Begin with minimal changes to establish baseline.

```python
# Start here - minimal risk
memory = model.compute_memory_requirements(
    weight_dtype='bf16',
    activation_dtype='bf16',
    kv_cache_dtype='fp8',  # Only change KV cache
    include_kv_cache=True
)
```

### Phase 2: Gradual Optimization

Incrementally add more aggressive settings.

```python
# Add attention parameter compression
memory = model.compute_memory_requirements(
    weight_dtype='bf16',
    activation_dtype='bf16',
    kv_cache_dtype='fp8',
    attention_parameter_dtype='fp8',  # Add this
    include_kv_cache=True
)
```

### Phase 3: Advanced Optimization

Apply more aggressive quantization based on quality results.

```python
# Add weight quantization if quality is acceptable
memory = model.compute_memory_requirements(
    weight_dtype='int8',           # Add this carefully
    activation_dtype='bf16',
    kv_cache_dtype='fp8',
    attention_parameter_dtype='fp8',
    include_kv_cache=True
)
```

## Quality Validation

### Essential Checks

1. **Perplexity Comparison**: Compare model perplexity before and after precision changes
2. **Task-Specific Evaluation**: Test on your specific downstream tasks
3. **Numerical Stability**: Monitor for NaN or infinite values
4. **Convergence**: Ensure training still converges (for training scenarios)

### Validation Script Template

```python
def validate_mixed_precision(model, test_data, precision_config):
    """
    Template for validating mixed precision configurations.
    """
    # Compute memory with new precision
    memory = model.compute_memory_requirements(**precision_config)
    
    # TODO: Add your quality metrics here
    # - Run inference on test set
    # - Compute perplexity/accuracy
    # - Compare against baseline
    
    return {
        'memory_gb': memory['total'] / (1024**3),
        'quality_score': None,  # Your quality metric
        'passed_validation': None  # Your validation logic
    }
```

## Best Practices

### Do's

- ✅ Start with conservative settings and gradually optimize
- ✅ Always validate quality on your specific tasks
- ✅ Monitor memory usage in production
- ✅ Use FP8 for KV cache as a safe first step
- ✅ Keep optimizer states in FP32 during training
- ✅ Test on representative hardware

### Don'ts

- ❌ Don't apply aggressive quantization without quality validation
- ❌ Don't assume all models respond the same to precision changes
- ❌ Don't ignore numerical stability issues
- ❌ Don't use FP4 without extensive testing
- ❌ Don't change all precisions at once

### Troubleshooting

**Issue**: Model quality degraded significantly
- **Solution**: Revert to higher precision for critical components (weights, attention)

**Issue**: Memory savings less than expected
- **Solution**: Check that KV cache and expert parameters are using lower precision

**Issue**: Training instability
- **Solution**: Use FP32 for optimizer states, BF16 for gradients

**Issue**: Hardware compatibility errors
- **Solution**: Check GPU support for specific precision types

## Performance Monitoring

### Key Metrics

- **Memory Usage**: Monitor actual GPU memory consumption
- **Throughput**: Tokens per second in inference
- **Quality Metrics**: Task-specific accuracy/perplexity
- **Numerical Stability**: Check for NaN/inf values

### Monitoring Template

```python
def monitor_mixed_precision_deployment(model, config):
    """
    Template for monitoring mixed precision in production.
    """
    import time
    import torch
    
    # Memory monitoring
    if torch.cuda.is_available():
        torch.cuda.reset_peak_memory_stats()
        # Run inference
        peak_memory = torch.cuda.max_memory_allocated()
        
    # Throughput monitoring
    start_time = time.time()
    # Run batch of inferences
    end_time = time.time()
    throughput = batch_size / (end_time - start_time)
    
    return {
        'peak_memory_gb': peak_memory / (1024**3),
        'throughput_tokens_per_sec': throughput,
        'config': config
    }
```

## Advanced Topics

### Custom Precision Combinations

For specialized use cases, you can create custom precision combinations:

```python
# Research configuration: Maximum quality
research_config = {
    'weight_dtype': 'fp32',
    'activation_dtype': 'fp32',
    'kv_cache_dtype': 'bf16',
    'attention_parameter_dtype': 'fp32'
}

# Extreme memory optimization
extreme_config = {
    'weight_dtype': 'int8',
    'activation_dtype': 'fp16',
    'kv_cache_dtype': 'fp4',
    'attention_parameter_dtype': 'fp8'
}
```

### Per-Layer Precision (Future)

Future versions may support per-layer precision control:

```python
# Conceptual - not yet implemented
layer_specific_config = {
    'early_layers': {'weight_dtype': 'bf16'},
    'middle_layers': {'weight_dtype': 'fp8'},
    'final_layers': {'weight_dtype': 'bf16'}
}
```

## Conclusion

Mixed precision is a powerful tool for optimizing LLM deployments. Start conservatively, validate quality carefully, and gradually optimize based on your specific requirements. The memory savings can be substantial while maintaining acceptable model quality for most use cases.

For questions or issues, refer to the examples in the `examples/` directory or consult the API reference documentation.