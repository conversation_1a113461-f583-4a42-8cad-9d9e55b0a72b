# Mixed Precision Migration Guide

## Overview

This guide helps you migrate from single-precision configurations to mixed precision setups. It provides step-by-step instructions, code examples, and best practices for a smooth transition.

## Before You Start

### Prerequisites

- Existing LLM modeling metrics setup
- Basic understanding of your current memory requirements
- Access to representative test data for quality validation
- Hardware information (GPU model, memory capacity)

### Backup Your Current Configuration

Before making changes, document your current setup:

```python
# Document your baseline
baseline_memory = model.compute_memory_requirements(
    sequence_length=2048,
    batch_size=1,
    dtype='bf16',  # Your current setting
    include_kv_cache=True
)

print(f"Baseline memory: {baseline_memory['total'] / (1024**3):.2f} GB")
```

## Migration Phases

### Phase 1: Assessment and Planning

#### Step 1.1: Analyze Current Usage

```python
# Analyze your current memory breakdown
def analyze_current_setup(model, sequence_length=2048, batch_size=1):
    """Analyze current memory usage patterns."""
    
    memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',  # Current precision
        include_kv_cache=True
    )
    
    total_gb = memory['total'] / (1024**3)
    
    print("=== Current Memory Analysis ===")
    print(f"Total memory: {total_gb:.2f} GB")
    print(f"Parameters: {memory['parameters'] / (1024**3):.2f} GB")
    print(f"Activations: {memory['activations'] / (1024**3):.2f} GB")
    print(f"KV Cache: {memory['kv_cache'] / (1024**3):.2f} GB")
    
    # Identify largest components
    components = {
        'parameters': memory['parameters'],
        'activations': memory['activations'],
        'kv_cache': memory['kv_cache']
    }
    
    largest_component = max(components, key=components.get)
    print(f"Largest component: {largest_component}")
    
    return memory, largest_component

# Run analysis
baseline_memory, largest_component = analyze_current_setup(model)
```

#### Step 1.2: Set Migration Goals

Define your objectives:

```python
# Define your migration goals
migration_goals = {
    'target_memory_reduction': 0.25,  # 25% reduction
    'min_quality_threshold': 0.95,   # 95% of original quality
    'max_latency_increase': 0.10,    # 10% latency increase acceptable
    'deployment_timeline': '2_weeks'  # Timeline for migration
}

print("Migration Goals:")
for goal, value in migration_goals.items():
    print(f"  {goal}: {value}")
```

### Phase 2: Conservative Migration

#### Step 2.1: KV Cache Optimization (Lowest Risk)

Start with KV cache compression - typically the safest change:

```python
def migrate_kv_cache(model, sequence_length=2048, batch_size=1):
    """Phase 2.1: Migrate KV cache to FP8."""
    
    print("=== Phase 2.1: KV Cache Migration ===")
    
    # Current setup
    baseline = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',
        include_kv_cache=True
    )
    
    # KV cache optimization
    kv_optimized = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='bf16',      # Keep weights unchanged
        activation_dtype='bf16',  # Keep activations unchanged
        kv_cache_dtype='fp8',     # Only change KV cache
        include_kv_cache=True
    )
    
    # Calculate impact
    memory_saved = baseline['total'] - kv_optimized['total']
    savings_percent = (memory_saved / baseline['total']) * 100
    
    print(f"Memory saved: {memory_saved / (1024**3):.2f} GB ({savings_percent:.1f}%)")
    print(f"New total: {kv_optimized['total'] / (1024**3):.2f} GB")
    
    return kv_optimized

# Execute Phase 2.1
phase2_memory = migrate_kv_cache(model)
```

#### Step 2.2: Quality Validation

Always validate quality after each change:

```python
def validate_quality_change(model, test_config, baseline_config=None):
    """
    Template for quality validation.
    Implement your specific quality metrics here.
    """
    
    print("=== Quality Validation ===")
    
    # TODO: Implement your quality validation
    # This is a template - replace with your actual validation logic
    
    validation_results = {
        'perplexity_change': None,      # Your perplexity comparison
        'task_accuracy_change': None,   # Your task-specific accuracy
        'numerical_stability': True,    # Check for NaN/inf values
        'inference_speed_change': None  # Latency comparison
    }
    
    # Example validation framework
    print("Validation checklist:")
    print("□ Run perplexity evaluation on test set")
    print("□ Test downstream task performance")
    print("□ Check for numerical instability")
    print("□ Measure inference latency")
    print("□ Compare memory usage")
    
    return validation_results

# Validate Phase 2.1 changes
validation_results = validate_quality_change(model, phase2_memory)
```

### Phase 3: Moderate Optimization

#### Step 3.1: Attention Parameter Compression

If Phase 2 validation passed, proceed to attention parameters:

```python
def migrate_attention_parameters(model, sequence_length=2048, batch_size=1):
    """Phase 3.1: Compress attention parameters."""
    
    print("=== Phase 3.1: Attention Parameter Migration ===")
    
    # Build on Phase 2 changes
    attention_optimized = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='bf16',              # Still unchanged
        activation_dtype='bf16',          # Still unchanged
        kv_cache_dtype='fp8',             # From Phase 2
        attention_parameter_dtype='fp8',  # New change
        include_kv_cache=True
    )
    
    # Compare to baseline
    baseline = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',
        include_kv_cache=True
    )
    
    total_saved = baseline['total'] - attention_optimized['total']
    total_percent = (total_saved / baseline['total']) * 100
    
    print(f"Cumulative savings: {total_saved / (1024**3):.2f} GB ({total_percent:.1f}%)")
    
    return attention_optimized

# Execute Phase 3.1 (only if Phase 2 validation passed)
if validation_results.get('numerical_stability', False):
    phase3_memory = migrate_attention_parameters(model)
    # Validate again
    validation_results = validate_quality_change(model, phase3_memory)
else:
    print("Skipping Phase 3 due to Phase 2 validation issues")
```

### Phase 4: Advanced Optimization

#### Step 4.1: Weight Quantization (Higher Risk)

Only proceed if previous phases validated successfully:

```python
def migrate_weights(model, sequence_length=2048, batch_size=1):
    """Phase 4.1: Quantize weights to INT8."""
    
    print("=== Phase 4.1: Weight Quantization ===")
    print("⚠️  WARNING: This is a higher-risk change. Monitor quality carefully.")
    
    # Aggressive optimization
    weight_quantized = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='int8',              # New: quantize weights
        activation_dtype='bf16',          # Keep stable
        kv_cache_dtype='fp8',             # From Phase 2
        attention_parameter_dtype='fp8',  # From Phase 3
        include_kv_cache=True
    )
    
    # Compare to baseline
    baseline = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',
        include_kv_cache=True
    )
    
    total_saved = baseline['total'] - weight_quantized['total']
    total_percent = (total_saved / baseline['total']) * 100
    
    print(f"Aggressive savings: {total_saved / (1024**3):.2f} GB ({total_percent:.1f}%)")
    print("🔍 CRITICAL: Validate quality extensively before production use")
    
    return weight_quantized

# Execute Phase 4.1 (only if all previous validations passed)
if all([
    validation_results.get('numerical_stability', False),
    # Add your quality thresholds here
]):
    phase4_memory = migrate_weights(model)
    # Extensive validation required
    validation_results = validate_quality_change(model, phase4_memory)
else:
    print("Skipping Phase 4 due to previous validation issues")
```

## MoE-Specific Migration

### MoE Phase 1: Expert Parameter Optimization

For MoE models, expert parameters are often the largest component:

```python
def migrate_moe_experts(moe_model, sequence_length=4096, batch_size=1):
    """MoE-specific migration: Expert parameter compression."""
    
    print("=== MoE Expert Parameter Migration ===")
    
    # Baseline MoE
    baseline = moe_model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',
        include_kv_cache=True
    )
    
    # Expert-focused optimization
    expert_optimized = moe_model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='bf16',                    # Keep base weights
        activation_dtype='bf16',                # Keep activations
        kv_cache_dtype='fp8',                   # Compress KV cache
        attention_parameter_dtype='bf16',       # Keep attention quality
        expert_parameter_dtype='fp8',           # Compress experts
        include_kv_cache=True
    )
    
    expert_saved = baseline['total'] - expert_optimized['total']
    expert_percent = (expert_saved / baseline['total']) * 100
    
    print(f"Expert compression savings: {expert_saved / (1024**3):.2f} GB ({expert_percent:.1f}%)")
    
    # Show expert-specific breakdown
    expert_memory_baseline = baseline.get('expert_parameters', 0)
    expert_memory_optimized = expert_optimized.get('expert_parameters', 0)
    expert_reduction = expert_memory_baseline - expert_memory_optimized
    
    print(f"Expert parameter reduction: {expert_reduction / (1024**3):.2f} GB")
    
    return expert_optimized

# For MoE models
if hasattr(model, 'n_routed_experts'):  # Check if it's a MoE model
    moe_optimized = migrate_moe_experts(model)
```

## Training Migration

### Training-Specific Considerations

```python
def migrate_training_precision(model, sequence_length=2048, batch_size=4):
    """Migrate training configuration to mixed precision."""
    
    print("=== Training Mixed Precision Migration ===")
    
    # Current training memory
    baseline_training = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype='bf16',
        training=True
    )
    
    # Mixed precision training
    mixed_training = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype='bf16',        # Keep weights stable
        activation_dtype='bf16',    # Keep activations stable
        grad_dtype='fp16',          # Compress gradients
        optimizer_dtype='fp32',     # Keep optimizer precise
        training=True
    )
    
    training_saved = baseline_training['total'] - mixed_training['total']
    training_percent = (training_saved / baseline_training['total']) * 100
    
    print(f"Training memory savings: {training_saved / (1024**3):.2f} GB ({training_percent:.1f}%)")
    
    # Training-specific warnings
    print("\n⚠️  Training Migration Warnings:")
    print("• Monitor convergence carefully with FP16 gradients")
    print("• Keep optimizer states in FP32 for stability")
    print("• Consider gradient clipping adjustments")
    print("• Validate on smaller experiments first")
    
    return mixed_training

# Training migration
training_memory = migrate_training_precision(model)
```

## Rollback Strategy

### Implementing Safe Rollback

```python
class MigrationManager:
    """Manages mixed precision migration with rollback capability."""
    
    def __init__(self, model):
        self.model = model
        self.migration_history = []
        self.current_config = {'dtype': 'bf16'}  # Baseline
    
    def apply_config(self, new_config, validation_func=None):
        """Apply new configuration with rollback capability."""
        
        # Save current state
        self.migration_history.append(self.current_config.copy())
        
        try:
            # Test new configuration
            memory = self.model.compute_memory_requirements(**new_config)
            
            # Run validation if provided
            if validation_func:
                validation_passed = validation_func(new_config)
                if not validation_passed:
                    raise ValueError("Validation failed")
            
            # If successful, update current config
            self.current_config = new_config.copy()
            print(f"✅ Successfully applied configuration")
            return memory
            
        except Exception as e:
            print(f"❌ Configuration failed: {e}")
            print("Rolling back to previous configuration")
            return None
    
    def rollback(self):
        """Rollback to previous configuration."""
        if self.migration_history:
            self.current_config = self.migration_history.pop()
            print("🔄 Rolled back to previous configuration")
        else:
            print("No previous configuration to rollback to")
    
    def get_current_config(self):
        """Get current configuration."""
        return self.current_config.copy()

# Usage example
migration_manager = MigrationManager(model)

# Try Phase 2 migration
phase2_config = {
    'weight_dtype': 'bf16',
    'activation_dtype': 'bf16',
    'kv_cache_dtype': 'fp8',
    'include_kv_cache': True
}

memory = migration_manager.apply_config(phase2_config, validate_quality_change)
if memory is None:
    migration_manager.rollback()
```

## Production Deployment

### Gradual Production Rollout

```python
def production_migration_plan():
    """Template for production migration."""
    
    plan = {
        'week_1': {
            'action': 'Deploy KV cache optimization to 10% of traffic',
            'config': {'kv_cache_dtype': 'fp8'},
            'monitoring': ['memory_usage', 'latency', 'quality_metrics'],
            'rollback_trigger': 'quality_degradation > 2%'
        },
        'week_2': {
            'action': 'Scale to 50% of traffic if week 1 successful',
            'config': {'kv_cache_dtype': 'fp8'},
            'monitoring': ['memory_usage', 'latency', 'quality_metrics'],
            'rollback_trigger': 'latency_increase > 10%'
        },
        'week_3': {
            'action': 'Full rollout + attention parameter optimization',
            'config': {'kv_cache_dtype': 'fp8', 'attention_parameter_dtype': 'fp8'},
            'monitoring': ['memory_usage', 'latency', 'quality_metrics'],
            'rollback_trigger': 'any_metric_degradation'
        }
    }
    
    return plan

# Print production plan
plan = production_migration_plan()
for week, details in plan.items():
    print(f"\n{week.upper()}:")
    for key, value in details.items():
        print(f"  {key}: {value}")
```

## Monitoring and Alerting

### Set Up Monitoring

```python
def setup_migration_monitoring():
    """Template for monitoring mixed precision deployment."""
    
    monitoring_config = {
        'memory_metrics': [
            'gpu_memory_utilization',
            'peak_memory_usage',
            'memory_fragmentation'
        ],
        'performance_metrics': [
            'tokens_per_second',
            'latency_p50',
            'latency_p95',
            'latency_p99'
        ],
        'quality_metrics': [
            'perplexity',
            'task_accuracy',
            'user_satisfaction_score'
        ],
        'stability_metrics': [
            'nan_count',
            'inf_count',
            'gradient_norm',
            'loss_spikes'
        ]
    }
    
    # Alert thresholds
    alert_thresholds = {
        'memory_increase': 5,      # % increase from expected
        'latency_increase': 10,    # % increase from baseline
        'quality_decrease': 2,     # % decrease from baseline
        'stability_issues': 1      # Any stability issue
    }
    
    print("Monitoring Configuration:")
    for category, metrics in monitoring_config.items():
        print(f"\n{category}:")
        for metric in metrics:
            print(f"  - {metric}")
    
    print("\nAlert Thresholds:")
    for alert, threshold in alert_thresholds.items():
        print(f"  {alert}: {threshold}%")

setup_migration_monitoring()
```

## Common Issues and Solutions

### Issue 1: Quality Degradation

**Symptoms**: Lower accuracy, higher perplexity, poor task performance

**Solutions**:
```python
# Revert to higher precision for critical components
recovery_config = {
    'weight_dtype': 'bf16',              # Increase from int8
    'activation_dtype': 'bf16',          # Keep high
    'attention_parameter_dtype': 'bf16', # Increase from fp8
    'kv_cache_dtype': 'fp8'             # Keep optimized
}
```

### Issue 2: Numerical Instability

**Symptoms**: NaN values, infinite gradients, training divergence

**Solutions**:
```python
# Stability-focused configuration
stable_config = {
    'weight_dtype': 'bf16',      # BF16 more stable than FP16
    'activation_dtype': 'bf16',  # Keep stable
    'grad_dtype': 'bf16',        # Increase from fp16
    'optimizer_dtype': 'fp32'    # Always keep high precision
}
```

### Issue 3: Hardware Compatibility

**Symptoms**: Unsupported precision errors, poor performance

**Solutions**:
```python
# Check hardware capabilities
def check_hardware_support():
    """Check what precisions your hardware supports."""
    import torch
    
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name()
        print(f"GPU: {device_name}")
        
        # Hardware-specific recommendations
        if 'H100' in device_name or 'A100' in device_name:
            return ['fp32', 'bf16', 'fp16', 'fp8', 'int8']
        elif 'V100' in device_name or 'T4' in device_name:
            return ['fp32', 'bf16', 'fp16', 'int8']
        else:
            return ['fp32', 'bf16', 'fp16']
    
    return ['fp32', 'bf16', 'fp16']  # CPU fallback

supported_precisions = check_hardware_support()
print(f"Supported precisions: {supported_precisions}")
```

## Success Metrics

### Define Success Criteria

```python
def evaluate_migration_success(baseline_memory, optimized_memory, quality_metrics):
    """Evaluate if migration was successful."""
    
    # Memory improvement
    memory_savings = (baseline_memory['total'] - optimized_memory['total']) / baseline_memory['total']
    
    # Quality preservation
    quality_preserved = quality_metrics.get('quality_score', 0) > 0.95
    
    # Stability check
    numerically_stable = quality_metrics.get('numerical_stability', False)
    
    success_criteria = {
        'memory_savings_achieved': memory_savings > 0.15,  # 15% minimum
        'quality_preserved': quality_preserved,
        'numerically_stable': numerically_stable,
        'production_ready': all([
            memory_savings > 0.15,
            quality_preserved,
            numerically_stable
        ])
    }
    
    print("Migration Success Evaluation:")
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {criterion}: {status}")
    
    return success_criteria

# Example usage
success = evaluate_migration_success(baseline_memory, phase3_memory, validation_results)
```

## Next Steps

After successful migration:

1. **Document Configuration**: Save your successful configuration
2. **Update Deployment Scripts**: Integrate mixed precision into your deployment
3. **Train Team**: Educate team members on new configuration
4. **Monitor Long-term**: Set up ongoing monitoring
5. **Plan Further Optimization**: Consider more advanced techniques

```python
# Save successful configuration
successful_config = {
    'weight_dtype': 'bf16',
    'activation_dtype': 'bf16',
    'kv_cache_dtype': 'fp8',
    'attention_parameter_dtype': 'fp8',
    'memory_savings': '25%',
    'quality_impact': 'minimal',
    'validated_date': '2024-01-15'
}

print("Successful Configuration:")
for key, value in successful_config.items():
    print(f"  {key}: {value}")
```

## Conclusion

Mixed precision migration should be approached systematically with careful validation at each step. Start conservatively, validate thoroughly, and gradually optimize based on your specific requirements and constraints.

Remember: **Quality first, optimization second**. It's better to have a stable model with moderate memory savings than an unstable model with aggressive optimization.