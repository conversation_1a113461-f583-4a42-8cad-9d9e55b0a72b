# Hardware Integration Guide

The hardware integration module provides comprehensive support for analyzing LLM performance across different hardware platforms including GPUs and NPUs.

## Overview

The hardware integration consists of several key components:

- **HardwareAdapter**: Wraps existing GPU module functionality for web interface compatibility
- **HardwareService**: High-level service for hardware management and recommendations
- **HardwareSpec**: Data model representing hardware specifications with validation
- **ValidationResult**: Result object for hardware validation operations

## Quick Start

```python
from llm_modeling_metrics.hardware import HardwareService

# Initialize the service
service = HardwareService()

# Get all available hardware
hardware = service.get_available_hardware()
print(f"Found {len(hardware['gpu'])} GPUs and {len(hardware['npu'])} NPUs")

# Get specific hardware specs
h100 = service.get_hardware_specs('nvidia_h100_sxm5')
if h100:
    print(f"H100 Memory: {h100.memory_size_gb}GB")
    print(f"Peak BF16: {h100.get_peak_flops('bf16')} TFLOPS")
```

## Hardware Specifications

### Supported Hardware

The system automatically loads hardware specifications from `gpu/data/gpu_specs.yaml`, including:

**GPUs:**
- NVIDIA A100
- NVIDIA H100 SXM5
- NVIDIA B200
- NVIDIA B300
- NVIDIA L20
- NVIDIA H20

**NPUs:**
- Huawei Ascend 910B

### Hardware Properties

Each hardware device includes:

- **Basic Info**: Name, architecture, form factor, year
- **Memory**: Size (GB), type, bandwidth (GB/s), L2 cache
- **Performance**: Tensor and vector performance by precision
- **Physical**: TDP, manufacturing process, interconnect

## Hardware Recommendations

Get hardware recommendations based on workload characteristics:

```python
from llm_modeling_metrics.hardware.models import WorkloadProfile

# Define your workload
workload = WorkloadProfile(
    model_type="dense",  # or "moe"
    batch_size=32,
    sequence_length=2048,
    precision_requirements=["fp16", "bf16"],
    memory_constraints=50  # GB
)

# Get recommendations
recommendations = service.get_hardware_recommendations(workload)

for rec in recommendations[:3]:
    print(f"{rec.hardware_name}: {rec.score:.1f}/100")
    print(f"Reasons: {', '.join(rec.reasons)}")
```

## Hardware Validation

### Configuration Validation

Validate hardware configuration files:

```python
from llm_modeling_metrics.hardware.validation import validate_hardware_config_file

result = validate_hardware_config_file('gpu/data/gpu_specs.yaml')

if result.is_valid:
    print("✅ Configuration is valid")
else:
    print("❌ Configuration has errors:")
    for error in result.errors:
        print(f"  • {error}")
```

### Compatibility Validation

Check hardware compatibility with operators:

```python
# Mock operators for testing
class MockOperator:
    def __init__(self, name, precision="fp16"):
        self.name = name
        self.precision = precision
        self.operator_type = "attention"

operators = [MockOperator("attn_1"), MockOperator("mlp_1")]

validation = service.validate_hardware_compatibility('nvidia_h100_sxm5', operators)

if validation.is_valid:
    print("✅ Hardware is compatible")
    
for recommendation in validation.recommendations:
    print(f"💡 {recommendation}")
```

## Hardware Specifications Format

The hardware specifications follow this YAML structure:

```yaml
gpus:
  hardware_id:
    name: "Hardware Name"
    architecture: "Architecture Name"
    memory_size_gb: 80
    memory_bandwidth_gbps: 3352
    tensor_performance:
      fp16_tensor: 989.4
      bf16_tensor: 989.4
      fp8_tensor: 1978.9
    vector_performance:
      fp32: 66.9
      fp16: 133.8
```

### Required Fields

- `name`: Human-readable hardware name
- `memory_size_gb`: Memory capacity in gigabytes
- `memory_bandwidth_gbps`: Memory bandwidth in GB/s

### Optional Fields

- `architecture`: Hardware architecture name
- `form_factor`: Physical form factor
- `year`: Release year
- `memory_type`: Memory type (HBM3, GDDR6, etc.)
- `l2_cache_mb`: L2 cache size in MB
- `tensor_performance`: Tensor operation performance by precision
- `vector_performance`: Vector operation performance by precision
- `tdp_watts`: Thermal design power
- `manufacturing_process`: Manufacturing process node
- `interconnect`: Interconnect specifications

## API Reference

### HardwareService

Main service class for hardware operations.

#### Methods

- `get_available_hardware()` → Dict[str, List[HardwareSpec]]
- `get_hardware_specs(hardware_id: str)` → Optional[HardwareSpec]
- `validate_hardware_compatibility(hardware_id: str, operators: List[BaseOperator])` → ValidationResult
- `get_hardware_recommendations(workload: WorkloadProfile)` → List[HardwareRecommendation]
- `reload_hardware_specifications()` → None

### HardwareSpec

Data model representing hardware specifications.

#### Properties

- `id`: Hardware identifier
- `name`: Hardware name
- `type`: HardwareType (GPU or NPU)
- `memory_size_gb`: Memory capacity
- `memory_bandwidth_gbps`: Memory bandwidth
- `supported_precisions`: List of supported precisions
- `peak_flops`: Peak FLOPS by precision

#### Methods

- `get_peak_flops(precision: str)` → float
- `supports_precision(precision: str)` → bool
- `is_tensor_core_capable()` → bool

### ValidationResult

Result object for validation operations.

#### Properties

- `is_valid`: Whether validation passed
- `errors`: List of error messages
- `warnings`: List of warning messages
- `recommendations`: List of recommendation messages

#### Methods

- `add_error(message: str)` → None
- `add_warning(message: str)` → None
- `add_recommendation(message: str)` → None
- `has_issues()` → bool

## Error Handling

The hardware integration includes comprehensive error handling:

### Common Exceptions

- `FileNotFoundError`: Hardware specifications file not found
- `ValueError`: Invalid hardware specification data
- `RuntimeError`: Failed to load hardware specifications

### Validation Errors

- Missing required fields
- Invalid data types
- Negative performance values
- Unknown precision types

## Best Practices

### 1. Hardware Selection

- Consider memory requirements first
- Match precision requirements with hardware capabilities
- Factor in power and cost constraints
- Use recommendations for guidance

### 2. Configuration Management

- Validate configurations before deployment
- Keep specifications up to date
- Use version control for configuration files
- Test with realistic workloads

### 3. Performance Optimization

- Leverage tensor cores when available
- Use appropriate precisions for your use case
- Consider memory bandwidth limitations
- Monitor actual vs. theoretical performance

## Integration Examples

### Web Interface Integration

```python
from llm_modeling_metrics.hardware import HardwareService

# In your web application
service = HardwareService()

@app.route('/api/hardware/list')
def list_hardware():
    hardware = service.get_available_hardware()
    return jsonify(hardware)

@app.route('/api/hardware/<hardware_id>')
def get_hardware(hardware_id):
    spec = service.get_hardware_specs(hardware_id)
    if spec:
        return jsonify(spec.__dict__)
    return jsonify({'error': 'Hardware not found'}), 404
```

### Batch Processing

```python
# Process multiple workloads
workloads = [
    WorkloadProfile(model_type="dense", batch_size=16, sequence_length=1024, precision_requirements=["fp16"]),
    WorkloadProfile(model_type="moe", batch_size=32, sequence_length=2048, precision_requirements=["bf16"])
]

for i, workload in enumerate(workloads):
    print(f"Workload {i+1}:")
    recommendations = service.get_hardware_recommendations(workload)
    best = recommendations[0] if recommendations else None
    if best:
        print(f"  Best: {best.hardware_name} ({best.score:.1f}/100)")
```

## Troubleshooting

### Common Issues

1. **Hardware specifications not found**
   - Check that `gpu/data/gpu_specs.yaml` exists
   - Verify file permissions
   - Ensure YAML syntax is valid

2. **Import errors**
   - Verify package installation
   - Check Python path
   - Ensure dependencies are installed

3. **Validation failures**
   - Check required fields are present
   - Verify numeric values are positive
   - Ensure precision names are valid

### Debug Mode

Enable debug logging for troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

service = HardwareService()
# Debug information will be printed
```

## Contributing

To add new hardware specifications:

1. Add hardware data to `gpu/data/gpu_specs.yaml`
2. Follow the required field structure
3. Validate the configuration
4. Test with the hardware service
5. Update documentation

For new features:

1. Extend the appropriate service class
2. Add comprehensive tests
3. Update documentation
4. Ensure backward compatibility