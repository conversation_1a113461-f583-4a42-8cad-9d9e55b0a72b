"""System monitoring and error handling for hardware integration."""

import logging
import time
import traceback
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import defaultdict, deque

from .models import ValidationResult, HardwareSpec


class HealthStatus(Enum):
    """System health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class HealthCheck:
    """Health check result."""
    name: str
    status: HealthStatus
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)
    duration_ms: Optional[float] = None


@dataclass
class ErrorEvent:
    """Error event record."""
    error_id: str
    severity: ErrorSeverity
    component: str
    message: str
    exception: Optional[Exception] = None
    traceback_str: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    context: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime = field(default_factory=datetime.now)
    hardware_load_time_ms: float = 0.0
    validation_time_ms: float = 0.0
    config_reload_count: int = 0
    error_count: int = 0
    warning_count: int = 0
    active_hardware_count: int = 0
    memory_usage_mb: float = 0.0


class HardwareSystemMonitor:
    """System monitoring and health checking for hardware integration."""
    
    def __init__(self, check_interval: int = 60, max_error_history: int = 1000):
        """Initialize the system monitor.
        
        Args:
            check_interval: Health check interval in seconds
            max_error_history: Maximum number of error events to keep
        """
        self.check_interval = check_interval
        self.max_error_history = max_error_history
        
        # Health and error tracking
        self._health_checks: Dict[str, HealthCheck] = {}
        self._error_events: deque = deque(maxlen=max_error_history)
        self._error_counts: Dict[str, int] = defaultdict(int)
        self._metrics_history: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        
        # Monitoring state
        self._monitoring_active = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
        # Registered health check functions
        self._health_check_functions: Dict[str, Callable[[], HealthCheck]] = {}
        
        # Fallback mechanisms
        self._fallback_configs: Dict[str, Any] = {}
        self._fallback_active = False
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
        # Register default health checks
        self._register_default_health_checks()
    
    def start_monitoring(self):
        """Start the system monitoring."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitor_thread.start()
        self.logger.info("Hardware system monitoring started")
    
    def stop_monitoring(self):
        """Stop the system monitoring."""
        self._monitoring_active = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        self.logger.info("Hardware system monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self._monitoring_active:
            try:
                self._run_health_checks()
                self._collect_metrics()
                self._cleanup_old_data()
                time.sleep(self.check_interval)
            except Exception as e:
                self.record_error("monitor", ErrorSeverity.HIGH, 
                                f"Monitoring loop error: {e}", e)
                time.sleep(min(self.check_interval, 30))  # Shorter sleep on error
    
    def _run_health_checks(self):
        """Run all registered health checks."""
        with self._lock:
            for check_name, check_function in self._health_check_functions.items():
                try:
                    start_time = time.time()
                    health_check = check_function()
                    health_check.duration_ms = (time.time() - start_time) * 1000
                    self._health_checks[check_name] = health_check
                    
                    # Log warnings and errors
                    if health_check.status in [HealthStatus.WARNING, HealthStatus.ERROR, HealthStatus.CRITICAL]:
                        self.logger.warning(f"Health check {check_name}: {health_check.message}")
                        
                except Exception as e:
                    self.record_error("health_check", ErrorSeverity.MEDIUM,
                                    f"Health check {check_name} failed: {e}", e)
                    self._health_checks[check_name] = HealthCheck(
                        name=check_name,
                        status=HealthStatus.ERROR,
                        message=f"Health check failed: {e}"
                    )
    
    def _collect_metrics(self):
        """Collect system performance metrics."""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
        except ImportError:
            memory_mb = 0.0
        
        metrics = SystemMetrics(
            error_count=len([e for e in self._error_events if not e.resolved]),
            warning_count=len([h for h in self._health_checks.values() 
                             if h.status == HealthStatus.WARNING]),
            memory_usage_mb=memory_mb
        )
        
        with self._lock:
            self._metrics_history.append(metrics)
    
    def _cleanup_old_data(self):
        """Clean up old monitoring data."""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        with self._lock:
            # Clean up old error events
            while (self._error_events and 
                   self._error_events[0].timestamp < cutoff_time):
                self._error_events.popleft()
            
            # Clean up old health checks
            for check_name, health_check in list(self._health_checks.items()):
                if health_check.timestamp < cutoff_time:
                    del self._health_checks[check_name]
    
    def register_health_check(self, name: str, check_function: Callable[[], HealthCheck]):
        """Register a custom health check function.
        
        Args:
            name: Name of the health check
            check_function: Function that returns a HealthCheck
        """
        self._health_check_functions[name] = check_function
        self.logger.info(f"Registered health check: {name}")
    
    def record_error(self, component: str, severity: ErrorSeverity, 
                    message: str, exception: Optional[Exception] = None,
                    context: Optional[Dict[str, Any]] = None):
        """Record an error event.
        
        Args:
            component: Component where error occurred
            severity: Error severity level
            message: Error message
            exception: Optional exception object
            context: Optional context information
        """
        error_id = f"{component}_{int(time.time() * 1000)}"
        
        error_event = ErrorEvent(
            error_id=error_id,
            severity=severity,
            component=component,
            message=message,
            exception=exception,
            traceback_str=traceback.format_exc() if exception else None,
            context=context or {}
        )
        
        with self._lock:
            self._error_events.append(error_event)
            self._error_counts[component] += 1
        
        # Log the error
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }[severity]
        
        self.logger.log(log_level, f"[{component}] {message}", exc_info=exception)
        
        # Activate fallback for critical errors
        if severity == ErrorSeverity.CRITICAL:
            self._activate_fallback(component)
    
    def resolve_error(self, error_id: str, resolution_notes: Optional[str] = None):
        """Mark an error as resolved.
        
        Args:
            error_id: ID of the error to resolve
            resolution_notes: Optional resolution notes
        """
        with self._lock:
            for error_event in self._error_events:
                if error_event.error_id == error_id:
                    error_event.resolved = True
                    error_event.resolution_time = datetime.now()
                    if resolution_notes:
                        error_event.context["resolution_notes"] = resolution_notes
                    break
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status.
        
        Returns:
            Dictionary with system health information
        """
        with self._lock:
            health_checks = list(self._health_checks.values())
            error_events = list(self._error_events)
            recent_metrics = list(self._metrics_history)[-10:]  # Last 10 metrics
        
        # Determine overall status
        overall_status = HealthStatus.HEALTHY
        if any(h.status == HealthStatus.CRITICAL for h in health_checks):
            overall_status = HealthStatus.CRITICAL
        elif any(h.status == HealthStatus.ERROR for h in health_checks):
            overall_status = HealthStatus.ERROR
        elif any(h.status == HealthStatus.WARNING for h in health_checks):
            overall_status = HealthStatus.WARNING
        
        # Count unresolved errors by severity
        unresolved_errors = [e for e in error_events if not e.resolved]
        error_counts = {
            "critical": len([e for e in unresolved_errors if e.severity == ErrorSeverity.CRITICAL]),
            "high": len([e for e in unresolved_errors if e.severity == ErrorSeverity.HIGH]),
            "medium": len([e for e in unresolved_errors if e.severity == ErrorSeverity.MEDIUM]),
            "low": len([e for e in unresolved_errors if e.severity == ErrorSeverity.LOW])
        }
        
        return {
            "overall_status": overall_status.value,
            "timestamp": datetime.now().isoformat(),
            "health_checks": {h.name: {
                "status": h.status.value,
                "message": h.message,
                "timestamp": h.timestamp.isoformat(),
                "duration_ms": h.duration_ms
            } for h in health_checks},
            "error_summary": error_counts,
            "total_errors": len(error_events),
            "unresolved_errors": len(unresolved_errors),
            "fallback_active": self._fallback_active,
            "monitoring_active": self._monitoring_active,
            "metrics": {
                "avg_memory_mb": sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics) if recent_metrics else 0,
                "total_config_reloads": sum(m.config_reload_count for m in recent_metrics),
                "avg_hardware_load_time": sum(m.hardware_load_time_ms for m in recent_metrics) / len(recent_metrics) if recent_metrics else 0
            }
        }
    
    def get_error_history(self, component: Optional[str] = None, 
                         severity: Optional[ErrorSeverity] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """Get error history with optional filtering.
        
        Args:
            component: Optional component filter
            severity: Optional severity filter
            limit: Maximum number of errors to return
            
        Returns:
            List of error event dictionaries
        """
        with self._lock:
            errors = list(self._error_events)
        
        # Apply filters
        if component:
            errors = [e for e in errors if e.component == component]
        if severity:
            errors = [e for e in errors if e.severity == severity]
        
        # Sort by timestamp (newest first) and limit
        errors.sort(key=lambda x: x.timestamp, reverse=True)
        errors = errors[:limit]
        
        return [{
            "error_id": e.error_id,
            "severity": e.severity.value,
            "component": e.component,
            "message": e.message,
            "timestamp": e.timestamp.isoformat(),
            "resolved": e.resolved,
            "resolution_time": e.resolution_time.isoformat() if e.resolution_time else None,
            "context": e.context
        } for e in errors]
    
    def set_fallback_config(self, component: str, fallback_data: Any):
        """Set fallback configuration for a component.
        
        Args:
            component: Component name
            fallback_data: Fallback configuration data
        """
        self._fallback_configs[component] = fallback_data
        self.logger.info(f"Set fallback configuration for {component}")
    
    def _activate_fallback(self, component: str):
        """Activate fallback mechanism for a component.
        
        Args:
            component: Component to activate fallback for
        """
        if component in self._fallback_configs:
            self._fallback_active = True
            self.logger.warning(f"Activated fallback for component: {component}")
        else:
            self.logger.error(f"No fallback configuration available for component: {component}")
    
    def get_fallback_config(self, component: str) -> Optional[Any]:
        """Get fallback configuration for a component.
        
        Args:
            component: Component name
            
        Returns:
            Fallback configuration or None if not available
        """
        return self._fallback_configs.get(component)
    
    def _register_default_health_checks(self):
        """Register default health check functions."""
        
        def hardware_config_health() -> HealthCheck:
            """Check hardware configuration health."""
            try:
                # This would be connected to the actual config manager
                # For now, return a basic health check
                return HealthCheck(
                    name="hardware_config",
                    status=HealthStatus.HEALTHY,
                    message="Hardware configuration is accessible"
                )
            except Exception as e:
                return HealthCheck(
                    name="hardware_config",
                    status=HealthStatus.ERROR,
                    message=f"Hardware configuration error: {e}"
                )
        
        def memory_usage_health() -> HealthCheck:
            """Check memory usage health."""
            try:
                import psutil
                memory_percent = psutil.virtual_memory().percent
                
                if memory_percent > 90:
                    status = HealthStatus.CRITICAL
                    message = f"Critical memory usage: {memory_percent:.1f}%"
                elif memory_percent > 80:
                    status = HealthStatus.WARNING
                    message = f"High memory usage: {memory_percent:.1f}%"
                else:
                    status = HealthStatus.HEALTHY
                    message = f"Memory usage normal: {memory_percent:.1f}%"
                
                return HealthCheck(
                    name="memory_usage",
                    status=status,
                    message=message,
                    details={"memory_percent": memory_percent}
                )
            except ImportError:
                return HealthCheck(
                    name="memory_usage",
                    status=HealthStatus.WARNING,
                    message="psutil not available for memory monitoring"
                )
            except Exception as e:
                return HealthCheck(
                    name="memory_usage",
                    status=HealthStatus.ERROR,
                    message=f"Memory check failed: {e}"
                )
        
        def error_rate_health() -> HealthCheck:
            """Check error rate health."""
            with self._lock:
                recent_errors = [e for e in self._error_events 
                               if e.timestamp > datetime.now() - timedelta(minutes=10)]
            
            error_count = len(recent_errors)
            critical_errors = len([e for e in recent_errors if e.severity == ErrorSeverity.CRITICAL])
            
            if critical_errors > 0:
                status = HealthStatus.CRITICAL
                message = f"{critical_errors} critical errors in last 10 minutes"
            elif error_count > 10:
                status = HealthStatus.WARNING
                message = f"High error rate: {error_count} errors in last 10 minutes"
            elif error_count > 0:
                status = HealthStatus.WARNING
                message = f"{error_count} errors in last 10 minutes"
            else:
                status = HealthStatus.HEALTHY
                message = "No recent errors"
            
            return HealthCheck(
                name="error_rate",
                status=status,
                message=message,
                details={"recent_errors": error_count, "critical_errors": critical_errors}
            )
        
        # Register the health checks
        self.register_health_check("hardware_config", hardware_config_health)
        self.register_health_check("memory_usage", memory_usage_health)
        self.register_health_check("error_rate", error_rate_health)


class HardwareErrorHandler:
    """Specialized error handler for hardware-related operations."""
    
    def __init__(self, monitor: Optional[HardwareSystemMonitor] = None):
        """Initialize the error handler.
        
        Args:
            monitor: Optional system monitor for error reporting
        """
        self.monitor = monitor
        self.logger = logging.getLogger(__name__)
        
        # Default fallback hardware specifications
        self._default_fallback_specs = {
            "fallback_gpu": {
                "name": "Fallback GPU",
                "memory_size_gb": 8,
                "memory_bandwidth_gbps": 500,
                "tensor_performance": {"fp16_tensor": 100},
                "vector_performance": {"fp32": 50}
            }
        }
    
    def handle_hardware_validation_error(self, hardware_id: str, 
                                       validation_result: ValidationResult) -> ValidationResult:
        """Handle hardware validation errors with fallback mechanisms.
        
        Args:
            hardware_id: Hardware identifier
            validation_result: Original validation result
            
        Returns:
            Enhanced validation result with fallback information
        """
        if validation_result.is_valid:
            return validation_result
        
        # Record the validation error
        if self.monitor:
            self.monitor.record_error(
                "hardware_validation",
                ErrorSeverity.MEDIUM,
                f"Hardware validation failed for {hardware_id}",
                context={"hardware_id": hardware_id, "errors": validation_result.errors}
            )
        
        # Add fallback recommendations
        validation_result.add_recommendation(
            f"Consider using fallback hardware specifications for {hardware_id}"
        )
        
        # Check if fallback is available
        if hardware_id in self._default_fallback_specs:
            validation_result.add_recommendation(
                f"Fallback specification available for {hardware_id}"
            )
        
        return validation_result
    
    def handle_config_loading_error(self, config_file: str, error: Exception) -> Dict[str, Any]:
        """Handle configuration loading errors with fallback.
        
        Args:
            config_file: Path to configuration file
            error: Exception that occurred
            
        Returns:
            Fallback configuration or empty config
        """
        if self.monitor:
            self.monitor.record_error(
                "config_loading",
                ErrorSeverity.HIGH,
                f"Failed to load configuration from {config_file}: {error}",
                error,
                context={"config_file": config_file}
            )
        
        self.logger.error(f"Configuration loading failed, using fallback: {error}")
        
        # Return minimal fallback configuration
        return {
            "gpus": self._default_fallback_specs,
            "npus": {}
        }
    
    def handle_timing_calculation_error(self, operator_name: str, hardware_id: str, 
                                      error: Exception) -> Dict[str, Any]:
        """Handle timing calculation errors with fallback values.
        
        Args:
            operator_name: Name of the operator
            hardware_id: Hardware identifier
            error: Exception that occurred
            
        Returns:
            Fallback timing results
        """
        if self.monitor:
            self.monitor.record_error(
                "timing_calculation",
                ErrorSeverity.MEDIUM,
                f"Timing calculation failed for {operator_name} on {hardware_id}: {error}",
                error,
                context={"operator": operator_name, "hardware": hardware_id}
            )
        
        self.logger.warning(f"Timing calculation failed, using fallback values: {error}")
        
        # Return fallback timing values
        return {
            "compute_time_ms": 1.0,  # Default 1ms
            "memory_time_ms": 1.0,   # Default 1ms
            "execution_time_ms": 1.0,
            "bottleneck_type": "unknown",
            "utilization_percent": 0.0,
            "fallback_used": True,
            "error_message": str(error)
        }
    
    def create_debug_context(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Create debug context for error reporting.
        
        Args:
            operation: Operation being performed
            **kwargs: Additional context information
            
        Returns:
            Debug context dictionary
        """
        context = {
            "operation": operation,
            "timestamp": datetime.now().isoformat(),
            "thread_id": threading.current_thread().ident
        }
        context.update(kwargs)
        return context
    
    def log_performance_warning(self, operation: str, duration_ms: float, 
                              threshold_ms: float = 1000.0):
        """Log performance warnings for slow operations.
        
        Args:
            operation: Operation name
            duration_ms: Operation duration in milliseconds
            threshold_ms: Warning threshold in milliseconds
        """
        if duration_ms > threshold_ms:
            if self.monitor:
                self.monitor.record_error(
                    "performance",
                    ErrorSeverity.LOW,
                    f"Slow operation: {operation} took {duration_ms:.1f}ms",
                    context={"operation": operation, "duration_ms": duration_ms}
                )
            
            self.logger.warning(f"Performance warning: {operation} took {duration_ms:.1f}ms")


# Global monitor instance
_global_monitor: Optional[HardwareSystemMonitor] = None


def get_system_monitor() -> HardwareSystemMonitor:
    """Get the global system monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = HardwareSystemMonitor()
        _global_monitor.start_monitoring()
    return _global_monitor


def setup_logging_integration():
    """Set up integration with Python logging system."""
    monitor = get_system_monitor()
    
    class MonitoringHandler(logging.Handler):
        """Logging handler that reports errors to the monitor."""
        
        def emit(self, record):
            if record.levelno >= logging.ERROR:
                severity = ErrorSeverity.HIGH if record.levelno >= logging.CRITICAL else ErrorSeverity.MEDIUM
                monitor.record_error(
                    record.name,
                    severity,
                    record.getMessage(),
                    record.exc_info[1] if record.exc_info else None,
                    context={"logger": record.name, "level": record.levelname}
                )
    
    # Add the monitoring handler to the root logger
    root_logger = logging.getLogger()
    monitoring_handler = MonitoringHandler()
    monitoring_handler.setLevel(logging.ERROR)
    root_logger.addHandler(monitoring_handler)