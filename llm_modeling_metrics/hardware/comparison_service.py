"""Hardware comparison service for multi-platform analysis."""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
import logging
import numpy as np

from .models import HardwareSpec, HardwareType, WorkloadProfile
from .service import HardwareService
from .timing_service import OperatorTimingService, OperatorTiming, TimingComparison
from .roofline_service import RooflineService, ComparisonPlotData
from ..core.operators import BaseOperator

logger = logging.getLogger(__name__)


@dataclass
class HardwareComparisonMetrics:
    """Metrics for comparing hardware platforms."""
    hardware_id: str
    hardware_name: str
    peak_performance_tflops: float
    memory_bandwidth_gbps: float
    memory_size_gb: int
    cost_performance_ratio: Optional[float] = None
    power_efficiency: Optional[float] = None  # TFLOPS/Watt
    memory_efficiency: Optional[float] = None  # GB/s per GB
    tensor_core_capable: bool = False
    supported_precisions: List[str] = field(default_factory=list)
    architecture_generation: Optional[str] = None


@dataclass
class WorkloadPerformanceAnalysis:
    """Performance analysis for a specific workload across hardware."""
    workload_name: str
    hardware_rankings: List[str]  # Hardware IDs ranked by performance
    performance_scores: Dict[str, float]  # hardware_id -> normalized score
    bottleneck_analysis: Dict[str, str]  # hardware_id -> bottleneck type
    optimization_opportunities: Dict[str, List[str]]  # hardware_id -> opportunities
    cost_effectiveness: Dict[str, float]  # hardware_id -> cost/performance ratio


@dataclass
class HardwareRecommendationEngine:
    """Recommendation engine results for hardware selection."""
    recommended_hardware: List[str]  # Ordered list of hardware IDs
    recommendation_reasons: Dict[str, List[str]]  # hardware_id -> reasons
    use_case_suitability: Dict[str, Dict[str, float]]  # hardware_id -> use_case -> score
    migration_recommendations: List[str]  # General migration advice
    cost_analysis: Optional[Dict[str, Any]] = None


@dataclass
class MultiHardwareComparison:
    """Complete multi-hardware comparison results."""
    hardware_platforms: List[str]
    comparison_metrics: Dict[str, HardwareComparisonMetrics]  # hardware_id -> metrics
    workload_analysis: Dict[str, WorkloadPerformanceAnalysis]  # workload -> analysis
    roofline_comparison: ComparisonPlotData
    timing_comparison: TimingComparison
    recommendation_engine: HardwareRecommendationEngine
    summary_insights: List[str]
    timestamp: str


class HardwareComparisonService:
    """Service for comprehensive multi-hardware comparison and analysis."""
    
    def __init__(self):
        """Initialize the comparison service."""
        self.hardware_service = HardwareService()
        self.timing_service = OperatorTimingService()
        self.roofline_service = RooflineService()
        
        # Cost data (in practice, this would come from a database or external service)
        self._hardware_costs = {
            "h100": 30000,  # USD
            "h20": 15000,
            "b200": 35000,
            "a100": 12000,
            "v100": 8000,
            "ascend_910b": 10000,
            "mi300x": 15000,
        }
    
    def compare_hardware_platforms(self, hardware_ids: List[str], 
                                 workload_profiles: List[WorkloadProfile] = None,
                                 operators: List[BaseOperator] = None,
                                 include_cost_analysis: bool = True) -> MultiHardwareComparison:
        """Perform comprehensive comparison across multiple hardware platforms.
        
        Args:
            hardware_ids: List of hardware IDs to compare
            workload_profiles: Optional workload profiles for analysis
            operators: Optional operators for detailed timing analysis
            include_cost_analysis: Whether to include cost-performance analysis
            
        Returns:
            MultiHardwareComparison with complete analysis results
        """
        try:
            # Get hardware specifications
            hardware_specs = []
            for hw_id in hardware_ids:
                spec = self.hardware_service.get_hardware_specs(hw_id)
                if spec is None:
                    logger.warning(f"Hardware {hw_id} not found, skipping")
                    continue
                hardware_specs.append(spec)
            
            if not hardware_specs:
                raise ValueError("No valid hardware specifications found")
            
            # Generate comparison metrics
            comparison_metrics = self._generate_comparison_metrics(
                hardware_specs, include_cost_analysis
            )
            
            # Perform workload analysis if profiles provided
            workload_analysis = {}
            if workload_profiles:
                for profile in workload_profiles:
                    analysis = self._analyze_workload_performance(
                        profile, hardware_specs, operators
                    )
                    workload_analysis[profile.model_type] = analysis
            
            # Generate roofline comparison
            roofline_comparison = self.roofline_service.compare_hardware_rooflines(
                hardware_specs, operators
            )
            
            # Generate timing comparison if operators provided
            timing_comparison = None
            if operators:
                timing_comparison = self.timing_service.compare_across_hardware(
                    operators, hardware_specs
                )
            
            # Generate recommendations
            recommendation_engine = self._generate_recommendations(
                hardware_specs, workload_analysis, comparison_metrics, include_cost_analysis
            )
            
            # Generate summary insights
            summary_insights = self._generate_summary_insights(
                comparison_metrics, workload_analysis, recommendation_engine
            )
            
            return MultiHardwareComparison(
                hardware_platforms=[spec.id for spec in hardware_specs],
                comparison_metrics=comparison_metrics,
                workload_analysis=workload_analysis,
                roofline_comparison=roofline_comparison,
                timing_comparison=timing_comparison,
                recommendation_engine=recommendation_engine,
                summary_insights=summary_insights,
                timestamp=str(np.datetime64('now'))
            )
            
        except Exception as e:
            logger.error(f"Error in hardware comparison: {e}")
            raise
    
    def create_hardware_selection_wizard(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Create hardware selection wizard based on user requirements.
        
        Args:
            requirements: Dictionary with user requirements (budget, use_case, etc.)
            
        Returns:
            Dictionary with wizard recommendations and guidance
        """
        try:
            # Extract requirements
            budget = requirements.get('budget')
            use_case = requirements.get('use_case', 'general')
            memory_requirement = requirements.get('memory_gb')
            precision_requirements = requirements.get('precisions', ['fp16'])
            performance_priority = requirements.get('performance_priority', 'balanced')
            
            # Get all available hardware
            all_hardware = self.hardware_service.get_available_hardware()
            all_specs = []
            for hw_type, hw_list in all_hardware.items():
                all_specs.extend(hw_list)
            
            # Filter by budget if specified
            if budget:
                filtered_specs = []
                for spec in all_specs:
                    cost = self._hardware_costs.get(spec.id.lower(), float('inf'))
                    if cost <= budget:
                        filtered_specs.append(spec)
                all_specs = filtered_specs
            
            # Filter by memory requirements
            if memory_requirement:
                all_specs = [spec for spec in all_specs if spec.memory_size_gb >= memory_requirement]
            
            # Filter by precision support
            all_specs = [
                spec for spec in all_specs 
                if any(spec.supports_precision(p) for p in precision_requirements)
            ]
            
            # Score hardware based on use case and priorities
            scored_hardware = []
            for spec in all_specs:
                score = self._calculate_wizard_score(
                    spec, use_case, performance_priority, precision_requirements
                )
                scored_hardware.append((spec, score))
            
            # Sort by score
            scored_hardware.sort(key=lambda x: x[1], reverse=True)
            
            # Generate recommendations
            recommendations = []
            for i, (spec, score) in enumerate(scored_hardware[:5]):  # Top 5
                cost = self._hardware_costs.get(spec.id.lower())
                rec = {
                    'rank': i + 1,
                    'hardware_id': spec.id,
                    'hardware_name': spec.name,
                    'score': score,
                    'cost': cost,
                    'reasons': self._generate_wizard_reasons(spec, use_case, performance_priority),
                    'pros': self._generate_pros(spec, use_case),
                    'cons': self._generate_cons(spec, use_case, requirements)
                }
                recommendations.append(rec)
            
            # Generate guidance
            guidance = self._generate_selection_guidance(
                requirements, recommendations, all_specs
            )
            
            return {
                'recommendations': recommendations,
                'guidance': guidance,
                'total_options': len(all_specs),
                'filtered_count': len(scored_hardware),
                'requirements_met': len(recommendations) > 0
            }
            
        except Exception as e:
            logger.error(f"Error in hardware selection wizard: {e}")
            raise
    
    def _generate_comparison_metrics(self, hardware_specs: List[HardwareSpec], 
                                   include_cost: bool) -> Dict[str, HardwareComparisonMetrics]:
        """Generate comparison metrics for hardware platforms."""
        metrics = {}
        
        for spec in hardware_specs:
            # Calculate peak performance (use best precision)
            peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
            
            # Calculate power efficiency
            power_efficiency = None
            if spec.tdp_watts and spec.tdp_watts > 0:
                power_efficiency = peak_perf / spec.tdp_watts
            
            # Calculate memory efficiency
            memory_efficiency = None
            if spec.memory_size_gb > 0:
                memory_efficiency = spec.memory_bandwidth_gbps / spec.memory_size_gb
            
            # Calculate cost-performance ratio
            cost_performance_ratio = None
            if include_cost:
                cost = self._hardware_costs.get(spec.id.lower())
                if cost and peak_perf > 0:
                    cost_performance_ratio = cost / peak_perf
            
            metrics[spec.id] = HardwareComparisonMetrics(
                hardware_id=spec.id,
                hardware_name=spec.name,
                peak_performance_tflops=peak_perf,
                memory_bandwidth_gbps=spec.memory_bandwidth_gbps,
                memory_size_gb=spec.memory_size_gb,
                cost_performance_ratio=cost_performance_ratio,
                power_efficiency=power_efficiency,
                memory_efficiency=memory_efficiency,
                tensor_core_capable=spec.is_tensor_core_capable(),
                supported_precisions=spec.supported_precisions,
                architecture_generation=spec.architecture
            )
        
        return metrics
    
    def _analyze_workload_performance(self, workload: WorkloadProfile, 
                                    hardware_specs: List[HardwareSpec],
                                    operators: List[BaseOperator] = None) -> WorkloadPerformanceAnalysis:
        """Analyze workload performance across hardware platforms."""
        performance_scores = {}
        bottleneck_analysis = {}
        optimization_opportunities = {}
        
        for spec in hardware_specs:
            # Calculate workload-specific performance score
            score = self._calculate_workload_score(workload, spec)
            performance_scores[spec.id] = score
            
            # Analyze bottlenecks
            if operators:
                bottleneck_analysis[spec.id] = self._analyze_workload_bottlenecks(
                    operators, spec
                )
                optimization_opportunities[spec.id] = self._identify_workload_optimizations(
                    workload, spec, operators
                )
            else:
                # General bottleneck analysis based on workload characteristics
                bottleneck_analysis[spec.id] = self._analyze_general_bottlenecks(workload, spec)
                optimization_opportunities[spec.id] = self._identify_general_optimizations(
                    workload, spec
                )
        
        # Rank hardware by performance
        ranked_hardware = sorted(
            performance_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        hardware_rankings = [hw_id for hw_id, _ in ranked_hardware]
        
        # Calculate cost effectiveness
        cost_effectiveness = {}
        for hw_id, score in performance_scores.items():
            cost = self._hardware_costs.get(hw_id.lower())
            if cost and score > 0:
                cost_effectiveness[hw_id] = score / cost
            else:
                cost_effectiveness[hw_id] = 0
        
        return WorkloadPerformanceAnalysis(
            workload_name=workload.model_type,
            hardware_rankings=hardware_rankings,
            performance_scores=performance_scores,
            bottleneck_analysis=bottleneck_analysis,
            optimization_opportunities=optimization_opportunities,
            cost_effectiveness=cost_effectiveness
        )
    
    def _generate_recommendations(self, hardware_specs: List[HardwareSpec],
                                workload_analysis: Dict[str, WorkloadPerformanceAnalysis],
                                comparison_metrics: Dict[str, HardwareComparisonMetrics],
                                include_cost: bool) -> HardwareRecommendationEngine:
        """Generate hardware recommendations based on analysis."""
        # Aggregate scores across workloads
        overall_scores = {}
        for spec in hardware_specs:
            score = 0
            count = 0
            
            # Performance score
            perf_score = comparison_metrics[spec.id].peak_performance_tflops
            score += perf_score
            count += 1
            
            # Workload-specific scores
            for workload_name, analysis in workload_analysis.items():
                if spec.id in analysis.performance_scores:
                    score += analysis.performance_scores[spec.id]
                    count += 1
            
            # Cost effectiveness
            if include_cost and comparison_metrics[spec.id].cost_performance_ratio:
                # Lower cost/performance ratio is better, so invert it
                cost_score = 1.0 / comparison_metrics[spec.id].cost_performance_ratio
                score += cost_score * 100  # Scale to match other scores
                count += 1
            
            overall_scores[spec.id] = score / count if count > 0 else 0
        
        # Rank hardware
        ranked_hardware = sorted(
            overall_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )
        recommended_hardware = [hw_id for hw_id, _ in ranked_hardware]
        
        # Generate recommendation reasons
        recommendation_reasons = {}
        for hw_id in recommended_hardware:
            reasons = []
            metrics = comparison_metrics[hw_id]
            
            if metrics.peak_performance_tflops > 100:
                reasons.append("High peak performance capability")
            if metrics.tensor_core_capable:
                reasons.append("Tensor core acceleration support")
            if metrics.memory_size_gb >= 80:
                reasons.append("Large memory capacity")
            if metrics.power_efficiency and metrics.power_efficiency > 1.0:
                reasons.append("Good power efficiency")
            if include_cost and metrics.cost_performance_ratio and metrics.cost_performance_ratio < 500:
                reasons.append("Good cost-performance ratio")
            
            recommendation_reasons[hw_id] = reasons
        
        # Generate use case suitability
        use_case_suitability = {}
        for spec in hardware_specs:
            suitability = {
                'training': self._calculate_training_suitability(spec),
                'inference': self._calculate_inference_suitability(spec),
                'research': self._calculate_research_suitability(spec),
                'production': self._calculate_production_suitability(spec)
            }
            use_case_suitability[spec.id] = suitability
        
        # Generate migration recommendations
        migration_recommendations = self._generate_migration_recommendations(
            hardware_specs, comparison_metrics
        )
        
        return HardwareRecommendationEngine(
            recommended_hardware=recommended_hardware,
            recommendation_reasons=recommendation_reasons,
            use_case_suitability=use_case_suitability,
            migration_recommendations=migration_recommendations
        )
    
    def _generate_summary_insights(self, comparison_metrics: Dict[str, HardwareComparisonMetrics],
                                 workload_analysis: Dict[str, WorkloadPerformanceAnalysis],
                                 recommendation_engine: HardwareRecommendationEngine) -> List[str]:
        """Generate summary insights from the comparison analysis."""
        insights = []
        
        # Performance insights
        best_perf_hw = max(
            comparison_metrics.items(),
            key=lambda x: x[1].peak_performance_tflops
        )
        insights.append(
            f"{best_perf_hw[1].hardware_name} offers the highest peak performance "
            f"at {best_perf_hw[1].peak_performance_tflops:.0f} TFLOPS"
        )
        
        # Memory insights
        best_memory_hw = max(
            comparison_metrics.items(),
            key=lambda x: x[1].memory_size_gb
        )
        insights.append(
            f"{best_memory_hw[1].hardware_name} provides the largest memory capacity "
            f"at {best_memory_hw[1].memory_size_gb} GB"
        )
        
        # Cost insights
        cost_effective_hw = []
        for hw_id, metrics in comparison_metrics.items():
            if metrics.cost_performance_ratio and metrics.cost_performance_ratio < 300:
                cost_effective_hw.append(metrics.hardware_name)
        
        if cost_effective_hw:
            insights.append(
                f"Most cost-effective options: {', '.join(cost_effective_hw[:3])}"
            )
        
        # Workload-specific insights
        for workload_name, analysis in workload_analysis.items():
            if analysis.hardware_rankings:
                best_hw_id = analysis.hardware_rankings[0]
                best_hw_name = comparison_metrics[best_hw_id].hardware_name
                insights.append(
                    f"For {workload_name} workloads, {best_hw_name} shows the best performance"
                )
        
        # Technology insights
        tensor_core_hw = [
            metrics.hardware_name for metrics in comparison_metrics.values()
            if metrics.tensor_core_capable
        ]
        if tensor_core_hw:
            insights.append(
                f"Tensor core acceleration available on: {', '.join(tensor_core_hw)}"
            )
        
        return insights
    
    def _calculate_workload_score(self, workload: WorkloadProfile, spec: HardwareSpec) -> float:
        """Calculate performance score for a workload on specific hardware."""
        score = 0.0
        
        # Memory compatibility (40% of score)
        if workload.memory_constraints:
            if spec.memory_size_gb >= workload.memory_constraints:
                memory_score = min(spec.memory_size_gb / workload.memory_constraints, 2.0)
                score += memory_score * 40
        else:
            # Default memory score based on capacity
            score += min(spec.memory_size_gb / 40, 2.0) * 40
        
        # Performance score (40% of score)
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        perf_score = min(peak_perf / 100, 2.0)  # Normalize to 100 TFLOPS
        score += perf_score * 40
        
        # Precision support (20% of score)
        if workload.precision_requirements:
            supported_count = sum(
                1 for p in workload.precision_requirements
                if spec.supports_precision(p)
            )
            precision_score = supported_count / len(workload.precision_requirements)
            score += precision_score * 20
        else:
            score += 20  # Full score if no specific requirements
        
        return score
    
    def _calculate_wizard_score(self, spec: HardwareSpec, use_case: str, 
                              performance_priority: str, precisions: List[str]) -> float:
        """Calculate wizard score for hardware selection."""
        score = 0.0
        
        # Base performance score
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        score += min(peak_perf / 100, 1.0) * 30
        
        # Memory score
        score += min(spec.memory_size_gb / 80, 1.0) * 20
        
        # Use case specific scoring
        if use_case == 'training':
            if spec.memory_size_gb >= 80:
                score += 20
            if spec.is_tensor_core_capable():
                score += 15
        elif use_case == 'inference':
            if spec.memory_bandwidth_gbps > 1000:
                score += 20
            score += 15  # Generally less demanding
        elif use_case == 'research':
            if spec.memory_size_gb >= 40:
                score += 15
            if len(spec.supported_precisions) >= 4:
                score += 10
        
        # Precision support
        supported_precisions = sum(1 for p in precisions if spec.supports_precision(p))
        score += (supported_precisions / len(precisions)) * 15
        
        return score
    
    def _analyze_workload_bottlenecks(self, operators: List[BaseOperator], 
                                    spec: HardwareSpec) -> str:
        """Analyze bottlenecks for operators on specific hardware."""
        compute_bound = 0
        memory_bound = 0
        
        for operator in operators:
            timing = self.timing_service.compute_operator_timing(operator, spec)
            if timing.bottleneck_type == 'compute':
                compute_bound += 1
            else:
                memory_bound += 1
        
        if compute_bound > memory_bound * 1.5:
            return 'compute'
        elif memory_bound > compute_bound * 1.5:
            return 'memory'
        else:
            return 'balanced'
    
    def _analyze_general_bottlenecks(self, workload: WorkloadProfile, spec: HardwareSpec) -> str:
        """Analyze general bottlenecks based on workload characteristics."""
        # Simple heuristic based on workload type and hardware characteristics
        if workload.model_type == 'moe':
            # MoE models are typically memory-bound due to expert routing
            return 'memory'
        elif workload.batch_size > 16:
            # Large batch sizes tend to be compute-bound
            return 'compute'
        else:
            # Default to balanced
            return 'balanced'
    
    def _identify_workload_optimizations(self, workload: WorkloadProfile, 
                                       spec: HardwareSpec, 
                                       operators: List[BaseOperator]) -> List[str]:
        """Identify optimization opportunities for workload on hardware."""
        optimizations = []
        
        # Check tensor core utilization
        if spec.is_tensor_core_capable():
            tensor_core_ops = sum(
                1 for op in operators 
                if hasattr(op, '_detect_tensor_core_utilization') and 
                op._detect_tensor_core_utilization(spec)
            )
            if tensor_core_ops < len(operators) * 0.5:
                optimizations.append("Enable tensor core utilization for more operators")
        
        # Check precision optimization
        if 'fp8' in spec.supported_precisions and 'fp8' not in workload.precision_requirements:
            optimizations.append("Consider FP8 precision for improved performance")
        
        # Check memory optimization
        if workload.memory_constraints and workload.memory_constraints > spec.memory_size_gb * 0.8:
            optimizations.append("Optimize memory usage - approaching hardware limits")
        
        return optimizations
    
    def _identify_general_optimizations(self, workload: WorkloadProfile, 
                                      spec: HardwareSpec) -> List[str]:
        """Identify general optimization opportunities."""
        optimizations = []
        
        if spec.is_tensor_core_capable() and 'fp16' not in workload.precision_requirements:
            optimizations.append("Use FP16 precision to leverage tensor cores")
        
        if workload.batch_size < 8 and spec.memory_size_gb > 40:
            optimizations.append("Increase batch size to improve hardware utilization")
        
        if 'fp8' in spec.supported_precisions:
            optimizations.append("Consider FP8 precision for maximum performance")
        
        return optimizations
    
    def _calculate_training_suitability(self, spec: HardwareSpec) -> float:
        """Calculate suitability score for training workloads."""
        score = 0.0
        
        # Memory is crucial for training
        if spec.memory_size_gb >= 80:
            score += 40
        elif spec.memory_size_gb >= 40:
            score += 25
        else:
            score += 10
        
        # High performance needed
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        if peak_perf >= 150:
            score += 30
        elif peak_perf >= 100:
            score += 20
        else:
            score += 10
        
        # Tensor cores help with training
        if spec.is_tensor_core_capable():
            score += 20
        
        # Mixed precision support
        if 'fp16' in spec.supported_precisions:
            score += 10
        
        return min(score, 100)
    
    def _calculate_inference_suitability(self, spec: HardwareSpec) -> float:
        """Calculate suitability score for inference workloads."""
        score = 0.0
        
        # Memory bandwidth important for inference
        if spec.memory_bandwidth_gbps >= 2000:
            score += 30
        elif spec.memory_bandwidth_gbps >= 1000:
            score += 20
        else:
            score += 10
        
        # Moderate memory requirements
        if spec.memory_size_gb >= 40:
            score += 25
        elif spec.memory_size_gb >= 20:
            score += 20
        else:
            score += 10
        
        # Performance still matters
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        if peak_perf >= 100:
            score += 25
        elif peak_perf >= 50:
            score += 20
        else:
            score += 10
        
        # Low precision support for efficiency
        if 'int8' in spec.supported_precisions:
            score += 10
        if 'fp8' in spec.supported_precisions:
            score += 10
        
        return min(score, 100)
    
    def _calculate_research_suitability(self, spec: HardwareSpec) -> float:
        """Calculate suitability score for research workloads."""
        score = 0.0
        
        # Flexibility in precision is important
        precision_count = len(spec.supported_precisions)
        score += min(precision_count * 5, 25)
        
        # Good memory capacity for experimentation
        if spec.memory_size_gb >= 40:
            score += 25
        elif spec.memory_size_gb >= 20:
            score += 15
        else:
            score += 5
        
        # Decent performance
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        if peak_perf >= 100:
            score += 25
        elif peak_perf >= 50:
            score += 20
        else:
            score += 10
        
        # Tensor cores useful for research
        if spec.is_tensor_core_capable():
            score += 15
        
        # Modern architecture preferred
        if spec.year and spec.year >= 2022:
            score += 10
        
        return min(score, 100)
    
    def _calculate_production_suitability(self, spec: HardwareSpec) -> float:
        """Calculate suitability score for production workloads."""
        score = 0.0
        
        # Reliability and efficiency important
        if spec.tdp_watts and spec.tdp_watts < 400:
            score += 20
        elif spec.tdp_watts and spec.tdp_watts < 600:
            score += 10
        
        # Good performance per watt
        if spec.tdp_watts:
            peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
            efficiency = peak_perf / spec.tdp_watts if spec.tdp_watts > 0 else 0
            if efficiency > 0.5:
                score += 25
            elif efficiency > 0.3:
                score += 15
            else:
                score += 5
        
        # Adequate memory
        if spec.memory_size_gb >= 40:
            score += 20
        elif spec.memory_size_gb >= 20:
            score += 15
        else:
            score += 5
        
        # Good memory bandwidth for throughput
        if spec.memory_bandwidth_gbps >= 1500:
            score += 20
        elif spec.memory_bandwidth_gbps >= 1000:
            score += 15
        else:
            score += 5
        
        # Inference-optimized precisions
        if 'int8' in spec.supported_precisions:
            score += 10
        
        return min(score, 100)
    
    def _generate_migration_recommendations(self, hardware_specs: List[HardwareSpec],
                                          comparison_metrics: Dict[str, HardwareComparisonMetrics]) -> List[str]:
        """Generate hardware migration recommendations."""
        recommendations = []
        
        # Find newest generation hardware
        newest_hw = max(hardware_specs, key=lambda x: x.year or 0)
        if newest_hw.year and newest_hw.year >= 2023:
            recommendations.append(
                f"Consider upgrading to {newest_hw.name} for latest architecture benefits"
            )
        
        # Find most efficient hardware
        efficient_hw = []
        for hw_id, metrics in comparison_metrics.items():
            if metrics.power_efficiency and metrics.power_efficiency > 0.5:
                efficient_hw.append(metrics.hardware_name)
        
        if efficient_hw:
            recommendations.append(
                f"For power efficiency, consider: {', '.join(efficient_hw[:2])}"
            )
        
        # Memory upgrade recommendations
        high_memory_hw = [
            metrics.hardware_name for metrics in comparison_metrics.values()
            if metrics.memory_size_gb >= 80
        ]
        if high_memory_hw:
            recommendations.append(
                f"For large model support, consider high-memory options: {', '.join(high_memory_hw[:2])}"
            )
        
        return recommendations
    
    def _generate_wizard_reasons(self, spec: HardwareSpec, use_case: str, 
                               performance_priority: str) -> List[str]:
        """Generate reasons for wizard recommendations."""
        reasons = []
        
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        
        if peak_perf > 150:
            reasons.append("Excellent compute performance")
        elif peak_perf > 100:
            reasons.append("Good compute performance")
        
        if spec.memory_size_gb >= 80:
            reasons.append("Large memory capacity")
        elif spec.memory_size_gb >= 40:
            reasons.append("Adequate memory capacity")
        
        if spec.is_tensor_core_capable():
            reasons.append("Tensor core acceleration")
        
        if len(spec.supported_precisions) >= 4:
            reasons.append("Flexible precision support")
        
        if use_case == 'training' and spec.memory_size_gb >= 80:
            reasons.append("Well-suited for training workloads")
        
        return reasons
    
    def _generate_pros(self, spec: HardwareSpec, use_case: str) -> List[str]:
        """Generate pros for hardware in wizard."""
        pros = []
        
        peak_perf = max(spec.peak_flops.values()) if spec.peak_flops else 0
        if peak_perf > 100:
            pros.append(f"High performance ({peak_perf:.0f} TFLOPS)")
        
        if spec.memory_size_gb >= 80:
            pros.append(f"Large memory ({spec.memory_size_gb} GB)")
        
        if spec.memory_bandwidth_gbps >= 2000:
            pros.append(f"High memory bandwidth ({spec.memory_bandwidth_gbps:.0f} GB/s)")
        
        if spec.is_tensor_core_capable():
            pros.append("Tensor core acceleration")
        
        if 'fp8' in spec.supported_precisions:
            pros.append("FP8 precision support")
        
        return pros
    
    def _generate_cons(self, spec: HardwareSpec, use_case: str, 
                      requirements: Dict[str, Any]) -> List[str]:
        """Generate cons for hardware in wizard."""
        cons = []
        
        # Check budget constraints
        budget = requirements.get('budget')
        if budget:
            cost = self._hardware_costs.get(spec.id.lower())
            if cost and cost > budget:
                cons.append(f"Exceeds budget (${cost:,} vs ${budget:,})")
        
        # Check power consumption
        if spec.tdp_watts and spec.tdp_watts > 500:
            cons.append(f"High power consumption ({spec.tdp_watts}W)")
        
        # Check memory limitations
        memory_req = requirements.get('memory_gb')
        if memory_req and spec.memory_size_gb < memory_req:
            cons.append(f"Insufficient memory ({spec.memory_size_gb} GB < {memory_req} GB)")
        
        # Check precision support
        precision_reqs = requirements.get('precisions', [])
        unsupported = [p for p in precision_reqs if not spec.supports_precision(p)]
        if unsupported:
            cons.append(f"Limited precision support (missing: {', '.join(unsupported)})")
        
        return cons
    
    def _generate_selection_guidance(self, requirements: Dict[str, Any],
                                   recommendations: List[Dict[str, Any]],
                                   all_specs: List[HardwareSpec]) -> Dict[str, Any]:
        """Generate selection guidance for the wizard."""
        guidance = {
            'summary': '',
            'key_considerations': [],
            'next_steps': [],
            'alternatives': []
        }
        
        if not recommendations:
            guidance['summary'] = "No hardware meets all specified requirements. Consider adjusting your criteria."
            guidance['alternatives'] = [
                "Increase budget if cost is a constraint",
                "Reduce memory requirements if possible",
                "Consider alternative precision requirements"
            ]
            return guidance
        
        # Generate summary
        top_rec = recommendations[0]
        guidance['summary'] = (
            f"Based on your requirements, {top_rec['hardware_name']} is the top recommendation "
            f"with a score of {top_rec['score']:.1f}/100."
        )
        
        # Key considerations
        use_case = requirements.get('use_case', 'general')
        if use_case == 'training':
            guidance['key_considerations'].append("Training requires high memory capacity and compute performance")
        elif use_case == 'inference':
            guidance['key_considerations'].append("Inference benefits from high memory bandwidth and efficiency")
        
        budget = requirements.get('budget')
        if budget:
            guidance['key_considerations'].append(f"Budget constraint of ${budget:,} limits available options")
        
        # Next steps
        guidance['next_steps'] = [
            "Review the top 3 recommendations in detail",
            "Consider total cost of ownership including power and cooling",
            "Validate performance with your specific workloads",
            "Check availability and lead times"
        ]
        
        return guidance