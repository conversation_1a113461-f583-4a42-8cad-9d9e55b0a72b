"""Roofline visualization service for generating roofline data and analysis."""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field
import logging

from .models import HardwareSpec, HardwareType
from ..core.operators import BaseOperator

logger = logging.getLogger(__name__)


@dataclass
class KneePoint:
    """Roofline knee point where compute and memory limits intersect."""
    operational_intensity: float  # FLOP/Byte
    performance_tflops: float
    precision: str
    hardware_id: str


@dataclass
class OperatorPoint:
    """Operator point on roofline plot."""
    operator_name: str
    operational_intensity: float
    achieved_performance_tflops: float
    utilization_percent: float
    is_compute_bound: bool
    precision: str
    hardware_id: str


@dataclass
class RooflineData:
    """Complete roofline data for visualization."""
    operational_intensity_range: np.ndarray
    performance_curves: Dict[str, Dict[str, np.ndarray]] = field(default_factory=dict)  # hardware_id -> precision -> performance
    knee_points: Dict[str, Dict[str, KneePoint]] = field(default_factory=dict)  # hardware_id -> precision -> knee_point
    operator_points: List[OperatorPoint] = field(default_factory=list)
    hardware_specs: Dict[str, HardwareSpec] = field(default_factory=dict)  # hardware_id -> spec


@dataclass
class RooflinePlotData:
    """Roofline plot data for single hardware with operators."""
    hardware_id: str
    hardware_name: str
    operational_intensity_range: np.ndarray
    roofline_curves: Dict[str, np.ndarray] = field(default_factory=dict)  # precision -> performance
    knee_points: Dict[str, KneePoint] = field(default_factory=dict)  # precision -> knee_point
    operator_points: List[OperatorPoint] = field(default_factory=list)


@dataclass
class ComparisonPlotData:
    """Roofline comparison data for multiple hardware platforms."""
    hardware_platforms: List[str]
    operational_intensity_range: np.ndarray
    roofline_data: Dict[str, RooflinePlotData] = field(default_factory=dict)  # hardware_id -> plot_data
    performance_rankings: Dict[str, List[str]] = field(default_factory=dict)  # operator -> ranked hardware list
    recommendations: List[str] = field(default_factory=list)


class RooflineService:
    """Service for generating roofline visualizations and analysis."""
    
    def __init__(self):
        """Initialize roofline service."""
        self._roofline_cache: Dict[str, RooflineData] = {}
        self._knee_point_cache: Dict[str, KneePoint] = {}
    
    def generate_roofline_data(self, hardware_specs: List[HardwareSpec], 
                              precisions: List[str] = None) -> RooflineData:
        """Generate roofline data for multiple hardware and precisions.
        
        Args:
            hardware_specs: List of hardware specifications
            precisions: List of precisions to include (default: common precisions)
            
        Returns:
            RooflineData with curves and knee points for all hardware/precision combinations
        """
        if precisions is None:
            precisions = ['fp32', 'fp16', 'bf16', 'fp8', 'int8']
        
        # Generate operational intensity range (FLOP/Byte)
        oi_range = np.logspace(-1, 3, 2000)  # 0.1 to 1000 FLOP/Byte
        
        roofline_data = RooflineData(
            operational_intensity_range=oi_range,
            hardware_specs={hw.id: hw for hw in hardware_specs}
        )
        
        # Generate curves for each hardware and precision
        for hardware in hardware_specs:
            roofline_data.performance_curves[hardware.id] = {}
            roofline_data.knee_points[hardware.id] = {}
            
            for precision in precisions:
                if hardware.supports_precision(precision):
                    try:
                        # Generate roofline curve
                        curve = self._generate_roofline_curve(hardware, precision, oi_range)
                        roofline_data.performance_curves[hardware.id][precision] = curve
                        
                        # Calculate knee point
                        knee_point = self.calculate_knee_points(hardware, precision)
                        if knee_point:
                            roofline_data.knee_points[hardware.id][precision] = knee_point
                            
                    except Exception as e:
                        logger.warning(f"Failed to generate roofline for {hardware.id} {precision}: {e}")
        
        return roofline_data
    
    def calculate_knee_points(self, hardware: HardwareSpec, precision: str) -> Optional[KneePoint]:
        """Calculate roofline knee point for specific hardware and precision.
        
        Args:
            hardware: Hardware specification
            precision: Precision type (e.g., 'fp16', 'bf16')
            
        Returns:
            KneePoint where compute and memory limits intersect, or None if cannot calculate
        """
        cache_key = f"{hardware.id}_{precision}"
        if cache_key in self._knee_point_cache:
            return self._knee_point_cache[cache_key]
        
        try:
            # Get peak performance for this precision
            peak_perf_tflops = hardware.get_peak_flops(precision)
            if peak_perf_tflops <= 0:
                return None
            
            # Convert to FLOPS per second
            peak_perf_flops = peak_perf_tflops * 1e12
            
            # Get memory bandwidth in bytes per second
            mem_bw_bps = hardware.get_memory_bandwidth_bps()
            if mem_bw_bps <= 0:
                return None
            
            # Calculate knee point operational intensity
            # Knee point is where: peak_performance = memory_bandwidth * operational_intensity
            knee_oi = peak_perf_flops / mem_bw_bps
            
            knee_point = KneePoint(
                operational_intensity=knee_oi,
                performance_tflops=peak_perf_tflops,
                precision=precision,
                hardware_id=hardware.id
            )
            
            # Cache the result
            self._knee_point_cache[cache_key] = knee_point
            return knee_point
            
        except Exception as e:
            logger.error(f"Failed to calculate knee point for {hardware.id} {precision}: {e}")
            return None
    
    def plot_operators_on_roofline(self, operators: List[BaseOperator], 
                                  hardware: HardwareSpec, **kwargs) -> RooflinePlotData:
        """Plot operators on roofline for specific hardware.
        
        Args:
            operators: List of operators to plot
            hardware: Hardware specification
            **kwargs: Additional parameters for operator computation
            
        Returns:
            RooflinePlotData with operators positioned on roofline
        """
        # Generate operational intensity range
        oi_range = np.logspace(-1, 3, 2000)
        
        # Get supported precisions for this hardware
        supported_precisions = [p for p in ['fp32', 'fp16', 'bf16', 'fp8', 'int8'] 
                               if hardware.supports_precision(p)]
        
        # Generate roofline curves
        roofline_curves = {}
        knee_points = {}
        
        for precision in supported_precisions:
            try:
                curve = self._generate_roofline_curve(hardware, precision, oi_range)
                roofline_curves[precision] = curve
                
                knee_point = self.calculate_knee_points(hardware, precision)
                if knee_point:
                    knee_points[precision] = knee_point
            except Exception as e:
                logger.warning(f"Failed to generate roofline curve for {precision}: {e}")
        
        # Calculate operator points
        operator_points = []
        for operator in operators:
            try:
                op_point = self._calculate_operator_point(operator, hardware, **kwargs)
                if op_point:
                    operator_points.append(op_point)
            except Exception as e:
                logger.warning(f"Failed to calculate operator point for {operator.name}: {e}")
        
        return RooflinePlotData(
            hardware_id=hardware.id,
            hardware_name=hardware.name,
            operational_intensity_range=oi_range,
            roofline_curves=roofline_curves,
            knee_points=knee_points,
            operator_points=operator_points
        )
    
    def compare_hardware_rooflines(self, hardware_list: List[HardwareSpec], 
                                  operators: List[BaseOperator] = None, 
                                  **kwargs) -> ComparisonPlotData:
        """Compare rooflines across multiple hardware platforms.
        
        Args:
            hardware_list: List of hardware specifications to compare
            operators: Optional list of operators to plot on all rooflines
            **kwargs: Additional parameters for operator computation
            
        Returns:
            ComparisonPlotData with multi-hardware roofline comparison
        """
        # Generate operational intensity range
        oi_range = np.logspace(-1, 3, 2000)
        
        roofline_data = {}
        performance_rankings = {}
        
        # Generate roofline data for each hardware
        for hardware in hardware_list:
            if operators:
                plot_data = self.plot_operators_on_roofline(operators, hardware, **kwargs)
            else:
                # Generate roofline without operators
                supported_precisions = [p for p in ['fp32', 'fp16', 'bf16', 'fp8', 'int8'] 
                                       if hardware.supports_precision(p)]
                
                roofline_curves = {}
                knee_points = {}
                
                for precision in supported_precisions:
                    try:
                        curve = self._generate_roofline_curve(hardware, precision, oi_range)
                        roofline_curves[precision] = curve
                        
                        knee_point = self.calculate_knee_points(hardware, precision)
                        if knee_point:
                            knee_points[precision] = knee_point
                    except Exception as e:
                        logger.warning(f"Failed to generate roofline curve for {precision}: {e}")
                
                plot_data = RooflinePlotData(
                    hardware_id=hardware.id,
                    hardware_name=hardware.name,
                    operational_intensity_range=oi_range,
                    roofline_curves=roofline_curves,
                    knee_points=knee_points,
                    operator_points=[]
                )
            
            roofline_data[hardware.id] = plot_data
        
        # Generate performance rankings if operators are provided
        if operators:
            performance_rankings = self._generate_performance_rankings(roofline_data, operators)
        
        # Generate recommendations
        recommendations = self._generate_comparison_recommendations(hardware_list, roofline_data)
        
        return ComparisonPlotData(
            hardware_platforms=[hw.id for hw in hardware_list],
            operational_intensity_range=oi_range,
            roofline_data=roofline_data,
            performance_rankings=performance_rankings,
            recommendations=recommendations
        )
    
    def _generate_roofline_curve(self, hardware: HardwareSpec, precision: str, 
                                oi_range: np.ndarray) -> np.ndarray:
        """Generate roofline performance curve for specific hardware and precision.
        
        Args:
            hardware: Hardware specification
            precision: Precision type
            oi_range: Operational intensity range
            
        Returns:
            Performance curve in TFLOPS
        """
        # Get peak performance for this precision
        peak_perf_tflops = hardware.get_peak_flops(precision)
        if peak_perf_tflops <= 0:
            raise ValueError(f"No performance data for {precision} on {hardware.id}")
        
        # Convert to FLOPS per second
        peak_perf_flops = peak_perf_tflops * 1e12
        
        # Get memory bandwidth in bytes per second
        mem_bw_bps = hardware.get_memory_bandwidth_bps()
        if mem_bw_bps <= 0:
            raise ValueError(f"No memory bandwidth data for {hardware.id}")
        
        # Calculate roofline: Performance = min(peak_perf, mem_bw * operational_intensity)
        memory_limited_perf = mem_bw_bps * oi_range  # FLOPS
        roofline_perf = np.minimum(peak_perf_flops, memory_limited_perf)
        
        # Convert back to TFLOPS
        return roofline_perf / 1e12
    
    def _calculate_operator_point(self, operator: BaseOperator, hardware: HardwareSpec, 
                                 **kwargs) -> Optional[OperatorPoint]:
        """Calculate operator point on roofline.
        
        Args:
            operator: Operator to analyze
            hardware: Hardware specification
            **kwargs: Additional parameters for operator computation
            
        Returns:
            OperatorPoint with position and characteristics
        """
        try:
            # Calculate basic metrics
            flops = operator.compute_flops(**kwargs)
            memory_bytes = operator.compute_memory_movement_bytes(**kwargs)
            
            if memory_bytes <= 0:
                return None
            
            # Calculate operational intensity
            operational_intensity = flops / memory_bytes
            
            # Estimate achieved performance using timing methods
            compute_time_ms = operator._estimate_compute_time(flops, hardware)
            memory_time_ms = operator._estimate_memory_time(memory_bytes, hardware)
            execution_time_ms = max(compute_time_ms, memory_time_ms)
            
            if execution_time_ms <= 0:
                return None
            
            # Calculate achieved performance in TFLOPS
            achieved_performance_tflops = (flops / (execution_time_ms / 1000)) / 1e12
            
            # Determine if compute or memory bound
            is_compute_bound = compute_time_ms > memory_time_ms
            
            # Calculate utilization
            precision = getattr(operator, 'activation_precision', 'fp32')
            peak_perf_tflops = hardware.get_peak_flops(precision)
            utilization_percent = (achieved_performance_tflops / peak_perf_tflops * 100) if peak_perf_tflops > 0 else 0
            
            return OperatorPoint(
                operator_name=operator.name,
                operational_intensity=operational_intensity,
                achieved_performance_tflops=achieved_performance_tflops,
                utilization_percent=min(utilization_percent, 100.0),
                is_compute_bound=is_compute_bound,
                precision=precision,
                hardware_id=hardware.id
            )
            
        except Exception as e:
            logger.error(f"Failed to calculate operator point for {operator.name}: {e}")
            return None
    
    def _generate_performance_rankings(self, roofline_data: Dict[str, RooflinePlotData], 
                                     operators: List[BaseOperator]) -> Dict[str, List[str]]:
        """Generate performance rankings for operators across hardware."""
        rankings = {}
        
        for operator in operators:
            operator_performances = []
            
            # Collect performance data for this operator across all hardware
            for hw_id, plot_data in roofline_data.items():
                for op_point in plot_data.operator_points:
                    if op_point.operator_name == operator.name:
                        operator_performances.append((hw_id, op_point.achieved_performance_tflops))
                        break
            
            # Sort by performance (descending)
            operator_performances.sort(key=lambda x: x[1], reverse=True)
            rankings[operator.name] = [hw_id for hw_id, _ in operator_performances]
        
        return rankings
    
    def _generate_comparison_recommendations(self, hardware_list: List[HardwareSpec], 
                                           roofline_data: Dict[str, RooflinePlotData]) -> List[str]:
        """Generate recommendations based on hardware comparison."""
        recommendations = []
        
        # Find hardware with best peak performance
        best_peak_perf = 0
        best_peak_hw = None
        
        for hardware in hardware_list:
            max_perf = max(hardware.peak_flops.values()) if hardware.peak_flops else 0
            if max_perf > best_peak_perf:
                best_peak_perf = max_perf
                best_peak_hw = hardware
        
        if best_peak_hw:
            recommendations.append(f"{best_peak_hw.name} has the highest peak performance ({best_peak_perf:.0f} TFLOPS)")
        
        # Find hardware with best memory bandwidth
        best_mem_bw = 0
        best_mem_hw = None
        
        for hardware in hardware_list:
            if hardware.memory_bandwidth_gbps > best_mem_bw:
                best_mem_bw = hardware.memory_bandwidth_gbps
                best_mem_hw = hardware
        
        if best_mem_hw:
            recommendations.append(f"{best_mem_hw.name} has the highest memory bandwidth ({best_mem_bw:.0f} GB/s)")
        
        # Analyze knee points to identify compute vs memory characteristics
        compute_focused_hw = []
        memory_focused_hw = []
        
        for hw_id, plot_data in roofline_data.items():
            avg_knee_oi = 0
            knee_count = 0
            
            for precision, knee_point in plot_data.knee_points.items():
                avg_knee_oi += knee_point.operational_intensity
                knee_count += 1
            
            if knee_count > 0:
                avg_knee_oi /= knee_count
                
                if avg_knee_oi > 10:  # High operational intensity knee point
                    compute_focused_hw.append(plot_data.hardware_name)
                elif avg_knee_oi < 1:  # Low operational intensity knee point
                    memory_focused_hw.append(plot_data.hardware_name)
        
        if compute_focused_hw:
            recommendations.append(f"Best for compute-intensive workloads: {', '.join(compute_focused_hw)}")
        
        if memory_focused_hw:
            recommendations.append(f"Best for memory-intensive workloads: {', '.join(memory_focused_hw)}")
        
        # Precision support recommendations
        fp8_support = [hw.name for hw in hardware_list if hw.supports_precision('fp8')]
        if fp8_support:
            recommendations.append(f"FP8 precision support available on: {', '.join(fp8_support)}")
        
        tensor_core_hw = [hw.name for hw in hardware_list if hw.is_tensor_core_capable()]
        if tensor_core_hw:
            recommendations.append(f"Tensor core acceleration available on: {', '.join(tensor_core_hw)}")
        
        return recommendations
    
    def clear_cache(self):
        """Clear roofline and knee point caches."""
        self._roofline_cache.clear()
        self._knee_point_cache.clear()