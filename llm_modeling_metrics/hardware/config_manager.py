"""Hardware configuration management system."""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import shutil
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from .models import HardwareSpec, HardwareType, ValidationResult
from .validation import HardwareConfigValidator
from .adapter import HardwareAdapter

logger = logging.getLogger(__name__)


class HardwareConfigWatcher(FileSystemEventHandler):
    """File system watcher for hardware configuration changes."""
    
    def __init__(self, config_manager):
        """Initialize the watcher with a reference to the config manager."""
        self.config_manager = config_manager
        self.last_modified = {}
    
    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return
        
        file_path = event.src_path
        if not file_path.endswith(('.yaml', '.yml', '.json')):
            return
        
        # Debounce rapid file changes
        current_time = datetime.now().timestamp()
        if file_path in self.last_modified:
            if current_time - self.last_modified[file_path] < 1.0:  # 1 second debounce
                return
        
        self.last_modified[file_path] = current_time
        
        logger.info(f"Hardware configuration file changed: {file_path}")
        try:
            self.config_manager.reload_configuration(file_path)
        except Exception as e:
            logger.error(f"Failed to reload configuration after file change: {e}")


class HardwareConfigManager:
    """Advanced hardware configuration management system."""
    
    def __init__(self, 
                 primary_config_file: Optional[str] = None,
                 custom_config_dir: Optional[str] = None,
                 enable_auto_reload: bool = True):
        """Initialize the configuration manager.
        
        Args:
            primary_config_file: Path to primary hardware specs file (gpu_specs.yaml)
            custom_config_dir: Directory for custom hardware profiles
            enable_auto_reload: Whether to enable automatic configuration reloading
        """
        self.primary_config_file = primary_config_file or self._find_primary_config()
        self.custom_config_dir = Path(custom_config_dir or self._get_default_custom_dir())
        self.enable_auto_reload = enable_auto_reload
        
        # Ensure custom config directory exists
        self.custom_config_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.validator = HardwareConfigValidator()
        self.adapter = None
        self._merged_config = {}
        self._config_sources = {}
        self._file_watcher = None
        self._observer = None
        
        # Load initial configuration
        self._load_all_configurations()
        
        # Start file watching if enabled
        if self.enable_auto_reload:
            self._start_file_watching()
    
    def _find_primary_config(self) -> str:
        """Find the primary GPU specifications file."""
        possible_paths = [
            "gpu/data/gpu_specs.yaml",
            "../gpu/data/gpu_specs.yaml",
            "../../gpu/data/gpu_specs.yaml",
            Path(__file__).parent.parent.parent / "gpu" / "data" / "gpu_specs.yaml"
        ]
        
        for path in possible_paths:
            if isinstance(path, Path):
                if path.exists():
                    return str(path)
            elif os.path.exists(path):
                return path
        
        raise FileNotFoundError("Could not find primary gpu_specs.yaml file")
    
    def _get_default_custom_dir(self) -> str:
        """Get default directory for custom hardware profiles."""
        return str(Path.home() / ".llm_modeling_metrics" / "hardware_profiles")
    
    def _load_all_configurations(self):
        """Load and merge all hardware configurations."""
        self._merged_config = {"gpus": {}, "npus": {}}
        self._config_sources = {}
        
        # Load primary configuration
        try:
            primary_config = self._load_config_file(self.primary_config_file)
            self._merge_config(primary_config, "primary")
            logger.info(f"Loaded primary configuration from {self.primary_config_file}")
        except Exception as e:
            logger.error(f"Failed to load primary configuration: {e}")
            raise
        
        # Load custom configurations
        self._load_custom_configurations()
        
        # Create adapter with merged configuration
        self.adapter = HardwareAdapter()
        self.adapter._raw_specs = self._merged_config
        self.adapter._hardware_cache.clear()
    
    def _load_custom_configurations(self):
        """Load all custom hardware configuration files."""
        if not self.custom_config_dir.exists():
            return
        
        for config_file in self.custom_config_dir.glob("*.yaml"):
            try:
                custom_config = self._load_config_file(str(config_file))
                self._merge_config(custom_config, f"custom:{config_file.name}")
                logger.info(f"Loaded custom configuration from {config_file}")
            except Exception as e:
                logger.warning(f"Failed to load custom configuration {config_file}: {e}")
        
        for config_file in self.custom_config_dir.glob("*.json"):
            try:
                custom_config = self._load_config_file(str(config_file))
                self._merge_config(custom_config, f"custom:{config_file.name}")
                logger.info(f"Loaded custom configuration from {config_file}")
            except Exception as e:
                logger.warning(f"Failed to load custom configuration {config_file}: {e}")
    
    def _load_config_file(self, file_path: str) -> Dict[str, Any]:
        """Load configuration from a file."""
        with open(file_path, 'r') as f:
            if file_path.endswith('.json'):
                return json.load(f)
            else:
                return yaml.safe_load(f)
    
    def _merge_config(self, config: Dict[str, Any], source: str):
        """Merge configuration into the main config, tracking sources."""
        for section in ["gpus", "npus"]:
            if section in config:
                for hardware_id, hardware_data in config[section].items():
                    if hardware_id in self._merged_config[section]:
                        logger.warning(f"Hardware {hardware_id} redefined in {source}, overriding previous definition")
                    
                    self._merged_config[section][hardware_id] = hardware_data
                    self._config_sources[hardware_id] = source
    
    def _start_file_watching(self):
        """Start watching configuration files for changes."""
        try:
            self._file_watcher = HardwareConfigWatcher(self)
            self._observer = Observer()
            
            # Watch primary config file directory
            primary_dir = Path(self.primary_config_file).parent
            self._observer.schedule(self._file_watcher, str(primary_dir), recursive=False)
            
            # Watch custom config directory
            self._observer.schedule(self._file_watcher, str(self.custom_config_dir), recursive=False)
            
            self._observer.start()
            logger.info("Started hardware configuration file watching")
        except Exception as e:
            logger.warning(f"Failed to start file watching: {e}")
    
    def stop_file_watching(self):
        """Stop watching configuration files."""
        if self._observer:
            self._observer.stop()
            self._observer.join()
            self._observer = None
            self._file_watcher = None
            logger.info("Stopped hardware configuration file watching")
    
    def reload_configuration(self, changed_file: Optional[str] = None):
        """Reload hardware configurations.
        
        Args:
            changed_file: Specific file that changed (for targeted reloading)
        """
        logger.info(f"Reloading hardware configuration{f' due to change in {changed_file}' if changed_file else ''}")
        
        try:
            self._load_all_configurations()
            logger.info("Hardware configuration reloaded successfully")
        except Exception as e:
            logger.error(f"Failed to reload hardware configuration: {e}")
            raise
    
    def get_available_hardware(self) -> Dict[str, List[HardwareSpec]]:
        """Get all available hardware with source information."""
        if not self.adapter:
            return {"gpu": [], "npu": []}
        
        hardware_dict = self.adapter.get_available_hardware()
        
        # Add source information to each hardware spec
        for hardware_type, hardware_list in hardware_dict.items():
            for hardware in hardware_list:
                hardware.config_source = self._config_sources.get(hardware.id, "unknown")
        
        return hardware_dict
    
    def get_hardware_specs(self, hardware_id: str) -> Optional[HardwareSpec]:
        """Get hardware specifications with source information."""
        if not self.adapter:
            return None
        
        spec = self.adapter.get_hardware_specs(hardware_id)
        if spec:
            spec.config_source = self._config_sources.get(hardware_id, "unknown")
        
        return spec
    
    def validate_all_configurations(self) -> ValidationResult:
        """Validate all loaded hardware configurations."""
        return self.validator.validate_hardware_config_data(self._merged_config)
    
    def validate_custom_profile(self, profile_data: Dict[str, Any]) -> ValidationResult:
        """Validate a custom hardware profile before saving."""
        return self.validator.validate_hardware_config_data(profile_data)
    
    def save_custom_profile(self, profile_name: str, profile_data: Dict[str, Any], 
                          format: str = "yaml") -> ValidationResult:
        """Save a custom hardware profile.
        
        Args:
            profile_name: Name for the profile file
            profile_data: Hardware configuration data
            format: File format ('yaml' or 'json')
            
        Returns:
            ValidationResult indicating success/failure
        """
        result = ValidationResult(is_valid=True)
        
        # Validate profile data first
        validation_result = self.validate_custom_profile(profile_data)
        if not validation_result.is_valid:
            return validation_result
        
        # Determine file path
        extension = "yaml" if format == "yaml" else "json"
        file_path = self.custom_config_dir / f"{profile_name}.{extension}"
        
        try:
            # Create backup if file exists
            if file_path.exists():
                backup_path = file_path.with_suffix(f".{extension}.backup")
                shutil.copy2(file_path, backup_path)
                result.add_recommendation(f"Created backup at {backup_path}")
            
            # Save the profile
            with open(file_path, 'w') as f:
                if format == "yaml":
                    yaml.dump(profile_data, f, default_flow_style=False, indent=2)
                else:
                    json.dump(profile_data, f, indent=2)
            
            result.add_recommendation(f"Custom profile saved to {file_path}")
            
            # Reload configurations to include the new profile
            if not self.enable_auto_reload:
                self.reload_configuration(str(file_path))
            
        except Exception as e:
            result.add_error(f"Failed to save custom profile: {e}")
        
        return result
    
    def delete_custom_profile(self, profile_name: str) -> ValidationResult:
        """Delete a custom hardware profile.
        
        Args:
            profile_name: Name of the profile to delete
            
        Returns:
            ValidationResult indicating success/failure
        """
        result = ValidationResult(is_valid=True)
        
        # Find the profile file
        yaml_path = self.custom_config_dir / f"{profile_name}.yaml"
        json_path = self.custom_config_dir / f"{profile_name}.json"
        
        file_path = None
        if yaml_path.exists():
            file_path = yaml_path
        elif json_path.exists():
            file_path = json_path
        
        if not file_path:
            result.add_error(f"Custom profile '{profile_name}' not found")
            return result
        
        try:
            # Create backup before deletion
            backup_path = file_path.with_suffix(f"{file_path.suffix}.deleted")
            shutil.move(file_path, backup_path)
            
            result.add_recommendation(f"Profile deleted, backup created at {backup_path}")
            
            # Reload configurations
            if not self.enable_auto_reload:
                self.reload_configuration()
            
        except Exception as e:
            result.add_error(f"Failed to delete custom profile: {e}")
        
        return result
    
    def list_custom_profiles(self) -> List[Dict[str, Any]]:
        """List all custom hardware profiles.
        
        Returns:
            List of profile information dictionaries
        """
        profiles = []
        
        for config_file in self.custom_config_dir.glob("*.yaml"):
            profiles.append({
                "name": config_file.stem,
                "format": "yaml",
                "path": str(config_file),
                "modified": datetime.fromtimestamp(config_file.stat().st_mtime).isoformat(),
                "size": config_file.stat().st_size
            })
        
        for config_file in self.custom_config_dir.glob("*.json"):
            profiles.append({
                "name": config_file.stem,
                "format": "json", 
                "path": str(config_file),
                "modified": datetime.fromtimestamp(config_file.stat().st_mtime).isoformat(),
                "size": config_file.stat().st_size
            })
        
        return sorted(profiles, key=lambda x: x["modified"], reverse=True)
    
    def export_hardware_profile(self, hardware_ids: List[str], 
                              profile_name: str, format: str = "yaml") -> ValidationResult:
        """Export selected hardware specifications to a custom profile.
        
        Args:
            hardware_ids: List of hardware IDs to export
            profile_name: Name for the exported profile
            format: Export format ('yaml' or 'json')
            
        Returns:
            ValidationResult indicating success/failure
        """
        result = ValidationResult(is_valid=True)
        
        profile_data = {"gpus": {}, "npus": {}}
        
        for hardware_id in hardware_ids:
            if hardware_id in self._merged_config["gpus"]:
                profile_data["gpus"][hardware_id] = self._merged_config["gpus"][hardware_id]
            elif hardware_id in self._merged_config["npus"]:
                profile_data["npus"][hardware_id] = self._merged_config["npus"][hardware_id]
            else:
                result.add_warning(f"Hardware {hardware_id} not found, skipping")
        
        if not profile_data["gpus"] and not profile_data["npus"]:
            result.add_error("No valid hardware found to export")
            return result
        
        return self.save_custom_profile(profile_name, profile_data, format)
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration state.
        
        Returns:
            Dictionary with configuration summary information
        """
        summary = {
            "primary_config_file": self.primary_config_file,
            "custom_config_dir": str(self.custom_config_dir),
            "auto_reload_enabled": self.enable_auto_reload,
            "total_hardware": {
                "gpus": len(self._merged_config.get("gpus", {})),
                "npus": len(self._merged_config.get("npus", {}))
            },
            "config_sources": {},
            "custom_profiles": len(self.list_custom_profiles()),
            "last_reload": datetime.now().isoformat()
        }
        
        # Count hardware by source
        for hardware_id, source in self._config_sources.items():
            if source not in summary["config_sources"]:
                summary["config_sources"][source] = 0
            summary["config_sources"][source] += 1
        
        return summary
    
    def __del__(self):
        """Cleanup when the manager is destroyed."""
        self.stop_file_watching()