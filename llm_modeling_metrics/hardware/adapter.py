"""Hardware adapter to wrap existing GPU module classes."""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Union

from .models import HardwareSpec, HardwareType, ValidationResult
from ..core.operators import BaseOperator

# Import the existing GPU module classes
import sys
gpu_path = Path(__file__).parent.parent.parent / "gpu"
sys.path.insert(0, str(gpu_path))

try:
    from hardware import GPU, NPU, Dtype, load_gpu_specs, create_gpu_from_spec, create_npu_from_spec
except ImportError:
    # Fallback if import fails
    GPU = None
    NPU = None
    Dtype = None
    load_gpu_specs = None
    create_gpu_from_spec = None
    create_npu_from_spec = None


class HardwareAdapter:
    """Adapter to wrap existing GPU module functionality for web interface."""
    
    def __init__(self, specs_file: Optional[str] = None):
        """Initialize hardware adapter with specifications file."""
        self.specs_file = specs_file or self._find_specs_file()
        self._raw_specs = None
        self._hardware_cache: Dict[str, HardwareSpec] = {}
        self._load_specifications()
    
    def _find_specs_file(self) -> str:
        """Find the GPU specifications YAML file."""
        # Try multiple possible locations
        possible_paths = [
            "gpu/data/gpu_specs.yaml",
            "../gpu/data/gpu_specs.yaml", 
            "../../gpu/data/gpu_specs.yaml",
            Path(__file__).parent.parent.parent / "gpu" / "data" / "gpu_specs.yaml"
        ]
        
        for path in possible_paths:
            if isinstance(path, Path):
                if path.exists():
                    return str(path)
            elif os.path.exists(path):
                return path
        
        raise FileNotFoundError("Could not find gpu_specs.yaml file")
    
    def _load_specifications(self):
        """Load hardware specifications from YAML file."""
        try:
            with open(self.specs_file, 'r') as f:
                self._raw_specs = yaml.safe_load(f)
        except Exception as e:
            raise RuntimeError(f"Failed to load hardware specifications: {e}")
    
    def get_available_hardware(self) -> Dict[str, List[HardwareSpec]]:
        """Get all available hardware grouped by type."""
        if not self._raw_specs:
            return {"gpu": [], "npu": []}
        
        result = {"gpu": [], "npu": []}
        
        # Load GPUs
        if "gpus" in self._raw_specs:
            for gpu_id, gpu_data in self._raw_specs["gpus"].items():
                try:
                    hardware_spec = self._convert_gpu_spec(gpu_id, gpu_data)
                    result["gpu"].append(hardware_spec)
                    self._hardware_cache[gpu_id] = hardware_spec
                except Exception as e:
                    print(f"Warning: Failed to load GPU {gpu_id}: {e}")
        
        # Load NPUs
        if "npus" in self._raw_specs:
            for npu_id, npu_data in self._raw_specs["npus"].items():
                try:
                    hardware_spec = self._convert_npu_spec(npu_id, npu_data)
                    result["npu"].append(hardware_spec)
                    self._hardware_cache[npu_id] = hardware_spec
                except Exception as e:
                    print(f"Warning: Failed to load NPU {npu_id}: {e}")
        
        return result
    
    def get_hardware_specs(self, hardware_id: str) -> Optional[HardwareSpec]:
        """Get specifications for a specific hardware device."""
        if hardware_id in self._hardware_cache:
            return self._hardware_cache[hardware_id]
        
        # Try to load if not cached
        if not self._raw_specs:
            return None
        
        # Check GPUs
        if "gpus" in self._raw_specs and hardware_id in self._raw_specs["gpus"]:
            try:
                spec = self._convert_gpu_spec(hardware_id, self._raw_specs["gpus"][hardware_id])
                self._hardware_cache[hardware_id] = spec
                return spec
            except Exception:
                return None
        
        # Check NPUs
        if "npus" in self._raw_specs and hardware_id in self._raw_specs["npus"]:
            try:
                spec = self._convert_npu_spec(hardware_id, self._raw_specs["npus"][hardware_id])
                self._hardware_cache[hardware_id] = spec
                return spec
            except Exception:
                return None
        
        return None
    
    def _convert_gpu_spec(self, gpu_id: str, gpu_data: dict) -> HardwareSpec:
        """Convert raw GPU specification to HardwareSpec."""
        return HardwareSpec(
            id=gpu_id,
            name=gpu_data.get("name", gpu_id),
            type=HardwareType.GPU,
            architecture=gpu_data.get("architecture"),
            form_factor=gpu_data.get("form_factor"),
            year=gpu_data.get("year"),
            memory_size_gb=gpu_data.get("memory_size_gb", 0),
            memory_type=gpu_data.get("memory_type"),
            memory_bandwidth_gbps=gpu_data.get("memory_bandwidth_gbps", 0),
            l2_cache_mb=gpu_data.get("l2_cache_mb"),
            tensor_performance=gpu_data.get("tensor_performance", {}),
            vector_performance=gpu_data.get("vector_performance", {}),
            tdp_watts=gpu_data.get("tdp_watts"),
            manufacturing_process=gpu_data.get("manufacturing_process"),
            interconnect=gpu_data.get("interconnect"),
            tensor_cores=gpu_data.get("tensor_cores_total")
        )
    
    def _convert_npu_spec(self, npu_id: str, npu_data: dict) -> HardwareSpec:
        """Convert raw NPU specification to HardwareSpec."""
        return HardwareSpec(
            id=npu_id,
            name=npu_data.get("name", npu_id),
            type=HardwareType.NPU,
            architecture=npu_data.get("architecture"),
            form_factor=npu_data.get("form_factor"),
            year=npu_data.get("year"),
            memory_size_gb=npu_data.get("memory_size_gb", 0),
            memory_type=npu_data.get("memory_type"),
            memory_bandwidth_gbps=npu_data.get("memory_bandwidth_gbps", 0),
            l2_cache_mb=npu_data.get("l2_cache_mb"),
            tensor_performance=npu_data.get("tensor_performance", {}),
            vector_performance=npu_data.get("vector_performance", {}),
            tdp_watts=npu_data.get("tdp_watts"),
            manufacturing_process=npu_data.get("manufacturing_process"),
            interconnect=npu_data.get("interconnect")
        )
    
    def validate_hardware_compatibility(self, hardware_id: str, operators: List[BaseOperator]) -> ValidationResult:
        """Validate hardware compatibility with given operators."""
        result = ValidationResult(is_valid=True)
        
        hardware = self.get_hardware_specs(hardware_id)
        if not hardware:
            result.add_error(f"Hardware {hardware_id} not found")
            return result
        
        # Validate memory requirements
        total_memory_gb = self._estimate_total_memory_usage(operators)
        if total_memory_gb > hardware.memory_size_gb:
            result.add_error(
                f"Estimated memory usage ({total_memory_gb:.1f}GB) exceeds "
                f"hardware memory ({hardware.memory_size_gb}GB)"
            )
        elif total_memory_gb > hardware.memory_size_gb * 0.8:
            result.add_warning(
                f"High memory usage ({total_memory_gb:.1f}GB) may cause performance issues"
            )
        
        # Validate precision support
        required_precisions = self._get_required_precisions(operators)
        for precision in required_precisions:
            if not hardware.supports_precision(precision):
                result.add_warning(f"Hardware may not optimally support {precision} precision")
        
        # Add recommendations
        if hardware.is_tensor_core_capable():
            result.add_recommendation("Consider using tensor core optimized precisions (fp16, bf16)")
        
        if hardware.type == HardwareType.GPU and any(op.operator_type == "attention" for op in operators):
            result.add_recommendation("GPU is well-suited for attention operations")
        
        return result
    
    def _estimate_total_memory_usage(self, operators: List[BaseOperator]) -> float:
        """Estimate total memory usage for operators in GB."""
        total_bytes = 0
        for operator in operators:
            # Basic estimation - this would be enhanced with actual operator memory calculations
            if hasattr(operator, 'memory_usage_bytes'):
                total_bytes += operator.memory_usage_bytes
            else:
                # Rough estimation based on parameters
                total_bytes += getattr(operator, 'parameters', 0) * 4  # 4 bytes per fp32 parameter
        
        return total_bytes / (1024**3)  # Convert to GB
    
    def _get_required_precisions(self, operators: List[BaseOperator]) -> List[str]:
        """Get list of precisions required by operators."""
        precisions = set()
        for operator in operators:
            if hasattr(operator, 'precision'):
                precisions.add(operator.precision)
            else:
                precisions.add('fp32')  # Default assumption
        return list(precisions)
    
    def create_legacy_gpu_instance(self, hardware_id: str) -> Optional[Union[GPU, NPU]]:
        """Create legacy GPU/NPU instance for backward compatibility."""
        if not self._raw_specs:
            return None
        
        # Try GPU first
        if "gpus" in self._raw_specs and hardware_id in self._raw_specs["gpus"]:
            if create_gpu_from_spec:
                return create_gpu_from_spec(hardware_id, self._raw_specs)
        
        # Try NPU
        if "npus" in self._raw_specs and hardware_id in self._raw_specs["npus"]:
            if create_npu_from_spec:
                return create_npu_from_spec(hardware_id, self._raw_specs)
        
        return None
    
    def reload_specifications(self):
        """Reload hardware specifications from file."""
        self._hardware_cache.clear()
        self._load_specifications()
    
    def get_hardware_by_type(self, hardware_type: HardwareType) -> List[HardwareSpec]:
        """Get all hardware of a specific type."""
        all_hardware = self.get_available_hardware()
        return all_hardware.get(hardware_type.value, [])