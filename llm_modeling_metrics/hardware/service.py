"""Hardware service for managing hardware specifications and operations."""

from typing import Dict, List, Optional, Any
import logging
import time

from .adapter import HardwareAdapter
from .models import (
    HardwareSpec, HardwareType, ValidationResult, 
    HardwareRecommendation, WorkloadProfile
)
from .config_manager import HardwareConfigManager
from .monitoring import HardwareError<PERSON><PERSON><PERSON>, ErrorSeverity, get_system_monitor
from ..core.operators import BaseOperator

logger = logging.getLogger(__name__)


class HardwareService:
    """Service for managing hardware specifications and selection."""
    
    def __init__(self, specs_file: Optional[str] = None, 
                 enable_monitoring: bool = True,
                 enable_auto_reload: bool = True):
        """Initialize hardware service.
        
        Args:
            specs_file: Optional path to hardware specifications file
            enable_monitoring: Whether to enable system monitoring
            enable_auto_reload: Whether to enable automatic configuration reloading
        """
        # Initialize configuration manager
        try:
            self.config_manager = HardwareConfigManager(
                primary_config_file=specs_file,
                enable_auto_reload=enable_auto_reload
            )
            self.adapter = self.config_manager.adapter
        except Exception as e:
            logger.error(f"Failed to initialize configuration manager: {e}")
            # Fallback to basic adapter
            self.adapter = HardwareAdapter(specs_file)
            self.config_manager = None
        
        # Initialize monitoring and error handling
        self.monitor = get_system_monitor() if enable_monitoring else None
        self.error_handler = HardwareErrorHandler(self.monitor)
        
        # Cache for recommendations
        self._recommendation_cache: Dict[str, List[HardwareRecommendation]] = {}
        
        # Set up fallback configurations
        if self.monitor:
            self.monitor.set_fallback_config("hardware_service", {
                "fallback_gpu": {
                    "name": "Fallback GPU",
                    "memory_size_gb": 8,
                    "memory_bandwidth_gbps": 500,
                    "tensor_performance": {"fp16_tensor": 100},
                    "vector_performance": {"fp32": 50}
                }
            })
    
    def get_available_hardware(self) -> Dict[str, List[HardwareSpec]]:
        """Get all available hardware grouped by type.
        
        Returns:
            Dictionary with 'gpu' and 'npu' keys containing lists of HardwareSpec objects.
        """
        start_time = time.time()
        
        try:
            if self.config_manager:
                result = self.config_manager.get_available_hardware()
            else:
                result = self.adapter.get_available_hardware()
            
            # Record performance metrics
            duration_ms = (time.time() - start_time) * 1000
            if self.error_handler:
                self.error_handler.log_performance_warning("get_available_hardware", duration_ms)
            
            return result
            
        except Exception as e:
            if self.monitor:
                self.monitor.record_error(
                    "hardware_service",
                    ErrorSeverity.HIGH,
                    f"Failed to get available hardware: {e}",
                    e
                )
            
            # Try fallback configuration
            fallback_config = self.monitor.get_fallback_config("hardware_service") if self.monitor else None
            if fallback_config:
                logger.warning("Using fallback hardware configuration")
                return {"gpu": [self._create_fallback_spec(fallback_config["fallback_gpu"])], "npu": []}
            
            logger.error(f"Failed to get available hardware: {e}")
            return {"gpu": [], "npu": []}
    
    def get_hardware_specs(self, hardware_id: str) -> Optional[HardwareSpec]:
        """Get specifications for a specific hardware device.
        
        Args:
            hardware_id: Unique identifier for the hardware device.
            
        Returns:
            HardwareSpec object if found, None otherwise.
        """
        try:
            if self.config_manager:
                return self.config_manager.get_hardware_specs(hardware_id)
            else:
                return self.adapter.get_hardware_specs(hardware_id)
                
        except Exception as e:
            if self.monitor:
                self.monitor.record_error(
                    "hardware_service",
                    ErrorSeverity.MEDIUM,
                    f"Failed to get hardware specs for {hardware_id}: {e}",
                    e,
                    context={"hardware_id": hardware_id}
                )
            
            logger.error(f"Failed to get hardware specs for {hardware_id}: {e}")
            return None
    
    def validate_hardware_compatibility(self, hardware_id: str, operators: List[BaseOperator]) -> ValidationResult:
        """Validate hardware compatibility with given operators.
        
        Args:
            hardware_id: Hardware device identifier.
            operators: List of operators to validate against.
            
        Returns:
            ValidationResult with compatibility information.
        """
        try:
            result = self.adapter.validate_hardware_compatibility(hardware_id, operators)
            
            # Enhanced error handling with fallback recommendations
            if self.error_handler:
                result = self.error_handler.handle_hardware_validation_error(hardware_id, result)
            
            return result
            
        except Exception as e:
            if self.monitor:
                self.monitor.record_error(
                    "hardware_validation",
                    ErrorSeverity.HIGH,
                    f"Failed to validate hardware compatibility: {e}",
                    e,
                    context={"hardware_id": hardware_id, "operator_count": len(operators)}
                )
            
            logger.error(f"Failed to validate hardware compatibility: {e}")
            result = ValidationResult(is_valid=False)
            result.add_error(f"Validation failed: {str(e)}")
            
            # Add fallback recommendations
            if self.error_handler:
                result = self.error_handler.handle_hardware_validation_error(hardware_id, result)
            
            return result
    
    def get_hardware_recommendations(self, workload_profile: WorkloadProfile) -> List[HardwareRecommendation]:
        """Get hardware recommendations based on workload profile.
        
        Args:
            workload_profile: Workload characteristics for recommendation.
            
        Returns:
            List of hardware recommendations sorted by compatibility score.
        """
        cache_key = self._get_workload_cache_key(workload_profile)
        if cache_key in self._recommendation_cache:
            return self._recommendation_cache[cache_key]
        
        recommendations = []
        all_hardware = self.get_available_hardware()
        
        # Evaluate each hardware device
        for hardware_type, hardware_list in all_hardware.items():
            for hardware in hardware_list:
                recommendation = self._evaluate_hardware_for_workload(hardware, workload_profile)
                if recommendation:
                    recommendations.append(recommendation)
        
        # Sort by score (descending)
        recommendations.sort(key=lambda x: x.score, reverse=True)
        
        # Cache results
        self._recommendation_cache[cache_key] = recommendations
        
        return recommendations
    
    def get_hardware_by_type(self, hardware_type: HardwareType) -> List[HardwareSpec]:
        """Get all hardware of a specific type.
        
        Args:
            hardware_type: Type of hardware to retrieve.
            
        Returns:
            List of HardwareSpec objects of the specified type.
        """
        try:
            return self.adapter.get_hardware_by_type(hardware_type)
        except Exception as e:
            logger.error(f"Failed to get hardware by type {hardware_type}: {e}")
            return []
    
    def reload_hardware_specifications(self):
        """Reload hardware specifications from file."""
        start_time = time.time()
        
        try:
            if self.config_manager:
                self.config_manager.reload_configuration()
            else:
                self.adapter.reload_specifications()
            
            self._recommendation_cache.clear()
            
            duration_ms = (time.time() - start_time) * 1000
            if self.error_handler:
                self.error_handler.log_performance_warning("reload_specifications", duration_ms, 2000.0)
            
            logger.info("Hardware specifications reloaded successfully")
            
        except Exception as e:
            if self.monitor:
                self.monitor.record_error(
                    "config_reload",
                    ErrorSeverity.HIGH,
                    f"Failed to reload hardware specifications: {e}",
                    e
                )
            
            logger.error(f"Failed to reload hardware specifications: {e}")
            raise
    
    def validate_hardware_configuration(self, hardware_configs: Dict[str, any]) -> ValidationResult:
        """Validate hardware configuration data.
        
        Args:
            hardware_configs: Dictionary of hardware configuration data.
            
        Returns:
            ValidationResult with configuration validation results.
        """
        result = ValidationResult(is_valid=True)
        
        required_fields = ['name', 'memory_size_gb', 'memory_bandwidth_gbps']
        
        for hardware_id, config in hardware_configs.items():
            # Check required fields
            for field in required_fields:
                if field not in config:
                    result.add_error(f"Hardware {hardware_id} missing required field: {field}")
            
            # Validate numeric fields
            if 'memory_size_gb' in config:
                try:
                    memory_size = float(config['memory_size_gb'])
                    if memory_size <= 0:
                        result.add_error(f"Hardware {hardware_id} has invalid memory size: {memory_size}")
                except (ValueError, TypeError):
                    result.add_error(f"Hardware {hardware_id} has non-numeric memory size")
            
            if 'memory_bandwidth_gbps' in config:
                try:
                    bandwidth = float(config['memory_bandwidth_gbps'])
                    if bandwidth <= 0:
                        result.add_error(f"Hardware {hardware_id} has invalid memory bandwidth: {bandwidth}")
                except (ValueError, TypeError):
                    result.add_error(f"Hardware {hardware_id} has non-numeric memory bandwidth")
            
            # Validate performance data
            if 'tensor_performance' in config:
                perf_data = config['tensor_performance']
                if not isinstance(perf_data, dict):
                    result.add_error(f"Hardware {hardware_id} has invalid tensor performance data")
                else:
                    for precision, value in perf_data.items():
                        if value is not None:
                            try:
                                perf_value = float(value)
                                if perf_value < 0:
                                    result.add_warning(f"Hardware {hardware_id} has negative performance for {precision}")
                            except (ValueError, TypeError):
                                result.add_error(f"Hardware {hardware_id} has non-numeric performance for {precision}")
        
        return result
    
    def _get_workload_cache_key(self, workload_profile: WorkloadProfile) -> str:
        """Generate cache key for workload profile."""
        return f"{workload_profile.model_type}_{workload_profile.batch_size}_{workload_profile.sequence_length}_{'-'.join(workload_profile.precision_requirements)}"
    
    def _evaluate_hardware_for_workload(self, hardware: HardwareSpec, workload: WorkloadProfile) -> Optional[HardwareRecommendation]:
        """Evaluate hardware compatibility with workload profile."""
        score = 0.0
        reasons = []
        
        # Memory compatibility (40% of score)
        memory_score = self._evaluate_memory_compatibility(hardware, workload)
        score += memory_score * 0.4
        if memory_score > 0.8:
            reasons.append("Excellent memory capacity")
        elif memory_score > 0.6:
            reasons.append("Good memory capacity")
        elif memory_score < 0.3:
            reasons.append("Limited memory capacity")
        
        # Precision support (30% of score)
        precision_score = self._evaluate_precision_support(hardware, workload)
        score += precision_score * 0.3
        if precision_score > 0.8:
            reasons.append("Excellent precision support")
        elif precision_score < 0.5:
            reasons.append("Limited precision support")
        
        # Performance characteristics (30% of score)
        performance_score = self._evaluate_performance_characteristics(hardware, workload)
        score += performance_score * 0.3
        if performance_score > 0.8:
            reasons.append("High performance capability")
        elif performance_score < 0.5:
            reasons.append("Moderate performance capability")
        
        # Don't recommend hardware with very low scores
        if score < 0.2:
            return None
        
        return HardwareRecommendation(
            hardware_id=hardware.id,
            hardware_name=hardware.name,
            score=min(score * 100, 100.0),  # Convert to 0-100 scale
            reasons=reasons,
            estimated_performance=self._estimate_workload_performance(hardware, workload),
            memory_utilization=self._estimate_memory_utilization(hardware, workload)
        )
    
    def _evaluate_memory_compatibility(self, hardware: HardwareSpec, workload: WorkloadProfile) -> float:
        """Evaluate memory compatibility score (0-1)."""
        if workload.memory_constraints:
            if hardware.memory_size_gb < workload.memory_constraints:
                return 0.0
            elif hardware.memory_size_gb >= workload.memory_constraints * 2:
                return 1.0
            else:
                return (hardware.memory_size_gb - workload.memory_constraints) / workload.memory_constraints
        
        # Default memory requirements based on model type
        if workload.model_type == "moe":
            required_memory = workload.batch_size * workload.sequence_length * 0.001  # Rough estimate
        else:
            required_memory = workload.batch_size * workload.sequence_length * 0.0005  # Rough estimate
        
        if hardware.memory_size_gb < required_memory:
            return 0.0
        elif hardware.memory_size_gb >= required_memory * 3:
            return 1.0
        else:
            return min((hardware.memory_size_gb / required_memory) / 3.0, 1.0)
    
    def _evaluate_precision_support(self, hardware: HardwareSpec, workload: WorkloadProfile) -> float:
        """Evaluate precision support score (0-1)."""
        if not workload.precision_requirements:
            return 1.0
        
        supported_count = sum(
            1 for precision in workload.precision_requirements 
            if hardware.supports_precision(precision)
        )
        
        return supported_count / len(workload.precision_requirements)
    
    def _evaluate_performance_characteristics(self, hardware: HardwareSpec, workload: WorkloadProfile) -> float:
        """Evaluate performance characteristics score (0-1)."""
        # Prefer GPUs for dense models, both GPU/NPU for MoE
        if workload.model_type == "dense" and hardware.type == HardwareType.GPU:
            base_score = 0.8
        elif workload.model_type == "moe":
            base_score = 0.7
        else:
            base_score = 0.6
        
        # Bonus for tensor core capability
        if hardware.is_tensor_core_capable():
            base_score += 0.2
        
        return min(base_score, 1.0)
    
    def _estimate_workload_performance(self, hardware: HardwareSpec, workload: WorkloadProfile) -> Optional[float]:
        """Estimate workload performance in TFLOPS."""
        if not workload.precision_requirements:
            return None
        
        # Use the best supported precision
        best_performance = 0.0
        for precision in workload.precision_requirements:
            if hardware.supports_precision(precision):
                perf = hardware.get_peak_flops(precision)
                best_performance = max(best_performance, perf)
        
        return best_performance if best_performance > 0 else None
    
    def _estimate_memory_utilization(self, hardware: HardwareSpec, workload: WorkloadProfile) -> Optional[float]:
        """Estimate memory utilization percentage."""
        if workload.memory_constraints:
            return min((workload.memory_constraints / hardware.memory_size_gb) * 100, 100.0)
        
        # Rough estimation based on workload
        estimated_usage = workload.batch_size * workload.sequence_length * 0.001  # GB
        return min((estimated_usage / hardware.memory_size_gb) * 100, 100.0)
    
    def _create_fallback_spec(self, fallback_data: Dict[str, Any]) -> HardwareSpec:
        """Create a HardwareSpec from fallback data."""
        return HardwareSpec(
            id="fallback_gpu",
            name=fallback_data.get("name", "Fallback GPU"),
            type=HardwareType.GPU,
            memory_size_gb=fallback_data.get("memory_size_gb", 8),
            memory_bandwidth_gbps=fallback_data.get("memory_bandwidth_gbps", 500),
            tensor_performance=fallback_data.get("tensor_performance", {}),
            vector_performance=fallback_data.get("vector_performance", {})
        )
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get system health information.
        
        Returns:
            Dictionary with system health status
        """
        if self.monitor:
            return self.monitor.get_system_health()
        else:
            return {
                "overall_status": "unknown",
                "message": "Monitoring not enabled",
                "timestamp": time.time()
            }
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get configuration management summary.
        
        Returns:
            Dictionary with configuration information
        """
        if self.config_manager:
            return self.config_manager.get_configuration_summary()
        else:
            return {
                "config_manager": "disabled",
                "auto_reload": False,
                "custom_profiles": 0
            }
    
    def validate_all_configurations(self) -> ValidationResult:
        """Validate all loaded hardware configurations.
        
        Returns:
            ValidationResult with overall configuration validation
        """
        if self.config_manager:
            return self.config_manager.validate_all_configurations()
        else:
            result = ValidationResult(is_valid=True)
            result.add_warning("Configuration manager not available")
            return result
    
    def list_custom_profiles(self) -> List[Dict[str, Any]]:
        """List all custom hardware profiles.
        
        Returns:
            List of custom profile information
        """
        if self.config_manager:
            return self.config_manager.list_custom_profiles()
        else:
            return []
    
    def save_custom_profile(self, profile_name: str, profile_data: Dict[str, Any], 
                          format: str = "yaml") -> ValidationResult:
        """Save a custom hardware profile.
        
        Args:
            profile_name: Name for the profile
            profile_data: Hardware configuration data
            format: File format ('yaml' or 'json')
            
        Returns:
            ValidationResult indicating success/failure
        """
        if self.config_manager:
            return self.config_manager.save_custom_profile(profile_name, profile_data, format)
        else:
            result = ValidationResult(is_valid=False)
            result.add_error("Configuration manager not available")
            return result
    
    def delete_custom_profile(self, profile_name: str) -> ValidationResult:
        """Delete a custom hardware profile.
        
        Args:
            profile_name: Name of the profile to delete
            
        Returns:
            ValidationResult indicating success/failure
        """
        if self.config_manager:
            return self.config_manager.delete_custom_profile(profile_name)
        else:
            result = ValidationResult(is_valid=False)
            result.add_error("Configuration manager not available")
            return result