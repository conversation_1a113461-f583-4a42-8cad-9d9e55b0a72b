/* Custom styles for LLM Modeling Metrics Dashboard */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Global Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* Model Selection */
.select2-container--bootstrap-5 .select2-selection {
    min-height: calc(2.25rem + 2px);
}

.select2-container--bootstrap-5 .select2-selection--multiple {
    min-height: calc(2.25rem + 2px);
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
    color: rgba(255, 255, 255, 0.8);
    margin-right: 0.25rem;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: white;
}

/* Form Controls */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Progress Bar */
.progress {
    height: 0.5rem;
    border-radius: var(--border-radius);
}

.progress-bar {
    border-radius: var(--border-radius);
    transition: width 0.3s ease;
}

/* Validation Styles */
.validation-success {
    color: var(--success-color);
    font-size: 0.875rem;
}

.validation-error {
    color: var(--danger-color);
    font-size: 0.875rem;
}

.validation-warning {
    color: var(--warning-color);
    font-size: 0.875rem;
}

/* Model Cards */
.model-card {
    transition: transform 0.2s ease-in-out;
}

.model-card:hover {
    transform: translateY(-2px);
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Comparison Table */
.comparison-table {
    font-size: 0.9rem;
}

.comparison-table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
    user-select: none;
}

.comparison-table th:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.comparison-table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.table-secondary {
    background-color: rgba(108, 117, 125, 0.1) !important;
}

.badge-sm {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

.table-controls {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--info-color);
}

.table-controls .input-group-text {
    background-color: var(--light-color);
    border-color: #ced4da;
}

.table-controls .btn-group .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    background-color: white;
}

.comparison-table {
    margin-bottom: 0;
}

.comparison-table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0b5ed7 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.comparison-table tbody td {
    text-align: center;
    vertical-align: middle;
    padding: 0.75rem;
    border-color: rgba(0, 0, 0, 0.05);
}

.comparison-table tbody td:first-child {
    text-align: left;
    font-weight: 600;
    background-color: rgba(13, 110, 253, 0.05);
}

.comparison-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.comparison-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.08);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.progress {
    height: 3px;
    background-color: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Charts */
.chart-container {
    position: relative;
    height: 250px;
    max-height: 300px;
    margin-bottom: 1rem;
    background-color: white;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.chart-container canvas {
    max-height: 100% !important;
    max-width: 100% !important;
}

/* KV Growth Chart Specific Styles */
#kvGrowthChartContainer .chart-container {
    height: 250px;
    min-height: 200px;
    max-height: 280px;
}

#kvGrowthChartContainer .card-body {
    padding: 1rem;
}

#kvGrowthChart {
    width: 100% !important;
    height: 100% !important;
    max-height: 250px !important;
}

.chart-controls {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.chart-controls .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.chart-controls .form-select {
    font-size: 0.9rem;
}

.chart-controls .form-check-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Hardware Selector Component */
.hardware-selector-component {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--box-shadow);
}

.hardware-spec-section {
    background-color: rgba(13, 110, 253, 0.05);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.hardware-spec-section h6 {
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.spec-item {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.spec-item:last-child {
    margin-bottom: 0;
}

.spec-item strong {
    color: var(--dark-color);
    font-weight: 600;
    min-width: 120px;
    display: inline-block;
}

.performance-details {
    margin-top: 0.25rem;
    font-size: 0.85rem;
    color: var(--secondary-color);
    line-height: 1.4;
}

.precision-badges {
    margin-top: 0.25rem;
}

.precision-badges .badge {
    font-size: 0.7rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.hardware-selector-component .form-select {
    border: 2px solid #e9ecef;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.hardware-selector-component .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.hardware-selector-component .card {
    border: 1px solid rgba(13, 110, 253, 0.2);
    transition: box-shadow 0.15s ease-in-out;
}

.hardware-selector-component .card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
}

.hardware-selector-component .alert {
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.hardware-selector-component .badge {
    font-weight: 500;
}

/* Hardware selector loading state */
.hardware-selector-component .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Hardware validation feedback */
#hardwareValidationFeedback .alert {
    border: none;
    box-shadow: var(--box-shadow);
}

/* Responsive design for hardware specs */
@media (max-width: 768px) {
    .hardware-spec-section {
        padding: 0.5rem;
    }
    
    .spec-item strong {
        min-width: auto;
        display: block;
        margin-bottom: 0.25rem;
    }
    
    .performance-details {
        margin-top: 0;
    }
}

/* Roofline Visualizer Component */
.roofline-visualizer-component {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--box-shadow);
}

.roofline-controls .card {
    border: 1px solid rgba(13, 110, 253, 0.2);
    box-shadow: var(--box-shadow);
}

.roofline-controls .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0b5ed7 100%);
    color: white;
    border-bottom: none;
}

.roofline-controls .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.roofline-controls .form-select,
.roofline-controls .form-control {
    border: 1px solid #e9ecef;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.roofline-controls .form-select:focus,
.roofline-controls .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.roofline-chart-container .card {
    border: 1px solid rgba(13, 110, 253, 0.2);
    box-shadow: var(--box-shadow-lg);
}

.roofline-chart-container .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0b5ed7 100%);
    color: white;
    border-bottom: none;
}

.chart-wrapper {
    position: relative;
    background-color: #fafafa;
    border-radius: var(--border-radius);
    padding: 1rem;
}

.chart-wrapper canvas {
    background-color: white;
    border-radius: var(--border-radius);
}

.chart-status .badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
}

.roofline-info .card {
    border: 1px solid rgba(108, 117, 125, 0.2);
    box-shadow: var(--box-shadow);
}

.roofline-info .card-header {
    background-color: rgba(108, 117, 125, 0.1);
    border-bottom: 1px solid rgba(108, 117, 125, 0.2);
}

.roofline-info .badge {
    font-size: 0.7rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* Loading overlay for roofline chart */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 1000;
}

.loading-overlay .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Roofline controls responsive design */
@media (max-width: 768px) {
    .roofline-controls .row.g-3 {
        --bs-gutter-x: 1rem;
    }
    
    .roofline-controls .col-md-4 {
        margin-bottom: 1rem;
    }
    
    .chart-wrapper {
        height: 400px !important;
        padding: 0.5rem;
    }
    
    .roofline-controls .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

@media (max-width: 576px) {
    .roofline-visualizer-component {
        padding: 0.75rem;
    }
    
    .chart-wrapper {
        height: 350px !important;
        padding: 0.25rem;
    }
    
    .roofline-controls .card-body {
        padding: 0.75rem;
    }
    
    .roofline-controls .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    
    .roofline-info .card-body {
        padding: 0.75rem;
    }
}

/* Chart interaction styles */
.chart-wrapper:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
    transition: box-shadow 0.15s ease-in-out;
}

/* Precision selector styling */
.roofline-controls .select2-container--bootstrap-5 .select2-selection--multiple {
    min-height: calc(2.25rem + 2px);
    border: 1px solid #e9ecef;
}

.roofline-controls .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
}

/* Button group styling */
.roofline-controls .btn-group .btn {
    border-radius: var(--border-radius);
    margin-right: 0.25rem;
}

.roofline-controls .btn-group .btn:last-child {
    margin-right: 0;
}

/* Status indicator animations */
.chart-status .badge {
    transition: all 0.3s ease;
}

.chart-status .badge.bg-primary {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* Export Controls */
.export-controls {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--box-shadow);
    border-top: 3px solid var(--primary-color);
}

.export-controls h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.export-btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.export-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.export-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.export-btn i {
    margin-right: 0.25rem;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 1000;
}

.spinner-border-lg {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-vertical .btn {
        font-size: 0.875rem;
    }
    
    .metric-card {
        margin-bottom: 0.5rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    #kvGrowthChartContainer .chart-container {
        height: 200px;
        min-height: 180px;
        max-height: 220px;
    }
    
    #kvGrowthChart {
        max-height: 200px !important;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .metric-value {
        font-size: 1.25rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .chart-container {
        height: 180px;
        padding: 0.5rem;
    }
    
    #kvGrowthChartContainer .chart-container {
        height: 180px;
        min-height: 160px;
        max-height: 200px;
    }
    
    #kvGrowthChart {
        max-height: 180px !important;
    }
    
    #kvGrowthChartContainer .card-body {
        padding: 0.75rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-success {
    background-color: var(--success-color);
}

.status-error {
    background-color: var(--danger-color);
}

.status-warning {
    background-color: var(--warning-color);
}

.status-info {
    background-color: var(--info-color);
}

/* Tooltip Styles */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* Custom Scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--secondary-color) var(--light-color);
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
    }
    
    body {
        background-color: #1a1a1a;
        color: #f8f9fa;
    }
    
    .card {
        background-color: #2d3748;
        color: #f8f9fa;
    }
    
    .card-header {
        background-color: #4a5568;
        border-bottom-color: #2d3748;
    }
    
    .form-control,
    .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #f8f9fa;
    }
    
    .form-control:focus,
    .form-select:focus {
        background-color: #2d3748;
        border-color: var(--primary-color);
        color: #f8f9fa;
    }
    
    .table {
        color: #f8f9fa;
    }
    
    .table th {
        background-color: #4a5568;
        border-color: #2d3748;
    }
    
    .table td {
        border-color: #2d3748;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .export-controls {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .chart-container {
        height: 300px;
    }
}
/* Tim
ing Dashboard Styles */
.timing-dashboard {
    margin-bottom: 2rem;
}

.timing-table {
    font-size: 0.875rem;
}

.timing-table th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    white-space: nowrap;
}

.timing-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.15s ease-in-out;
}

.timing-table th.sortable:hover {
    background-color: #e9ecef;
}

.timing-table td {
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.timing-table .badge {
    font-size: 0.75rem;
}

.timing-summary-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.timing-summary-stats .stat-item {
    text-align: center;
    padding: 0.5rem;
}

.timing-summary-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.timing-summary-stats .stat-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Bottleneck Analysis Styles */
.bottleneck-analysis {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--warning-color);
}

.bottleneck-badge {
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bottleneck-recommendations {
    margin-top: 1rem;
}

.bottleneck-recommendations ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.bottleneck-recommendations li {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Optimization Suggestions Styles */
.optimization-suggestions {
    margin-bottom: 2rem;
}

.optimization-summary {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--info-color);
}

.suggestion-card {
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: all 0.15s ease-in-out;
}

.suggestion-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
}

.suggestion-header {
    padding: 1rem;
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.suggestion-header:hover {
    background-color: var(--light-color);
}

.suggestion-content {
    padding: 1.5rem;
    background-color: #fff;
}

.suggestion-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.suggestion-description {
    color: var(--secondary-color);
    font-size: 0.875rem;
    line-height: 1.4;
}

.suggestion-badges {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.complexity-low { background-color: var(--success-color) !important; }
.complexity-medium { background-color: var(--warning-color) !important; color: var(--dark-color) !important; }
.complexity-high { background-color: var(--danger-color) !important; }

.improvement-badge {
    background-color: var(--primary-color) !important;
    font-weight: 600;
}

.category-badge {
    background-color: var(--secondary-color) !important;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

/* Implementation Steps */
.implementation-steps {
    margin-top: 1rem;
}

.implementation-steps ol {
    padding-left: 1.5rem;
}

.implementation-steps li {
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

/* Quick Actions */
.quick-actions {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.quick-actions .btn {
    font-size: 0.875rem;
}

/* Filter Controls */
.filter-controls {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.filter-controls .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.filter-controls .form-select,
.filter-controls .form-control {
    font-size: 0.875rem;
}

/* Export and Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .timing-table {
        font-size: 0.75rem;
    }
    
    .timing-summary-stats .stat-value {
        font-size: 1.25rem;
    }
    
    .suggestion-badges {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
}

/* Loading States */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1055;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}

.border-start-info {
    border-left: 4px solid var(--info-color) !important;
}