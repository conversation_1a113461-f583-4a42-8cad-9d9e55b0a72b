/**
 * Hardware Selector Component for LLM Modeling Metrics Dashboard
 * 
 * This component provides hardware selection functionality with:
 * - Hardware dropdown with GPU and NPU categories
 * - Hardware specification display with key metrics
 * - Hardware validation feedback for selected operators
 * - Integration with existing web interface design
 */

class HardwareSelector {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/api',
            showValidation: true,
            showSpecs: true,
            onHardwareChange: null,
            ...options
        };
        
        // State
        this.availableHardware = { gpu: [], npu: [] };
        this.selectedHardware = null;
        this.validationResult = null;
        this.isLoading = false;
        
        // Initialize component
        this.init();
    }
    
    async init() {
        try {
            console.log('Initializing Hardware Selector...');
            await this.loadAvailableHardware();
            this.render();
            this.bindEvents();
            console.log('Hardware Selector initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Hardware Selector:', error);
            this.showError('Failed to initialize hardware selector. Please refresh the page.');
        }
    }
    
    async loadAvailableHardware() {
        try {
            console.log('Loading available hardware...');
            const response = await fetch(`${this.options.apiBaseUrl}/hardware/list`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.availableHardware = data;
            console.log('Available hardware loaded:', data);
        } catch (error) {
            console.error('Error loading available hardware:', error);
            throw error;
        }
    }
    
    render() {
        if (!this.container) {
            console.error(`Container with ID '${this.containerId}' not found`);
            return;
        }
        
        this.container.innerHTML = this.getHardwareSelectorHTML();
        this.populateHardwareSelect();
    }
    
    getHardwareSelectorHTML() {
        return `
            <div class="hardware-selector-component">
                <!-- Hardware Selection -->
                <div class="mb-4">
                    <label for="hardwareSelect" class="form-label">
                        <i class="fas fa-microchip me-1"></i>
                        Select Hardware Platform
                    </label>
                    <select id="hardwareSelect" class="form-select" style="width: 100%">
                        <option value="">Choose hardware platform...</option>
                    </select>
                    <div class="form-text">
                        Select GPU or NPU for hardware-aware performance analysis
                    </div>
                    <div id="hardwareValidationFeedback" class="mt-2"></div>
                </div>
                
                <!-- Hardware Specifications Display -->
                <div id="hardwareSpecsDisplay" class="mb-4" style="display: none;">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Hardware Specifications
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="hardwareSpecsContent">
                                <!-- Specs will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Hardware Validation Results -->
                <div id="hardwareValidationDisplay" class="mb-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-check-circle me-1"></i>
                                Hardware Compatibility
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="hardwareValidationContent">
                                <!-- Validation results will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div id="hardwareLoadingIndicator" class="text-center" style="display: none;">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>Loading hardware information...</span>
                </div>
            </div>
        `;
    }
    
    populateHardwareSelect() {
        const hardwareSelect = document.getElementById('hardwareSelect');
        if (!hardwareSelect) return;
        
        // Clear existing options (except placeholder)
        hardwareSelect.innerHTML = '<option value="">Choose hardware platform...</option>';
        
        // Add GPU options
        if (this.availableHardware.gpu && this.availableHardware.gpu.length > 0) {
            const gpuOptgroup = document.createElement('optgroup');
            gpuOptgroup.label = 'GPUs';
            
            this.availableHardware.gpu.forEach(gpu => {
                const option = document.createElement('option');
                option.value = gpu.id;
                option.textContent = `${gpu.name} (${gpu.memory_size_gb}GB)`;
                option.dataset.hardwareType = 'gpu';
                gpuOptgroup.appendChild(option);
            });
            
            hardwareSelect.appendChild(gpuOptgroup);
        }
        
        // Add NPU options
        if (this.availableHardware.npu && this.availableHardware.npu.length > 0) {
            const npuOptgroup = document.createElement('optgroup');
            npuOptgroup.label = 'NPUs';
            
            this.availableHardware.npu.forEach(npu => {
                const option = document.createElement('option');
                option.value = npu.id;
                option.textContent = `${npu.name} (${npu.memory_size_gb}GB)`;
                option.dataset.hardwareType = 'npu';
                npuOptgroup.appendChild(option);
            });
            
            hardwareSelect.appendChild(npuOptgroup);
        }
        
        console.log('Hardware select populated with', 
                   this.availableHardware.gpu.length, 'GPUs and', 
                   this.availableHardware.npu.length, 'NPUs');
    }
    
    bindEvents() {
        const hardwareSelect = document.getElementById('hardwareSelect');
        if (hardwareSelect) {
            hardwareSelect.addEventListener('change', (e) => {
                this.handleHardwareSelection(e.target.value);
            });
        }
    }
    
    async handleHardwareSelection(hardwareId) {
        if (!hardwareId) {
            this.selectedHardware = null;
            this.hideHardwareSpecs();
            this.hideValidationResults();
            this.notifyHardwareChange(null);
            return;
        }
        
        try {
            this.showLoading(true);
            
            // Load hardware specifications
            const hardwareSpecs = await this.loadHardwareSpecs(hardwareId);
            this.selectedHardware = hardwareSpecs;
            
            // Display specifications
            if (this.options.showSpecs) {
                this.displayHardwareSpecs(hardwareSpecs);
            }
            
            // Validate hardware compatibility if operators are available
            if (this.options.showValidation) {
                await this.validateHardwareCompatibility(hardwareId);
            }
            
            // Notify parent component
            this.notifyHardwareChange(hardwareSpecs);
            
        } catch (error) {
            console.error('Error handling hardware selection:', error);
            this.showValidationFeedback('error', `Failed to load hardware information: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }
    
    async loadHardwareSpecs(hardwareId) {
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/hardware/${hardwareId}/specs`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Error loading hardware specs:', error);
            throw error;
        }
    }
    
    displayHardwareSpecs(hardwareSpecs) {
        const specsDisplay = document.getElementById('hardwareSpecsDisplay');
        const specsContent = document.getElementById('hardwareSpecsContent');
        
        if (!specsDisplay || !specsContent) return;
        
        // Generate specifications HTML
        const specsHTML = this.generateHardwareSpecsHTML(hardwareSpecs);
        specsContent.innerHTML = specsHTML;
        
        // Show the specs display
        specsDisplay.style.display = 'block';
        
        // Add fade-in animation
        specsDisplay.classList.add('fade-in');
    }
    
    generateHardwareSpecsHTML(specs) {
        const formatNumber = (num) => {
            if (num === null || num === undefined) return 'N/A';
            return typeof num === 'number' ? num.toLocaleString() : num;
        };
        
        const formatPerformance = (perfDict) => {
            if (!perfDict || Object.keys(perfDict).length === 0) return 'N/A';
            
            return Object.entries(perfDict)
                .filter(([_, value]) => value !== null && value > 0)
                .map(([precision, value]) => `${precision.toUpperCase()}: ${value} TFLOPS`)
                .join(', ');
        };
        
        return `
            <div class="row g-3">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <div class="hardware-spec-section">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-tag me-1"></i>
                            Basic Information
                        </h6>
                        <div class="spec-item">
                            <strong>Name:</strong> ${specs.name}
                        </div>
                        <div class="spec-item">
                            <strong>Type:</strong> 
                            <span class="badge bg-${specs.type === 'gpu' ? 'success' : 'info'}">${specs.type.toUpperCase()}</span>
                        </div>
                        ${specs.architecture ? `
                            <div class="spec-item">
                                <strong>Architecture:</strong> ${specs.architecture}
                            </div>
                        ` : ''}
                        ${specs.year ? `
                            <div class="spec-item">
                                <strong>Year:</strong> ${specs.year}
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <!-- Memory Specifications -->
                <div class="col-md-6">
                    <div class="hardware-spec-section">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-memory me-1"></i>
                            Memory Specifications
                        </h6>
                        <div class="spec-item">
                            <strong>Memory Size:</strong> ${formatNumber(specs.memory_size_gb)} GB
                        </div>
                        <div class="spec-item">
                            <strong>Memory Bandwidth:</strong> ${formatNumber(specs.memory_bandwidth_gbps)} GB/s
                        </div>
                        ${specs.memory_type ? `
                            <div class="spec-item">
                                <strong>Memory Type:</strong> ${specs.memory_type}
                            </div>
                        ` : ''}
                        ${specs.l2_cache_mb ? `
                            <div class="spec-item">
                                <strong>L2 Cache:</strong> ${formatNumber(specs.l2_cache_mb)} MB
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <!-- Performance Specifications -->
                <div class="col-12">
                    <div class="hardware-spec-section">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            Performance Specifications
                        </h6>
                        
                        ${specs.tensor_performance && Object.keys(specs.tensor_performance).length > 0 ? `
                            <div class="spec-item">
                                <strong>Tensor Performance:</strong>
                                <div class="performance-details">
                                    ${formatPerformance(specs.tensor_performance)}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${specs.vector_performance && Object.keys(specs.vector_performance).length > 0 ? `
                            <div class="spec-item">
                                <strong>Vector Performance:</strong>
                                <div class="performance-details">
                                    ${formatPerformance(specs.vector_performance)}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${specs.supported_precisions && specs.supported_precisions.length > 0 ? `
                            <div class="spec-item">
                                <strong>Supported Precisions:</strong>
                                <div class="precision-badges">
                                    ${specs.supported_precisions.map(precision => 
                                        `<span class="badge bg-secondary me-1">${precision.toUpperCase()}</span>`
                                    ).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <!-- Physical Specifications -->
                ${specs.tdp_watts || specs.manufacturing_process || specs.interconnect ? `
                    <div class="col-12">
                        <div class="hardware-spec-section">
                            <h6 class="text-primary mb-2">
                                <i class="fas fa-cog me-1"></i>
                                Physical Specifications
                            </h6>
                            ${specs.tdp_watts ? `
                                <div class="spec-item">
                                    <strong>TDP:</strong> ${formatNumber(specs.tdp_watts)} W
                                </div>
                            ` : ''}
                            ${specs.manufacturing_process ? `
                                <div class="spec-item">
                                    <strong>Process:</strong> ${specs.manufacturing_process}
                                </div>
                            ` : ''}
                            ${specs.interconnect ? `
                                <div class="spec-item">
                                    <strong>Interconnect:</strong> ${specs.interconnect}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    async validateHardwareCompatibility(hardwareId) {
        // This would be called when operators are available
        // For now, we'll show a placeholder
        this.showValidationResults({
            is_valid: true,
            message: "Hardware compatibility validation will be performed when operators are selected."
        });
    }
    
    showValidationResults(validationResult) {
        const validationDisplay = document.getElementById('hardwareValidationDisplay');
        const validationContent = document.getElementById('hardwareValidationContent');
        
        if (!validationDisplay || !validationContent) return;
        
        const validationHTML = this.generateValidationHTML(validationResult);
        validationContent.innerHTML = validationHTML;
        
        validationDisplay.style.display = 'block';
        validationDisplay.classList.add('fade-in');
    }
    
    generateValidationHTML(result) {
        if (result.is_valid) {
            return `
                <div class="alert alert-success mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Compatible:</strong> ${result.message || 'Hardware is compatible with selected configuration.'}
                </div>
            `;
        } else {
            return `
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Compatibility Issues:</strong> ${result.message || 'Hardware may have compatibility issues.'}
                    ${result.errors && result.errors.length > 0 ? `
                        <ul class="mb-0 mt-2">
                            ${result.errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
            `;
        }
    }
    
    showValidationFeedback(type, message) {
        const feedbackDiv = document.getElementById('hardwareValidationFeedback');
        if (!feedbackDiv) return;
        
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const iconClass = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        }[type] || 'fa-info-circle';
        
        feedbackDiv.innerHTML = `
            <div class="alert ${alertClass} py-2 mb-0">
                <i class="fas ${iconClass} me-1"></i>
                ${message}
            </div>
        `;
    }
    
    showLoading(show) {
        const loadingIndicator = document.getElementById('hardwareLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = show ? 'block' : 'none';
        }
        this.isLoading = show;
    }
    
    hideHardwareSpecs() {
        const specsDisplay = document.getElementById('hardwareSpecsDisplay');
        if (specsDisplay) {
            specsDisplay.style.display = 'none';
        }
    }
    
    hideValidationResults() {
        const validationDisplay = document.getElementById('hardwareValidationDisplay');
        if (validationDisplay) {
            validationDisplay.style.display = 'none';
        }
        
        const feedbackDiv = document.getElementById('hardwareValidationFeedback');
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '';
        }
    }
    
    notifyHardwareChange(hardwareSpecs) {
        if (this.options.onHardwareChange && typeof this.options.onHardwareChange === 'function') {
            this.options.onHardwareChange(hardwareSpecs);
        }
        
        // Emit custom event
        const event = new CustomEvent('hardwareSelectionChange', {
            detail: { 
                hardware: hardwareSpecs,
                hardwareId: hardwareSpecs ? hardwareSpecs.id : null
            }
        });
        document.dispatchEvent(event);
    }
    
    showError(message) {
        console.error('Hardware Selector Error:', message);
        this.showValidationFeedback('error', message);
    }
    
    // Public API methods
    getSelectedHardware() {
        return this.selectedHardware;
    }
    
    setSelectedHardware(hardwareId) {
        const hardwareSelect = document.getElementById('hardwareSelect');
        if (hardwareSelect) {
            hardwareSelect.value = hardwareId;
            this.handleHardwareSelection(hardwareId);
        }
    }
    
    refresh() {
        this.loadAvailableHardware().then(() => {
            this.populateHardwareSelect();
        }).catch(error => {
            console.error('Failed to refresh hardware list:', error);
            this.showError('Failed to refresh hardware list');
        });
    }
    
    destroy() {
        // Clean up event listeners and DOM
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HardwareSelector;
}