/**
 * LLM Modeling Metrics Dashboard JavaScript Application - Minimal Version
 */

class LLMMetricsDashboard {
    constructor() {
        this.apiBaseUrl = '/api';
        this.supportedModels = {};
        this.modelPresets = {
            'dense-comparison': [
                "meta-llama/Meta-Llama-3-8B-Instruct",
                "meta-llama/Meta-Llama-3-70B-Instruct",
                "meta-llama/Llama-2-7b-hf",
                "Qwen/Qwen2-7B"
            ],
            'moe-comparison': [
                "deepseek-ai/DeepSeek-V3",
                "deepseek-ai/DeepSeek-v2-lite",
                "moonshotai/Kimi-K2-Instruct"
            ]
        };

        this.init();
    }

    async init() {
        try {
            console.log('Initializing LLM Metrics Dashboard...');
            console.log('jQuery available:', typeof $ !== 'undefined');
            console.log('Select2 available:', typeof $.fn.select2 !== 'undefined');

            await this.loadSupportedModels();
            this.initializeComponents();
            this.bindEvents();
            console.log('Dashboard initialized successfully');
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            this.showError('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    async loadSupportedModels() {
        try {
            console.log('Loading supported models...');
            const response = await fetch(`${this.apiBaseUrl}/models/supported`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.supportedModels = data;
            console.log('Supported models loaded:', data);
            
            this.populateModelSelect();
        } catch (error) {
            console.error('Error loading supported models:', error);
            // Continue with preset models only
            this.populateModelSelect();
        }
    }

    populateModelSelect() {
        console.log('populateModelSelect called');
        const modelSelect = $('#modelSelect');
        console.log('modelSelect element found:', modelSelect.length > 0);
        
        modelSelect.empty();

        // Add options from supported models if available
        if (this.supportedModels.architecture_info) {
            Object.entries(this.supportedModels.architecture_info).forEach(([arch, info]) => {
                if (info.examples && info.examples.length > 0) {
                    const optgroup = $(`<optgroup label="${arch.toUpperCase()}">`);
                    info.examples.forEach(model => {
                        optgroup.append(`<option value="${model}">${model}</option>`);
                    });
                    modelSelect.append(optgroup);
                }
            });
        } else {
            // Fallback: add preset models
            const allModels = [...this.modelPresets['dense-comparison'], ...this.modelPresets['moe-comparison']];
            allModels.forEach(model => {
                modelSelect.append(`<option value="${model}">${model}</option>`);
            });
        }

        try {
            // Initialize Select2
            modelSelect.select2({
                theme: 'bootstrap-5',
                placeholder: 'Search and select models...',
                allowClear: true,
                closeOnSelect: false,
                tags: true,
                tokenSeparators: [',', ' ']
            });
            console.log('Select2 initialized successfully');
        } catch (error) {
            console.error('Select2 initialization failed:', error);
        }
    }

    initializeComponents() {
        // Initialize parallel configuration collapse
        const enableParallel = document.getElementById('enableParallel');
        const parallelConfig = document.getElementById('parallelConfig');
        
        if (enableParallel && parallelConfig) {
            enableParallel.addEventListener('change', () => {
                if (enableParallel.checked) {
                    $(parallelConfig).collapse('show');
                } else {
                    $(parallelConfig).collapse('hide');
                }
            });
        }
    }

    bindEvents() {
        console.log('Binding events...');

        // Model selection validation
        $('#modelSelect').on('change', () => {
            this.validateModelSelection();
        });

        // Preset buttons - use event delegation for reliable binding
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-preset]')) {
                console.log('Preset button clicked via delegation:', e.target.dataset.preset);
                e.preventDefault();
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
            }
        });

        // Direct binding as backup
        const presetButtons = document.querySelectorAll('[data-preset]');
        console.log('Found preset buttons:', presetButtons.length);
        presetButtons.forEach((btn, index) => {
            console.log(`Preset button ${index}:`, btn.dataset.preset, btn.outerHTML.substring(0, 100));
            btn.addEventListener('click', (e) => {
                console.log('Preset button clicked directly:', e.target.dataset.preset);
                e.preventDefault();
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
            });
        });

        // Analysis button
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.startAnalysis();
            });
        } else {
            console.warn('Analyze button not found');
        }

        // Clear button
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearSelection();
            });
        } else {
            console.warn('Clear button not found');
        }

        console.log('Events bound successfully');
    }

    applyPreset(preset) {
        console.log('applyPreset called with:', preset);
        console.log('Available presets:', Object.keys(this.modelPresets));
        
        const models = this.modelPresets[preset];
        console.log('Models for preset:', models);
        
        if (models && models.length > 0) {
            try {
                $('#modelSelect').val(models).trigger('change');
                this.showToast(`Applied ${preset} preset with ${models.length} models`, 'success');
                console.log('Preset applied successfully');
            } catch (error) {
                console.error('Error applying preset:', error);
                this.showToast('Error applying preset', 'error');
            }
        } else {
            console.error('No models found for preset:', preset);
            this.showToast('No models found for preset: ' + preset, 'error');
        }
    }

    clearSelection() {
        $('#modelSelect').val(null).trigger('change');
        this.showToast('Selection cleared', 'info');
    }

    validateModelSelection() {
        const selectedModels = $('#modelSelect').val() || [];
        console.log('Selected models:', selectedModels);
        return selectedModels.length > 0;
    }

    async startAnalysis() {
        const selectedModels = $('#modelSelect').val() || [];
        
        if (selectedModels.length === 0) {
            this.showError('Please select at least one model to analyze.');
            return;
        }

        console.log('Starting analysis with models:', selectedModels);
        this.showToast('Analysis feature not implemented in minimal version', 'info');
    }

    showToast(message, type = 'info') {
        console.log(`Toast [${type}]: ${message}`);
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    showError(message) {
        console.error('Error:', message);
        this.showToast(message, 'error');
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing minimal dashboard...');
    
    // Test if preset buttons exist
    setTimeout(() => {
        const presetButtons = document.querySelectorAll('[data-preset]');
        console.log('Preset buttons found on DOM ready:', presetButtons.length);
        presetButtons.forEach((btn, i) => {
            console.log(`Button ${i}:`, btn.dataset.preset, btn.textContent.trim());
        });
    }, 100);
    
    window.dashboard = new LLMMetricsDashboard();
});