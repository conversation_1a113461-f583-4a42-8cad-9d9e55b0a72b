/**
 * Optimization Suggestions Component
 * Provides hardware-specific optimization recommendations and export functionality
 */

class OptimizationSuggestions {
    constructor() {
        this.suggestions = [];
        this.timingData = [];
        this.selectedHardware = null;
        this.categoryFilter = 'all';
        this.complexityFilter = 'all';
        this.sortBy = 'expected_improvement_percent';
        this.sortDirection = 'desc';
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Category filter
        $(document).on('change', '#optimizationCategoryFilter', (e) => {
            this.categoryFilter = e.target.value;
            this.renderSuggestions();
        });

        // Complexity filter
        $(document).on('change', '#optimizationComplexityFilter', (e) => {
            this.complexityFilter = e.target.value;
            this.renderSuggestions();
        });

        // Sort controls
        $(document).on('change', '#optimizationSortBy', (e) => {
            this.sortBy = e.target.value;
            this.renderSuggestions();
        });

        $(document).on('change', '#optimizationSortDirection', (e) => {
            this.sortDirection = e.target.value;
            this.renderSuggestions();
        });

        // Export functionality
        $(document).on('click', '#exportOptimizations', () => {
            this.exportOptimizations();
        });

        // Generate implementation guide
        $(document).on('click', '#generateImplementationGuide', () => {
            this.generateImplementationGuide();
        });

        // Toggle suggestion details
        $(document).on('click', '.suggestion-toggle', (e) => {
            const suggestionId = $(e.target).data('suggestion-id');
            $(`#suggestion-details-${suggestionId}`).collapse('toggle');
        });

        // Mark suggestion as implemented
        $(document).on('click', '.mark-implemented', (e) => {
            const suggestionId = $(e.target).data('suggestion-id');
            this.markAsImplemented(suggestionId);
        });
    }

    async generateOptimizationSuggestions(timingData, hardwareId) {
        try {
            this.timingData = timingData;
            this.selectedHardware = hardwareId;
            
            // Show loading state
            this.showLoadingState();

            // Generate suggestions based on timing data
            this.suggestions = await this.computeOptimizationSuggestions(timingData, hardwareId);

            // Render the suggestions interface
            this.renderInterface();

        } catch (error) {
            console.error('Error generating optimization suggestions:', error);
            this.showError('Failed to generate optimization suggestions: ' + error.message);
        }
    }

    async computeOptimizationSuggestions(timingData, hardwareId) {
        // For now, we'll generate suggestions based on the timing data
        // In a real implementation, this might call an API endpoint
        const suggestions = [];

        timingData.forEach((timing, index) => {
            // Precision optimization suggestions
            if (timing.precision_overhead_factor > 1.05) {
                suggestions.push({
                    id: `precision_${index}`,
                    operator_name: timing.operator_name,
                    suggestion_type: 'precision',
                    description: `Reduce mixed precision conversion overhead by using native precision. Current overhead: ${((timing.precision_overhead_factor - 1) * 100).toFixed(1)}%`,
                    expected_improvement_percent: (timing.precision_overhead_factor - 1) * 100,
                    implementation_complexity: 'low',
                    hardware_specific: true,
                    details: {
                        current_overhead: timing.precision_overhead_factor,
                        recommended_precision: 'fp16',
                        implementation_steps: [
                            'Review current precision configuration',
                            'Identify unnecessary precision conversions',
                            'Update model configuration to use consistent precision',
                            'Validate accuracy after changes'
                        ]
                    }
                });
            }

            // Tensor core optimization suggestions
            if (!timing.tensor_core_utilization && timing.bottleneck_type === 'compute') {
                suggestions.push({
                    id: `tensor_core_${index}`,
                    operator_name: timing.operator_name,
                    suggestion_type: 'tensor_core',
                    description: 'Enable tensor core utilization for compute-bound operations by using supported precisions (fp16, bf16, tf32)',
                    expected_improvement_percent: 30.0,
                    implementation_complexity: 'medium',
                    hardware_specific: true,
                    details: {
                        current_utilization: false,
                        supported_precisions: ['fp16', 'bf16', 'tf32'],
                        implementation_steps: [
                            'Verify hardware tensor core support',
                            'Update operator precision to fp16 or bf16',
                            'Ensure matrix dimensions are tensor core friendly',
                            'Benchmark performance improvements'
                        ]
                    }
                });
            }

            // Memory optimization suggestions
            if (timing.bottleneck_type === 'memory' && timing.operational_intensity < 1.0) {
                suggestions.push({
                    id: `memory_${index}`,
                    operator_name: timing.operator_name,
                    suggestion_type: 'memory_layout',
                    description: `Optimize memory access patterns and data layout. Current operational intensity: ${timing.operational_intensity.toFixed(2)} FLOP/Byte`,
                    expected_improvement_percent: 15.0,
                    implementation_complexity: 'high',
                    hardware_specific: true,
                    details: {
                        current_intensity: timing.operational_intensity,
                        target_intensity: 2.0,
                        implementation_steps: [
                            'Analyze memory access patterns',
                            'Implement operator fusion where possible',
                            'Optimize data layout for cache efficiency',
                            'Consider memory prefetching strategies'
                        ]
                    }
                });
            }

            // Batching optimization suggestions
            if (timing.utilization_percent < 50.0) {
                suggestions.push({
                    id: `batching_${index}`,
                    operator_name: timing.operator_name,
                    suggestion_type: 'batching',
                    description: `Increase batch size to improve hardware utilization. Current utilization: ${timing.utilization_percent.toFixed(1)}%`,
                    expected_improvement_percent: Math.min(50.0 - timing.utilization_percent, 25.0),
                    implementation_complexity: 'low',
                    hardware_specific: false,
                    details: {
                        current_utilization: timing.utilization_percent,
                        target_utilization: 80.0,
                        implementation_steps: [
                            'Analyze current batch size constraints',
                            'Gradually increase batch size while monitoring memory usage',
                            'Implement dynamic batching if applicable',
                            'Measure performance improvements'
                        ]
                    }
                });
            }

            // Add operator-specific optimizations from timing data
            if (timing.optimization_opportunities && timing.optimization_opportunities.length > 0) {
                timing.optimization_opportunities.forEach((opportunity, oppIndex) => {
                    suggestions.push({
                        id: `opportunity_${index}_${oppIndex}`,
                        operator_name: timing.operator_name,
                        suggestion_type: 'general',
                        description: opportunity,
                        expected_improvement_percent: 10.0, // Default estimate
                        implementation_complexity: 'medium',
                        hardware_specific: true,
                        details: {
                            source: 'timing_analysis',
                            implementation_steps: [
                                'Analyze the specific optimization opportunity',
                                'Research implementation approaches',
                                'Implement and test changes',
                                'Validate performance improvements'
                            ]
                        }
                    });
                });
            }
        });

        return suggestions;
    }

    renderInterface() {
        const container = $('#optimizationSuggestionsContainer');
        
        const interfaceHtml = `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            Optimization Suggestions
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-info" id="generateImplementationGuide">
                                <i class="fas fa-file-alt me-1"></i>
                                Implementation Guide
                            </button>
                            <button type="button" class="btn btn-outline-success" id="exportOptimizations">
                                <i class="fas fa-download me-1"></i>
                                Export
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            ${this.renderSummaryStats()}
                        </div>
                    </div>

                    <!-- Filter and Sort Controls -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="optimizationCategoryFilter" class="form-label form-label-sm">Category</label>
                            <select class="form-select form-select-sm" id="optimizationCategoryFilter">
                                <option value="all">All Categories</option>
                                <option value="precision">Precision</option>
                                <option value="tensor_core">Tensor Core</option>
                                <option value="memory_layout">Memory Layout</option>
                                <option value="batching">Batching</option>
                                <option value="general">General</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="optimizationComplexityFilter" class="form-label form-label-sm">Complexity</label>
                            <select class="form-select form-select-sm" id="optimizationComplexityFilter">
                                <option value="all">All Complexities</option>
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="optimizationSortBy" class="form-label form-label-sm">Sort By</label>
                            <select class="form-select form-select-sm" id="optimizationSortBy">
                                <option value="expected_improvement_percent">Expected Improvement</option>
                                <option value="implementation_complexity">Complexity</option>
                                <option value="suggestion_type">Category</option>
                                <option value="operator_name">Operator Name</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="optimizationSortDirection" class="form-label form-label-sm">Direction</label>
                            <select class="form-select form-select-sm" id="optimizationSortDirection">
                                <option value="desc">Descending</option>
                                <option value="asc">Ascending</option>
                            </select>
                        </div>
                    </div>

                    <!-- Suggestions List -->
                    <div id="suggestionsList">
                        ${this.renderSuggestions()}
                    </div>
                </div>
            </div>
        `;

        container.html(interfaceHtml);
    }

    renderSummaryStats() {
        const totalSuggestions = this.suggestions.length;
        const avgImprovement = totalSuggestions > 0 
            ? (this.suggestions.reduce((sum, s) => sum + s.expected_improvement_percent, 0) / totalSuggestions).toFixed(1)
            : 0;

        const complexityCounts = {
            low: this.suggestions.filter(s => s.implementation_complexity === 'low').length,
            medium: this.suggestions.filter(s => s.implementation_complexity === 'medium').length,
            high: this.suggestions.filter(s => s.implementation_complexity === 'high').length
        };

        const categoryCounts = {};
        this.suggestions.forEach(s => {
            categoryCounts[s.suggestion_type] = (categoryCounts[s.suggestion_type] || 0) + 1;
        });

        return `
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-chart-pie me-2"></i>
                        Optimization Overview
                    </h6>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-primary mb-0">${totalSuggestions}</div>
                                <small class="text-muted">Total Suggestions</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-success mb-0">${avgImprovement}%</div>
                                <small class="text-muted">Avg Improvement</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-success mb-0">${complexityCounts.low}</div>
                                <small class="text-muted">Low Complexity</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-warning mb-0">${complexityCounts.medium}</div>
                                <small class="text-muted">Medium Complexity</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-danger mb-0">${complexityCounts.high}</div>
                                <small class="text-muted">High Complexity</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-info mb-0">${Object.keys(categoryCounts).length}</div>
                                <small class="text-muted">Categories</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderSuggestions() {
        const filteredSuggestions = this.getFilteredSuggestions();
        const sortedSuggestions = this.getSortedSuggestions(filteredSuggestions);

        if (sortedSuggestions.length === 0) {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-search fa-2x text-muted mb-3"></i>
                    <h6 class="text-muted">No suggestions match the current filters</h6>
                    <p class="text-muted">Try adjusting the category or complexity filters</p>
                </div>
            `;
        }

        return `
            <div class="accordion" id="suggestionsAccordion">
                ${sortedSuggestions.map((suggestion, index) => this.renderSuggestionCard(suggestion, index)).join('')}
            </div>
        `;
    }

    renderSuggestionCard(suggestion, index) {
        const complexityColor = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger'
        };

        const categoryIcon = {
            'precision': 'fas fa-calculator',
            'tensor_core': 'fas fa-microchip',
            'memory_layout': 'fas fa-memory',
            'batching': 'fas fa-layer-group',
            'general': 'fas fa-cog'
        };

        const color = complexityColor[suggestion.implementation_complexity] || 'secondary';
        const icon = categoryIcon[suggestion.suggestion_type] || 'fas fa-lightbulb';

        return `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading${index}">
                    <button class="accordion-button collapsed" type="button" 
                            data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                            <div class="d-flex align-items-center">
                                <i class="${icon} me-2 text-primary"></i>
                                <div>
                                    <strong>${suggestion.operator_name}</strong>
                                    <br>
                                    <small class="text-muted">${suggestion.description}</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-${color} me-2">
                                    ${suggestion.implementation_complexity.toUpperCase()}
                                </span>
                                <span class="badge bg-primary me-2">
                                    +${suggestion.expected_improvement_percent.toFixed(1)}%
                                </span>
                                <span class="badge bg-secondary">
                                    ${suggestion.suggestion_type.replace('_', ' ').toUpperCase()}
                                </span>
                            </div>
                        </div>
                    </button>
                </h2>
                <div id="collapse${index}" class="accordion-collapse collapse" 
                     data-bs-parent="#suggestionsAccordion">
                    <div class="accordion-body">
                        ${this.renderSuggestionDetails(suggestion)}
                    </div>
                </div>
            </div>
        `;
    }

    renderSuggestionDetails(suggestion) {
        const details = suggestion.details || {};
        
        return `
            <div class="row">
                <div class="col-md-8">
                    <h6>Implementation Steps:</h6>
                    <ol>
                        ${(details.implementation_steps || []).map(step => `<li>${step}</li>`).join('')}
                    </ol>
                    
                    ${details.current_overhead ? `
                        <div class="mt-3">
                            <strong>Current Overhead:</strong> ${((details.current_overhead - 1) * 100).toFixed(1)}%
                        </div>
                    ` : ''}
                    
                    ${details.current_utilization ? `
                        <div class="mt-2">
                            <strong>Current Utilization:</strong> ${details.current_utilization.toFixed(1)}%
                        </div>
                    ` : ''}
                    
                    ${details.current_intensity ? `
                        <div class="mt-2">
                            <strong>Current Operational Intensity:</strong> ${details.current_intensity.toFixed(2)} FLOP/Byte
                        </div>
                    ` : ''}
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Quick Actions</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-sm btn-outline-success mark-implemented" 
                                        data-suggestion-id="${suggestion.id}">
                                    <i class="fas fa-check me-1"></i>
                                    Mark as Implemented
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info copy-suggestion" 
                                        data-suggestion-id="${suggestion.id}">
                                    <i class="fas fa-copy me-1"></i>
                                    Copy Details
                                </button>
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <strong>Hardware Specific:</strong> 
                                    ${suggestion.hardware_specific ? 'Yes' : 'No'}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getFilteredSuggestions() {
        return this.suggestions.filter(suggestion => {
            const matchesCategory = this.categoryFilter === 'all' || 
                suggestion.suggestion_type === this.categoryFilter;
            
            const matchesComplexity = this.complexityFilter === 'all' || 
                suggestion.implementation_complexity === this.complexityFilter;
            
            return matchesCategory && matchesComplexity;
        });
    }

    getSortedSuggestions(suggestions) {
        return [...suggestions].sort((a, b) => {
            let aVal = a[this.sortBy];
            let bVal = b[this.sortBy];

            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            let comparison = 0;
            if (aVal < bVal) comparison = -1;
            if (aVal > bVal) comparison = 1;

            return this.sortDirection === 'asc' ? comparison : -comparison;
        });
    }

    markAsImplemented(suggestionId) {
        const suggestion = this.suggestions.find(s => s.id === suggestionId);
        if (suggestion) {
            suggestion.implemented = true;
            suggestion.implemented_date = new Date().toISOString();
            
            // Update the UI
            $(`.mark-implemented[data-suggestion-id="${suggestionId}"]`)
                .removeClass('btn-outline-success')
                .addClass('btn-success')
                .html('<i class="fas fa-check me-1"></i> Implemented')
                .prop('disabled', true);
            
            // Show success message
            this.showToast('Suggestion marked as implemented', 'success');
        }
    }

    exportOptimizations() {
        if (this.suggestions.length === 0) {
            alert('No optimization suggestions to export');
            return;
        }

        const exportData = {
            hardware_id: this.selectedHardware,
            timestamp: new Date().toISOString(),
            summary: {
                total_suggestions: this.suggestions.length,
                average_improvement: this.suggestions.reduce((sum, s) => sum + s.expected_improvement_percent, 0) / this.suggestions.length,
                complexity_breakdown: {
                    low: this.suggestions.filter(s => s.implementation_complexity === 'low').length,
                    medium: this.suggestions.filter(s => s.implementation_complexity === 'medium').length,
                    high: this.suggestions.filter(s => s.implementation_complexity === 'high').length
                }
            },
            suggestions: this.suggestions,
            timing_data: this.timingData
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `optimization_suggestions_${this.selectedHardware}_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    generateImplementationGuide() {
        const filteredSuggestions = this.getFilteredSuggestions();
        const sortedByComplexity = [...filteredSuggestions].sort((a, b) => {
            const complexityOrder = { 'low': 0, 'medium': 1, 'high': 2 };
            return complexityOrder[a.implementation_complexity] - complexityOrder[b.implementation_complexity];
        });

        let guide = `# Optimization Implementation Guide\n\n`;
        guide += `Generated: ${new Date().toLocaleString()}\n`;
        guide += `Hardware: ${this.selectedHardware}\n`;
        guide += `Total Suggestions: ${filteredSuggestions.length}\n\n`;

        guide += `## Implementation Priority\n\n`;
        guide += `We recommend implementing optimizations in the following order:\n`;
        guide += `1. **Low Complexity** - Quick wins with minimal risk\n`;
        guide += `2. **Medium Complexity** - Moderate effort with good returns\n`;
        guide += `3. **High Complexity** - Significant effort but potentially high impact\n\n`;

        ['low', 'medium', 'high'].forEach(complexity => {
            const suggestions = sortedByComplexity.filter(s => s.implementation_complexity === complexity);
            if (suggestions.length === 0) return;

            guide += `## ${complexity.toUpperCase()} Complexity Optimizations\n\n`;
            
            suggestions.forEach((suggestion, index) => {
                guide += `### ${index + 1}. ${suggestion.operator_name} - ${suggestion.suggestion_type.replace('_', ' ').toUpperCase()}\n\n`;
                guide += `**Expected Improvement:** ${suggestion.expected_improvement_percent.toFixed(1)}%\n\n`;
                guide += `**Description:** ${suggestion.description}\n\n`;
                
                if (suggestion.details && suggestion.details.implementation_steps) {
                    guide += `**Implementation Steps:**\n`;
                    suggestion.details.implementation_steps.forEach((step, stepIndex) => {
                        guide += `${stepIndex + 1}. ${step}\n`;
                    });
                    guide += `\n`;
                }
                
                guide += `---\n\n`;
            });
        });

        // Create and download the guide
        const blob = new Blob([guide], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `implementation_guide_${this.selectedHardware}_${new Date().toISOString().split('T')[0]}.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showToast('Implementation guide generated and downloaded', 'success');
    }

    showLoadingState() {
        const container = $('#optimizationSuggestionsContainer');
        container.html(`
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Generating Optimization Suggestions...</h5>
                    <p class="text-muted">Analyzing timing data for optimization opportunities</p>
                </div>
            </div>
        `);
    }

    showError(message) {
        const container = $('#optimizationSuggestionsContainer');
        container.html(`
            <div class="card shadow-sm border-danger">
                <div class="card-body text-center py-5">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error</h5>
                    <p class="text-muted">${message}</p>
                </div>
            </div>
        `);
    }

    showToast(message, type = 'info') {
        // Simple toast notification
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);
        
        $('body').append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();
        
        // Remove from DOM after hiding
        toast.on('hidden.bs.toast', () => toast.remove());
    }

    show() {
        $('#optimizationSuggestionsContainer').show();
    }

    hide() {
        $('#optimizationSuggestionsContainer').hide();
    }
}

// Global instance
window.optimizationSuggestions = new OptimizationSuggestions();