/**
 * Roofline Visualization Component for LLM Modeling Metrics Dashboard
 * 
 * This component provides interactive roofline visualization with:
 * - Interactive roofline plot using Chart.js
 * - Operator point plotting with hover information
 * - Precision selector for different roofline curves
 * - Zoom and pan controls for detailed analysis
 */

class RooflineVisualizer {
    constructor(containerId, options = {}) {
        console.log('RooflineVisualizer v2.0 - Chart type fix applied');
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/api',
            showControls: true,
            showLegend: true,
            enableZoom: true,
            enablePan: true,
            onOperatorClick: null,
            onPrecisionChange: null,
            ...options
        };
        
        // State
        this.chart = null;
        this.rooflineData = null;
        this.selectedHardware = [];
        this.selectedPrecisions = ['fp16'];
        this.operatorData = [];
        this.isLoading = false;
        this.useLogScale = true;
        this.enableWheelZoom = true;
        
        // Initialize component
        this.init();
    }

    getChartConfig() {
        return {
            type: 'scatter',
            data: {
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: this.useLogScale ? 'logarithmic' : 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: 'Operational Intensity (FLOP/Byte)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        min: this.useLogScale ? 0.01 : 0,
                        max: this.useLogScale ? 1000 : 100,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y: {
                        type: this.useLogScale ? 'logarithmic' : 'linear',
                        title: {
                            display: true,
                            text: 'Performance (TFLOPS)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        min: this.useLogScale ? 0.1 : 0,
                        max: this.useLogScale ? 1000 : 100,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        callbacks: {
                            title: (context) => {
                                const point = context[0];
                                if (point.dataset.datasetType === 'roofline') {
                                    return `${point.dataset.label} Roofline`;
                                } else if (point.dataset.datasetType === 'operator') {
                                    return point.dataset.operatorName || 'Operator';
                                }
                                return '';
                            },
                            label: (context) => {
                                const point = context.parsed;
                                if (context.dataset.datasetType === 'roofline') {
                                    return [
                                        `Operational Intensity: ${point.x.toFixed(3)} FLOP/Byte`,
                                        `Performance: ${point.y.toFixed(2)} TFLOPS`
                                    ];
                                } else if (context.dataset.datasetType === 'operator') {
                                    const dataset = context.dataset;
                                    return [
                                        `Operational Intensity: ${point.x.toFixed(3)} FLOP/Byte`,
                                        `Performance: ${point.y.toFixed(2)} TFLOPS`,
                                        `Utilization: ${dataset.utilization || 'N/A'}%`,
                                        `Bottleneck: ${dataset.bottleneck || 'Unknown'}`
                                    ];
                                }
                                return '';
                            }
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 11
                            },
                            filter: (legendItem, chartData) => {
                                // Only show legend for rooflines and operator categories
                                return legendItem.text !== '';
                            }
                        }
                    },
                    zoom: this.options.enableZoom ? {
                        zoom: {
                            wheel: {
                                enabled: this.enableWheelZoom,
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy',
                        },
                        pan: {
                            enabled: this.options.enablePan,
                            mode: 'xy',
                        }
                    } : undefined
                },
                interaction: {
                    intersect: false,
                    mode: 'point'
                },
                onClick: (event, elements) => {
                    if (elements.length > 0 && this.options.onOperatorClick) {
                        const element = elements[0];
                        const dataset = this.chart.data.datasets[element.datasetIndex];
                        if (dataset.datasetType === 'operator') {
                            this.options.onOperatorClick(dataset, element.index);
                        }
                    }
                }
            }
        };
    }
    
    async init() {
        try {
            console.log('Initializing Roofline Visualizer...');
            this.render();
            this.bindEvents();
            console.log('Roofline Visualizer initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Roofline Visualizer:', error);
            this.showError('Failed to initialize roofline visualizer. Please refresh the page.');
        }
    }
    
    render() {
        if (!this.container) {
            console.error(`Container with ID '${this.containerId}' not found`);
            return;
        }
        
        this.container.innerHTML = this.getRooflineVisualizerHTML();
        this.initializeChart();
    }
    
    getRooflineVisualizerHTML() {
        return `
            <div class="roofline-visualizer-component">
                <!-- Controls Panel -->
                ${this.options.showControls ? `
                    <div class="roofline-controls mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-sliders-h me-1"></i>
                                    Roofline Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Precision Selector -->
                                    <div class="col-md-4">
                                        <label for="rooflinePrecisionSelect" class="form-label">
                                            <i class="fas fa-calculator me-1"></i>
                                            Precision
                                        </label>
                                        <select id="rooflinePrecisionSelect" class="form-select" multiple>
                                            <option value="fp32">FP32</option>
                                            <option value="fp16" selected>FP16</option>
                                            <option value="bf16">BF16</option>
                                            <option value="tf32">TF32</option>
                                            <option value="fp8">FP8</option>
                                            <option value="int8">INT8</option>
                                        </select>
                                        <div class="form-text">Select precisions to display</div>
                                    </div>
                                    
                                    <!-- Operational Intensity Range -->
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-arrows-alt-h me-1"></i>
                                            Intensity Range
                                        </label>
                                        <div class="row g-1">
                                            <div class="col-6">
                                                <input type="number" class="form-control form-control-sm" 
                                                       id="rooflineMinIntensity" value="0.01" 
                                                       min="0.001" max="10" step="0.001">
                                                <div class="form-text">Min</div>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" class="form-control form-control-sm" 
                                                       id="rooflineMaxIntensity" value="1000" 
                                                       min="10" max="10000" step="10">
                                                <div class="form-text">Max</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Display Options -->
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-eye me-1"></i>
                                            Display Options
                                        </label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="showOperatorLabels" checked>
                                            <label class="form-check-label" for="showOperatorLabels">
                                                Show operator labels
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="showKneePoints" checked>
                                            <label class="form-check-label" for="showKneePoints">
                                                Show knee points
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="useLogScale" checked>
                                            <label class="form-check-label" for="useLogScale">
                                                Use log scale
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="enableWheelZoom" checked>
                                            <label class="form-check-label" for="enableWheelZoom">
                                                Enable mouse wheel zoom
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="row g-2 mt-2">
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-primary btn-sm" id="updateRooflineBtn">
                                            <i class="fas fa-sync me-1"></i>
                                            Update Plot
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="resetZoomBtn">
                                            <i class="fas fa-search-minus me-1"></i>
                                            Reset Zoom
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-info btn-sm" id="exportRooflineBtn">
                                            <i class="fas fa-download me-1"></i>
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ` : ''}
                
                <!-- Chart Container -->
                <div class="roofline-chart-container">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-line me-1"></i>
                                Roofline Model Visualization
                            </h6>
                            <div class="chart-status">
                                <span id="rooflineStatus" class="badge bg-secondary">Ready</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-wrapper" style="height: 500px; position: relative;">
                                <canvas id="rooflineChart"></canvas>
                                
                                <!-- Loading Overlay -->
                                <div id="rooflineLoadingOverlay" class="loading-overlay" style="display: none;">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary mb-2" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <div>Generating roofline data...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Chart Information Panel -->
                <div class="roofline-info mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Chart Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="rooflineInfoContent">
                                <div class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Select hardware platforms and operators to generate roofline visualization.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    initializeChart() {
        const canvas = document.getElementById('rooflineChart');
        if (!canvas) {
            console.error('Roofline chart canvas not found');
            return;
        }
        
        const ctx = canvas.getContext('2d');
        
        // Import Chart.js zoom plugin if available
        if (typeof Chart !== 'undefined' && Chart.register && typeof ChartZoom !== 'undefined') {
            Chart.register(ChartZoom.default || ChartZoom);
        }
        
        this.chart = new Chart(ctx, this.getChartConfig());
        console.log('Roofline chart initialized');
    }
    
    bindEvents() {
        // Precision selector
        const precisionSelect = document.getElementById('rooflinePrecisionSelect');
        if (precisionSelect) {
            // Initialize Select2 if available
            if (typeof $ !== 'undefined' && $.fn.select2) {
                $(precisionSelect).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select precisions...',
                    allowClear: false,
                    closeOnSelect: false
                });
            }
            
            precisionSelect.addEventListener('change', () => {
                this.selectedPrecisions = Array.from(precisionSelect.selectedOptions).map(opt => opt.value);
                this.onPrecisionChange();
            });
        }
        
        // Intensity range controls
        const minIntensity = document.getElementById('rooflineMinIntensity');
        const maxIntensity = document.getElementById('rooflineMaxIntensity');
        
        if (minIntensity) {
            minIntensity.addEventListener('change', () => {
                this.updateIntensityRange();
            });
        }
        
        if (maxIntensity) {
            maxIntensity.addEventListener('change', () => {
                this.updateIntensityRange();
            });
        }
        
        // Display option checkboxes
        const showLabels = document.getElementById('showOperatorLabels');
        const showKneePoints = document.getElementById('showKneePoints');
        const useLogScale = document.getElementById('useLogScale');
        const enableWheelZoom = document.getElementById('enableWheelZoom');

        if (showLabels) {
            showLabels.addEventListener('change', () => {
                this.updateDisplayOptions();
            });
        }

        if (showKneePoints) {
            showKneePoints.addEventListener('change', () => {
                this.updateDisplayOptions();
            });
        }

        if (useLogScale) {
            useLogScale.addEventListener('change', () => {
                this.useLogScale = useLogScale.checked;
                this.updateScaleType();
            });
        }

        if (enableWheelZoom) {
            enableWheelZoom.addEventListener('change', () => {
                this.enableWheelZoom = enableWheelZoom.checked;
                this.updateZoomSettings();
            });
        }
        
        // Action buttons
        const updateBtn = document.getElementById('updateRooflineBtn');
        const resetZoomBtn = document.getElementById('resetZoomBtn');
        const exportBtn = document.getElementById('exportRooflineBtn');
        
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.updateRooflinePlot();
            });
        }
        
        if (resetZoomBtn) {
            resetZoomBtn.addEventListener('click', () => {
                this.resetZoom();
            });
        }
        
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportChart();
            });
        }
    }
    
    onPrecisionChange() {
        if (this.options.onPrecisionChange) {
            this.options.onPrecisionChange(this.selectedPrecisions);
        }
        
        // Update chart if we have data
        if (this.rooflineData) {
            this.updateChartData();
        }
    }
    
    updateIntensityRange() {
        const minInput = document.getElementById('rooflineMinIntensity');
        const maxInput = document.getElementById('rooflineMaxIntensity');
        
        if (!minInput || !maxInput) return;
        
        const min = parseFloat(minInput.value);
        const max = parseFloat(maxInput.value);
        
        if (min >= max) {
            this.showError('Minimum intensity must be less than maximum intensity');
            return;
        }
        
        // Update chart scales
        if (this.chart) {
            this.chart.options.scales.x.min = min;
            this.chart.options.scales.x.max = max;
            this.chart.update();
        }
    }
    
    updateDisplayOptions() {
        // This would update chart display options
        if (this.chart) {
            this.updateChartData();
        }
    }

    updateScaleType() {
        if (!this.chart) return;

        // Update scale types
        const scaleType = this.useLogScale ? 'logarithmic' : 'linear';
        this.chart.options.scales.x.type = scaleType;
        this.chart.options.scales.y.type = scaleType;

        // Update scale ranges based on scale type
        if (this.useLogScale) {
            this.chart.options.scales.x.min = 0.01;
            this.chart.options.scales.x.max = 1000;
            this.chart.options.scales.y.min = 0.1;
            this.chart.options.scales.y.max = 1000;
        } else {
            this.chart.options.scales.x.min = 0;
            this.chart.options.scales.x.max = 100;
            this.chart.options.scales.y.min = 0;
            this.chart.options.scales.y.max = 100;
        }

        // Update the chart
        this.chart.update();
    }

    updateZoomSettings() {
        if (!this.chart || !this.chart.options.plugins || !this.chart.options.plugins.zoom) return;

        // Update wheel zoom setting
        this.chart.options.plugins.zoom.zoom.wheel.enabled = this.enableWheelZoom;

        // Update the chart
        this.chart.update();
    }
    
    async updateRooflinePlot() {
        if (this.selectedHardware.length === 0) {
            this.showError('Please select at least one hardware platform');
            return;
        }
        
        try {
            this.setLoading(true);
            this.setStatus('Generating roofline data...');
            
            await this.generateRooflineData();
            this.updateChartData();
            
            this.setStatus('Ready');
            this.showSuccess('Roofline plot updated successfully');
        } catch (error) {
            console.error('Failed to update roofline plot:', error);
            this.showError(`Failed to update roofline plot: ${error.message}`);
            this.setStatus('Error');
        } finally {
            this.setLoading(false);
        }
    }
    
    async generateRooflineData() {
        const requestData = {
            hardware_ids: this.selectedHardware.map(hw => hw.id),
            precisions: this.selectedPrecisions
        };
        
        const response = await fetch(`${this.options.apiBaseUrl}/roofline/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        this.rooflineData = await response.json();
        console.log('Roofline data generated:', this.rooflineData);
    }
    
    updateChartData() {
        if (!this.chart || !this.rooflineData) return;
        
        const datasets = [];
        
        // Add roofline curves
        this.addRooflineDatasets(datasets);
        
        // Add operator points
        this.addOperatorDatasets(datasets);
        
        // Update chart
        this.chart.data.datasets = datasets;
        this.chart.update();
        
        // Update info panel
        this.updateInfoPanel();
    }
    
    addRooflineDatasets(datasets) {
        if (!this.rooflineData.performance_curves) return;
        
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];
        
        let colorIndex = 0;
        
        Object.entries(this.rooflineData.performance_curves).forEach(([hardwareId, precisionCurves]) => {
            const hardware = this.selectedHardware.find(hw => hw.id === hardwareId);
            const hardwareName = hardware ? hardware.name : hardwareId;
            
            this.selectedPrecisions.forEach(precision => {
                if (precisionCurves[precision]) {
                    const color = colors[colorIndex % colors.length];
                    const curve = precisionCurves[precision];
                    
                    datasets.push({
                        label: `${hardwareName} (${precision.toUpperCase()})`,
                        data: curve.map((perf, i) => ({
                            x: this.rooflineData.operational_intensity_range[i],
                            y: perf
                        })),
                        borderColor: color,
                        backgroundColor: color + '20',
                        fill: false,
                        showLine: true,
                        pointRadius: 0,
                        pointHoverRadius: 4,
                        borderWidth: 2,
                        type: 'line',
                        datasetType: 'roofline',
                        tension: 0.1
                    });
                    
                    colorIndex++;
                }
            });
        });
    }
    
    addOperatorDatasets(datasets) {
        if (!this.operatorData || this.operatorData.length === 0) return;
        
        const operatorColors = {
            'attention': '#FF6384',
            'mlp': '#36A2EB',
            'moe': '#FFCE56',
            'embedding': '#4BC0C0',
            'other': '#9966FF'
        };
        
        // Group operators by type
        const operatorGroups = {};
        this.operatorData.forEach(op => {
            const type = this.getOperatorType(op.name);
            if (!operatorGroups[type]) {
                operatorGroups[type] = [];
            }
            operatorGroups[type].push(op);
        });
        
        // Create dataset for each operator type
        Object.entries(operatorGroups).forEach(([type, operators]) => {
            datasets.push({
                label: `${type.charAt(0).toUpperCase() + type.slice(1)} Operators`,
                data: operators.map(op => ({
                    x: op.operational_intensity,
                    y: op.achieved_performance_tflops
                })),
                backgroundColor: operatorColors[type] || operatorColors.other,
                borderColor: operatorColors[type] || operatorColors.other,
                pointRadius: 6,
                pointHoverRadius: 8,
                showLine: false,
                type: 'scatter',
                datasetType: 'operator',
                operatorData: operators
            });
        });
    }
    
    getOperatorType(operatorName) {
        const name = operatorName.toLowerCase();
        if (name.includes('attention') || name.includes('attn')) return 'attention';
        if (name.includes('mlp') || name.includes('feed_forward')) return 'mlp';
        if (name.includes('moe') || name.includes('expert')) return 'moe';
        if (name.includes('embedding') || name.includes('embed')) return 'embedding';
        return 'other';
    }
    
    updateInfoPanel() {
        const infoContent = document.getElementById('rooflineInfoContent');
        if (!infoContent) return;
        
        let infoHTML = '';
        
        if (this.selectedHardware.length > 0) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Hardware Platforms:</strong>
                    <div class="mt-1">
                        ${this.selectedHardware.map(hw => 
                            `<span class="badge bg-primary me-1">${hw.name}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        if (this.selectedPrecisions.length > 0) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Precisions:</strong>
                    <div class="mt-1">
                        ${this.selectedPrecisions.map(precision => 
                            `<span class="badge bg-secondary me-1">${precision.toUpperCase()}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        if (this.operatorData.length > 0) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Operators:</strong> ${this.operatorData.length} plotted
                </div>
            `;
        }
        
        if (this.rooflineData && this.rooflineData.knee_points) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Knee Points:</strong>
                    <div class="mt-1">
                        ${Object.entries(this.rooflineData.knee_points).map(([hwId, kneePoints]) => {
                            const hw = this.selectedHardware.find(h => h.id === hwId);
                            const hwName = hw ? hw.name : hwId;
                            return `<div class="small text-muted">${hwName}: ${Object.keys(kneePoints).join(', ')}</div>`;
                        }).join('')}
                    </div>
                </div>
            `;
        }
        
        if (!infoHTML) {
            infoHTML = `
                <div class="text-muted">
                    <i class="fas fa-lightbulb me-1"></i>
                    Select hardware platforms and operators to generate roofline visualization.
                </div>
            `;
        }
        
        infoContent.innerHTML = infoHTML;
    }
    
    resetZoom() {
        if (this.chart && this.chart.resetZoom) {
            this.chart.resetZoom();
        }
    }
    
    exportChart() {
        if (!this.chart) return;
        
        try {
            const url = this.chart.toBase64Image();
            const link = document.createElement('a');
            link.download = 'roofline-chart.png';
            link.href = url;
            link.click();
            
            this.showSuccess('Chart exported successfully');
        } catch (error) {
            console.error('Failed to export chart:', error);
            this.showError('Failed to export chart');
        }
    }
    
    setLoading(loading) {
        const overlay = document.getElementById('rooflineLoadingOverlay');
        if (overlay) {
            overlay.style.display = loading ? 'flex' : 'none';
        }
        this.isLoading = loading;
    }
    
    setStatus(status) {
        const statusElement = document.getElementById('rooflineStatus');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `badge bg-${this.getStatusColor(status)}`;
        }
    }
    
    getStatusColor(status) {
        switch (status.toLowerCase()) {
            case 'ready': return 'success';
            case 'loading': case 'generating roofline data...': return 'primary';
            case 'error': return 'danger';
            default: return 'secondary';
        }
    }
    
    showError(message) {
        console.error('Roofline Visualizer Error:', message);
        // This could integrate with the main app's toast system
        if (window.dashboard && window.dashboard.showToast) {
            window.dashboard.showToast(message, 'error');
        }
    }
    
    showSuccess(message) {
        console.log('Roofline Visualizer Success:', message);
        if (window.dashboard && window.dashboard.showToast) {
            window.dashboard.showToast(message, 'success');
        }
    }
    
    // Public API methods
    setHardware(hardwareList) {
        this.selectedHardware = Array.isArray(hardwareList) ? hardwareList : [hardwareList];
        console.log('Hardware set for roofline:', this.selectedHardware);
    }
    
    setOperators(operatorData) {
        this.operatorData = operatorData || [];
        console.log('Operators set for roofline:', this.operatorData);
        
        if (this.chart) {
            this.updateChartData();
        }
    }
    
    setPrecisions(precisions) {
        this.selectedPrecisions = precisions || ['fp16'];
        
        const precisionSelect = document.getElementById('rooflinePrecisionSelect');
        if (precisionSelect) {
            // Update select options
            Array.from(precisionSelect.options).forEach(option => {
                option.selected = this.selectedPrecisions.includes(option.value);
            });
            
            // Trigger Select2 update if available
            if (typeof $ !== 'undefined' && $.fn.select2) {
                $(precisionSelect).trigger('change');
            }
        }
    }
    
    refresh() {
        if (this.selectedHardware.length > 0) {
            this.updateRooflinePlot();
        }
    }
    
    destroy() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
        
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RooflineVisualizer;
}