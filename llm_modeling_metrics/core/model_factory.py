"""
Model factory for creating appropriate model instances based on architecture.
"""

from typing import Optional, Dict, Type, Any
from .base_model import BaseModel
from .config_manager import ConfigManager


class ModelFactory:
    """Factory class for creating model instances based on architecture."""
    
    _model_registry: Dict[str, Type[BaseModel]] = {}
    _config_manager: Optional[ConfigManager] = None
    
    @classmethod
    def set_config_manager(cls, config_manager: ConfigManager) -> None:
        """
        Set the configuration manager for fetching model configs.
        
        Args:
            config_manager: ConfigManager instance
        """
        cls._config_manager = config_manager
    
    @classmethod
    def register_model(cls, architecture: str, model_class: Type[BaseModel]) -> None:
        """
        Register a new model architecture.
        
        Args:
            architecture: Architecture name (e.g., 'llama', 'deepseek')
            model_class: Model class that extends BaseModel
            
        Raises:
            TypeError: If model_class is not a subclass of BaseModel
        """
        if not issubclass(model_class, BaseModel):
            raise TypeError(f"Model class {model_class} must be a subclass of BaseModel")
        
        cls._model_registry[architecture.lower()] = model_class
    
    @classmethod
    def create_model(cls, model_name: str, config: Optional[Any] = None) -> BaseModel:
        """
        Create appropriate model instance based on architecture.
        
        All model names are supported by detecting architecture from config/name
        and falling back to default implementations when needed.
        
        Args:
            model_name: Name of the model
            config: Model configuration object (will be fetched if None)
            
        Returns:
            BaseModel instance for the detected or fallback architecture
            
        Raises:
            ConfigurationError: If configuration cannot be fetched
            ModelNotSupportedError: Only if no fallback architecture is available
        """
        # Fetch config if not provided
        if config is None:
            if cls._config_manager is None:
                cls._config_manager = ConfigManager()
            config = cls._config_manager.fetch_config(model_name)
        
        # Determine architecture from config
        architecture = cls._detect_architecture(model_name, config)
        
        # Get model class from registry
        model_class = cls._model_registry.get(architecture.lower())
        if model_class is None:
            # Fallback to llama architecture if detected architecture is not registered
            # This ensures all models are "supported" by using a default implementation
            model_class = cls._model_registry.get('llama')
            if model_class is None:
                raise ModelNotSupportedError(
                    f"No fallback architecture available. "
                    f"Supported architectures: {list(cls._model_registry.keys())}"
                )
        
        # Create and return model instance
        return model_class(model_name, config)
    
    @classmethod
    def _detect_architecture(cls, model_name: str, config: Dict[str, Any]) -> str:
        """
        Detect model architecture from config and model name.
        
        Args:
            model_name: Name of the model
            config: Model configuration dictionary
            
        Returns:
            Detected architecture name (defaults to 'llama' if cannot be determined)
        """
        # First try to get architecture from config
        if 'num_experts_per_tok' in config:
            return 'moe'
        else:
            return 'dense'
        # if 'model_type' in config:
        #     model_type = config['model_type'].lower()
            
        #     # Map known model types to our architecture names
        #     architecture_mapping = {
        #         'llama': 'llama',
        #         'llamaforcausallm': 'llama',
        #         'deepseek': 'deepseek',
        #         'deepseek_v2': 'deepseek',
        #         'deepseek_v3': 'deepseek',
        #         'qwen': 'qwen',
        #         'qwen2': 'qwen',
        #         'qwen3': 'qwen',

        #     }
            
        #     for pattern, arch in architecture_mapping.items():
        #         if pattern in model_type:
        #             return arch
        
        # # Try to get from architectures field
        # if 'architectures' in config and config['architectures']:
        #     arch_name = config['architectures'][0].lower()
            
        #     # Common architecture patterns
        #     if 'llama' in arch_name:
        #         return 'llama'
        #     elif 'deepseek' in arch_name:
        #         return 'deepseek'
        #     elif 'qwen' in arch_name:
        #         return 'qwen'

        
        # # Fallback to model name analysis
        # model_name_lower = model_name.lower()
        # if 'llama' in model_name_lower:
        #     return 'llama'
        # elif 'deepseek' in model_name_lower:
        #     return 'deepseek'
        # elif 'qwen' in model_name_lower:
        #     return 'qwen'

        
        # # Check for MoE indicators - only classify as MoE if actually has experts
        # moe_indicators = ['num_experts', 'n_routed_experts', 'experts_per_token', 'num_local_experts']
        # for key in moe_indicators:
        #     if key in config and config.get(key, 0) > 0:
        #         # Default MoE architecture
        #         return 'moe'
        
        # # Default fallback - assume it's a llama-like dense model
        # # This allows any model name to be "supported" by defaulting to llama architecture
        # return 'llama'
    
    @classmethod
    def get_supported_architectures(cls) -> list[str]:
        """
        Get list of supported model architectures.
        
        Returns:
            List of supported architecture names
        """
        return list(cls._model_registry.keys())
    
    @classmethod
    def is_architecture_supported(cls, architecture: str) -> bool:
        """
        Check if an architecture is supported.
        
        Args:
            architecture: Architecture name to check
            
        Returns:
            True if architecture is supported, False otherwise
        """
        return architecture.lower() in cls._model_registry
    
    @classmethod
    def get_model_class(cls, architecture: str) -> Optional[Type[BaseModel]]:
        """
        Get the model class for a specific architecture.
        
        Args:
            architecture: Architecture name
            
        Returns:
            Model class or None if not found
        """
        return cls._model_registry.get(architecture.lower())
    
    @classmethod
    def unregister_model(cls, architecture: str) -> bool:
        """
        Unregister a model architecture.
        
        Args:
            architecture: Architecture name to unregister
            
        Returns:
            True if architecture was unregistered, False if not found
        """
        architecture_lower = architecture.lower()
        if architecture_lower in cls._model_registry:
            del cls._model_registry[architecture_lower]
            return True
        return False
    
    @classmethod
    def clear_registry(cls) -> None:
        """Clear all registered model architectures."""
        cls._model_registry.clear()


class ModelNotSupportedError(Exception):
    """Raised when a model architecture is not supported."""
    pass


class ConfigurationError(Exception):
    """Raised when model configuration cannot be fetched or is invalid."""
    pass