"""
FLOP calculation utilities for LLM operations.
"""

from typing import Dict, Any, Optional
from ..core.base_model import ParallelConfig


class FLOPsCalculator:
    """
    Utility class for computing FLOPs (Floating Point Operations) in LLM operations.
    
    Provides detailed FLOP calculations for attention, MLP, embedding operations,
    and their modifications under different parallel strategies.
    """
    
    @staticmethod
    def compute_attention_flops(hidden_size: int, num_heads: int, num_kv_heads: int,
                              sequence_length: int, batch_size: int = 1) -> Dict[str, int]:
        """
        Compute FLOPs for attention operations.
        
        Args:
            hidden_size: Model hidden dimension
            num_heads: Number of attention heads
            num_kv_heads: Number of key-value heads (for GQA)
            sequence_length: Input sequence length
            batch_size: Batch size
            
        Returns:
            Dictionary with FLOP breakdown for attention operations
        """
        flops = {}
        
        head_dim = hidden_size // num_heads
        
        # Q, K, V projections: batch_size * seq_len * hidden_size * (q_dim + k_dim + v_dim)
        q_proj_flops = batch_size * sequence_length * hidden_size * (num_heads * head_dim)
        k_proj_flops = batch_size * sequence_length * hidden_size * (num_kv_heads * head_dim)
        v_proj_flops = batch_size * sequence_length * hidden_size * (num_kv_heads * head_dim)
        
        flops['q_projection'] = q_proj_flops
        flops['k_projection'] = k_proj_flops
        flops['v_projection'] = v_proj_flops
        
        # Attention scores computation: Q @ K^T
        # Shape: (batch, num_heads, seq_len, head_dim) @ (batch, num_heads, head_dim, seq_len)
        # For GQA, we need to account for key/value head repetition
        effective_kv_heads = num_heads  # After repetition for GQA
        attention_scores_flops = (batch_size * num_heads * sequence_length * 
                                sequence_length * head_dim)
        flops['attention_scores'] = attention_scores_flops
        
        # Attention weights @ V
        # Shape: (batch, num_heads, seq_len, seq_len) @ (batch, num_heads, seq_len, head_dim)
        attention_output_flops = (batch_size * num_heads * sequence_length * 
                                sequence_length * head_dim)
        flops['attention_output'] = attention_output_flops
        
        # Output projection: batch_size * seq_len * (num_heads * head_dim) * hidden_size
        output_proj_flops = batch_size * sequence_length * (num_heads * head_dim) * hidden_size
        flops['output_projection'] = output_proj_flops
        
        # Total attention FLOPs
        flops['total'] = sum(flops.values())
        
        return flops
    
    @staticmethod
    def compute_mlp_flops(hidden_size: int, intermediate_size: int,
                         sequence_length: int, batch_size: int = 1,
                         use_gated_activation: bool = True) -> Dict[str, int]:
        """
        Compute FLOPs for MLP operations.
        
        Args:
            hidden_size: Model hidden dimension
            intermediate_size: MLP intermediate dimension
            sequence_length: Input sequence length
            batch_size: Batch size
            use_gated_activation: Whether to use gated activation (SwiGLU, etc.)
            
        Returns:
            Dictionary with FLOP breakdown for MLP operations
        """
        flops = {}
        
        # Up projection(s)
        if use_gated_activation:
            # Gate projection: batch_size * seq_len * hidden_size * intermediate_size
            gate_proj_flops = batch_size * sequence_length * hidden_size * intermediate_size
            flops['gate_projection'] = gate_proj_flops
            
            # Up projection: batch_size * seq_len * hidden_size * intermediate_size
            up_proj_flops = batch_size * sequence_length * hidden_size * intermediate_size
            flops['up_projection'] = up_proj_flops
            
            # Gated activation (element-wise multiplication)
            gated_activation_flops = batch_size * sequence_length * intermediate_size
            flops['gated_activation'] = gated_activation_flops
        else:
            # Single up projection
            up_proj_flops = batch_size * sequence_length * hidden_size * intermediate_size
            flops['up_projection'] = up_proj_flops
            
            # Activation function (approximated as element-wise operations)
            activation_flops = batch_size * sequence_length * intermediate_size
            flops['activation'] = activation_flops
        
        # Down projection: batch_size * seq_len * intermediate_size * hidden_size
        down_proj_flops = batch_size * sequence_length * intermediate_size * hidden_size
        flops['down_projection'] = down_proj_flops
        
        # Total MLP FLOPs
        flops['total'] = sum(flops.values())
        
        return flops
    
    @staticmethod
    def compute_embedding_flops(vocab_size: int, hidden_size: int,
                              sequence_length: int, batch_size: int = 1,
                              tie_embeddings: bool = False) -> Dict[str, int]:
        """
        Compute FLOPs for embedding operations.
        
        Args:
            vocab_size: Vocabulary size
            hidden_size: Model hidden dimension
            sequence_length: Input sequence length
            batch_size: Batch size
            tie_embeddings: Whether input and output embeddings are tied
            
        Returns:
            Dictionary with FLOP breakdown for embedding operations
        """
        flops = {}
        
        # Input embedding lookup (typically no FLOPs, just indexing)
        # But we can count it as batch_size * seq_len lookups
        flops['input_embedding'] = 0  # Lookup operations don't count as FLOPs
        
        # Output projection (language modeling head)
        # batch_size * seq_len * hidden_size * vocab_size
        lm_head_flops = batch_size * sequence_length * hidden_size * vocab_size
        flops['lm_head'] = lm_head_flops
        
        # Total embedding FLOPs
        flops['total'] = sum(flops.values())
        
        return flops
    
    @staticmethod
    def compute_layer_norm_flops(hidden_size: int, sequence_length: int,
                               batch_size: int = 1, num_layer_norms: int = 1) -> Dict[str, int]:
        """
        Compute FLOPs for layer normalization operations.
        
        Args:
            hidden_size: Model hidden dimension
            sequence_length: Input sequence length
            batch_size: Batch size
            num_layer_norms: Number of layer norm operations per layer
            
        Returns:
            Dictionary with FLOP breakdown for layer norm operations
        """
        flops = {}
        
        # Layer norm: mean, variance, normalization, scale, shift
        # Approximated as 5 operations per element
        single_ln_flops = batch_size * sequence_length * hidden_size * 5
        total_ln_flops = single_ln_flops * num_layer_norms
        
        flops['layer_norm'] = total_ln_flops
        flops['total'] = total_ln_flops
        
        return flops
    
    @staticmethod
    def compute_moe_flops(hidden_size: int, intermediate_size: int, num_experts: int,
                         experts_per_token: int, sequence_length: int, batch_size: int = 1,
                         use_gated_activation: bool = True, shared_experts: int = 0) -> Dict[str, int]:
        """
        Compute FLOPs for Mixture of Experts operations.
        
        Args:
            hidden_size: Model hidden dimension
            intermediate_size: Expert intermediate dimension
            num_experts: Total number of experts
            experts_per_token: Number of experts activated per token
            sequence_length: Input sequence length
            batch_size: Batch size
            use_gated_activation: Whether to use gated activation
            shared_experts: Number of shared experts (always active)
            
        Returns:
            Dictionary with FLOP breakdown for MoE operations
        """
        flops = {}
        
        # Router/gating network FLOPs
        # Typically: batch_size * seq_len * hidden_size * num_experts
        router_flops = batch_size * sequence_length * hidden_size * num_experts
        flops['router'] = router_flops
        
        # Expert FLOPs (only for activated experts)
        # Each token activates experts_per_token experts
        total_tokens = batch_size * sequence_length
        active_expert_computations = total_tokens * experts_per_token
        
        # FLOPs per expert computation
        if use_gated_activation:
            # Gate + up + gated activation + down
            expert_flops_per_computation = (
                hidden_size * intermediate_size +  # gate projection
                hidden_size * intermediate_size +  # up projection
                intermediate_size +               # gated activation
                intermediate_size * hidden_size   # down projection
            )
        else:
            # Up + activation + down
            expert_flops_per_computation = (
                hidden_size * intermediate_size +  # up projection
                intermediate_size +               # activation
                intermediate_size * hidden_size   # down projection
            )
        
        routed_expert_flops = active_expert_computations * expert_flops_per_computation
        flops['routed_experts'] = routed_expert_flops
        
        # Shared experts (if any) - always active for all tokens
        if shared_experts > 0:
            shared_expert_flops = (total_tokens * shared_experts * 
                                 expert_flops_per_computation)
            flops['shared_experts'] = shared_expert_flops
        
        # Total MoE FLOPs
        flops['total'] = sum(flops.values())
        
        return flops
    
    @staticmethod
    def compute_model_flops(model_config: Dict[str, Any], sequence_length: int = 2048,
                          batch_size: int = 1, include_embeddings: bool = True) -> Dict[str, int]:
        """
        Compute total FLOPs for a complete model.
        
        Args:
            model_config: Model configuration dictionary
            sequence_length: Input sequence length
            batch_size: Batch size
            include_embeddings: Whether to include embedding FLOPs
            
        Returns:
            Dictionary with complete model FLOP breakdown
        """
        flops = {}
        
        # Extract configuration parameters
        hidden_size = model_config.get('hidden_size', 0)
        num_layers = model_config.get('num_hidden_layers', 0)
        num_heads = model_config.get('num_attention_heads', 0)
        num_kv_heads = model_config.get('num_key_value_heads', num_heads)
        intermediate_size = model_config.get('intermediate_size', 0)
        vocab_size = model_config.get('vocab_size', 0)
        tie_embeddings = model_config.get('tie_word_embeddings', False)
        
        # Check if it's a MoE model
        is_moe = 'num_experts' in model_config or 'n_routed_experts' in model_config
        
        if is_moe:
            # MoE model
            num_experts = model_config.get('num_experts', model_config.get('n_routed_experts', 0))
            experts_per_token = model_config.get('num_experts_per_tok', 
                                               model_config.get('experts_per_token', 2))
            shared_experts = model_config.get('n_shared_experts', 0)
            
            # Attention FLOPs per layer
            attention_flops = FLOPsCalculator.compute_attention_flops(
                hidden_size, num_heads, num_kv_heads, sequence_length, batch_size
            )
            flops['attention_per_layer'] = attention_flops['total']
            flops['attention_total'] = attention_flops['total'] * num_layers
            
            # MoE FLOPs per layer
            moe_flops = FLOPsCalculator.compute_moe_flops(
                hidden_size, intermediate_size, num_experts, experts_per_token,
                sequence_length, batch_size, True, shared_experts
            )
            flops['moe_per_layer'] = moe_flops['total']
            flops['moe_total'] = moe_flops['total'] * num_layers
            
        else:
            # Dense model
            # Attention FLOPs per layer
            attention_flops = FLOPsCalculator.compute_attention_flops(
                hidden_size, num_heads, num_kv_heads, sequence_length, batch_size
            )
            flops['attention_per_layer'] = attention_flops['total']
            flops['attention_total'] = attention_flops['total'] * num_layers
            
            # MLP FLOPs per layer
            mlp_flops = FLOPsCalculator.compute_mlp_flops(
                hidden_size, intermediate_size, sequence_length, batch_size
            )
            flops['mlp_per_layer'] = mlp_flops['total']
            flops['mlp_total'] = mlp_flops['total'] * num_layers
        
        # Layer norm FLOPs (typically 2 per layer: pre-attention and pre-MLP)
        layer_norm_flops = FLOPsCalculator.compute_layer_norm_flops(
            hidden_size, sequence_length, batch_size, 2
        )
        flops['layer_norm_per_layer'] = layer_norm_flops['total']
        flops['layer_norm_total'] = layer_norm_flops['total'] * num_layers
        
        # Embedding FLOPs
        if include_embeddings:
            embedding_flops = FLOPsCalculator.compute_embedding_flops(
                vocab_size, hidden_size, sequence_length, batch_size, tie_embeddings
            )
            flops['embeddings'] = embedding_flops['total']
        
        # Total model FLOPs
        flops['total'] = sum(v for k, v in flops.items() if k != 'total' and 'per_layer' not in k)
        
        return flops
    
    @staticmethod
    def adjust_flops_for_parallel(flops: Dict[str, int], 
                                parallel_config: ParallelConfig) -> Dict[str, int]:
        """
        Adjust FLOP calculations for parallel execution.
        
        Note: FLOPs don't change with parallelism, but this method can be used
        to compute per-device FLOPs or add communication overhead estimates.
        
        Args:
            flops: Original FLOP calculations
            parallel_config: Parallel configuration
            
        Returns:
            Adjusted FLOP calculations
        """
        adjusted_flops = flops.copy()
        
        tp_size = parallel_config.tensor_parallel_size
        pp_size = parallel_config.pipeline_parallel_size
        dp_size = parallel_config.data_parallel_size
        
        # FLOPs per device (for tensor and pipeline parallelism)
        if tp_size > 1 or pp_size > 1:
            # Tensor parallelism doesn't reduce FLOPs per device significantly
            # Pipeline parallelism distributes layers across devices
            effective_parallel_size = max(pp_size, 1)  # PP is the main FLOP reducer
            
            for key in adjusted_flops:
                if key != 'total' and 'per_layer' not in key:
                    adjusted_flops[f'{key}_per_device'] = adjusted_flops[key] // effective_parallel_size
        
        # Communication overhead (approximated as additional FLOPs)
        if tp_size > 1:
            # Estimate communication overhead as percentage of compute FLOPs
            comm_overhead_ratio = 0.05  # 5% overhead approximation
            total_compute_flops = adjusted_flops.get('total', 0)
            comm_overhead = int(total_compute_flops * comm_overhead_ratio)
            adjusted_flops['communication_overhead'] = comm_overhead
            adjusted_flops['total_with_comm'] = total_compute_flops + comm_overhead
        
        return adjusted_flops
    
    @staticmethod
    def get_flops_breakdown_summary(flops: Dict[str, int]) -> Dict[str, Any]:
        """
        Get a summary of FLOP breakdown with percentages.
        
        Args:
            flops: FLOP calculations dictionary
            
        Returns:
            Summary with percentages and human-readable values
        """
        total_flops = flops.get('total', 0)
        if total_flops == 0:
            return {'total': 0, 'breakdown': {}}
        
        summary = {
            'total': total_flops,
            'total_human': FLOPsCalculator._format_flops(total_flops),
            'breakdown': {}
        }
        
        # Calculate percentages for major components
        major_components = ['attention_total', 'mlp_total', 'moe_total', 
                          'layer_norm_total', 'embeddings']
        
        for component in major_components:
            if component in flops:
                value = flops[component]
                percentage = (value / total_flops) * 100
                summary['breakdown'][component] = {
                    'flops': value,
                    'flops_human': FLOPsCalculator._format_flops(value),
                    'percentage': round(percentage, 2)
                }
        
        return summary
    
    @staticmethod
    def _format_flops(flops: int) -> str:
        """Format FLOP count in human-readable form."""
        if flops >= 1e12:
            return f"{flops / 1e12:.2f}T"
        elif flops >= 1e9:
            return f"{flops / 1e9:.2f}G"
        elif flops >= 1e6:
            return f"{flops / 1e6:.2f}M"
        elif flops >= 1e3:
            return f"{flops / 1e3:.2f}K"
        else:
            return str(flops)